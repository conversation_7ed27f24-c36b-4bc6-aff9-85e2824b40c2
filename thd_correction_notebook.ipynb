{"cells": [{"cell_type": "markdown", "id": "thd_correction_title", "metadata": {}, "source": ["# THD计算修正 - 按照MATLAB标准\n", "\n", "## 问题描述\n", "原有的THD计算结果与MATLAB不匹配，需要按照MATLAB官方文档标准重新实现THD计算逻辑。\n", "\n", "## 参考文档\n", "- MATLAB THD函数: https://ww2.mathworks.cn/help/releases/R2024b/sps/powersys/ref/thd.html\n", "- THD定义: THD = sqrt(sum(H2^2 + H3^2 + ... + Hn^2)) / H1 * 100%\n", "\n", "## 主要修正点\n", "1. **不使用窗函数**: MATLAB THD计算保持信号真实幅值关系\n", "2. **扩展谐波范围**: 考虑2-50次谐波（或到奈奎斯特频率）\n", "3. **精确峰值检测**: 在±0.5Hz范围内搜索谐波峰值\n", "4. **标准化幅值计算**: 使用标准的单边谱幅值公式"]}, {"cell_type": "code", "execution_count": 1, "id": "imports_cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 库导入完成\n"]}], "source": ["# 导入必要的库\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import scipy.io\n", "from pathlib import Path\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"✅ 库导入完成\")"]}, {"cell_type": "code", "execution_count": 2, "id": "matlab_thd_function", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ MATLAB标准THD计算函数定义完成\n"]}], "source": ["def extract_matlab_standard_thd(signal_data, fs, grid_freq=50, verbose=True):\n", "    \"\"\"\n", "    按照MATLAB标准计算THD\n", "    参考MATLAB官方文档的THD计算方法\n", "    \"\"\"\n", "    N = len(signal_data)\n", "    \n", "    # 确保信号长度为偶数\n", "    if N % 2 != 0:\n", "        signal_data = signal_data[:-1]\n", "        N = len(signal_data)\n", "    \n", "    # 按照MATLAB标准，不使用窗函数进行THD计算\n", "    # 因为THD计算需要保持信号的真实幅值关系\n", "    \n", "    # FFT分析 - 使用原始信号\n", "    yf = np.fft.fft(signal_data)\n", "    yf_mag = np.abs(yf[:N//2]) * 2.0 / N  # 单边谱幅值\n", "    yf_mag[0] = yf_mag[0] / 2  # 直流分量不需要乘2\n", "    freqs = np.fft.fftfreq(N, 1/fs)[:N//2]\n", "    \n", "    # 频率分辨率\n", "    freq_resolution = fs / N\n", "    \n", "    # 基波检测 - 按照MATLAB标准\n", "    # 在50Hz附近寻找最大峰值\n", "    search_range = max(3, int(5 / freq_resolution))  # ±5Hz搜索范围\n", "    \n", "    fundamental_center_idx = np.argmin(np.abs(freqs - grid_freq))\n", "    start_idx = max(1, fundamental_center_idx - search_range)  # 避免直流分量\n", "    end_idx = min(len(yf_mag), fundamental_center_idx + search_range)\n", "    \n", "    # 在搜索范围内找到最大峰值作为基波\n", "    if end_idx > start_idx:\n", "        local_peak_idx = np.argmax(yf_mag[start_idx:end_idx])\n", "        fundamental_idx = start_idx + local_peak_idx\n", "    else:\n", "        fundamental_idx = fundamental_center_idx\n", "    \n", "    # 基波特征\n", "    fundamental_mag = yf_mag[fundamental_idx]\n", "    fundamental_freq = freqs[fundamental_idx]\n", "    \n", "    if verbose:\n", "        print(f\"基波检测结果:\")\n", "        print(f\"  - 基波频率: {fundamental_freq:.2f} Hz\")\n", "        print(f\"  - 基波幅值: {fundamental_mag:.4f}\")\n", "        print(f\"  - 频率分辨率: {freq_resolution:.2f} Hz\")\n", "    \n", "    # 谐波检测 - 按照MATLAB THD标准\n", "    # MATLAB THD计算通常考虑2-50次谐波\n", "    harmonics_power_sum = 0\n", "    harmonic_details = {}\n", "    \n", "    # 扩展谐波范围，按照MATLAB标准\n", "    max_harmonic = min(50, int((fs/2) / fundamental_freq))  # 最大到50次或奈奎斯特频率\n", "    \n", "    if verbose:\n", "        print(f\"\\n谐波检测结果 (最大{max_harmonic}次):\")\n", "    \n", "    for h in range(2, max_harmonic + 1):  # 从2次谐波开始\n", "        harmonic_freq = h * fundamental_freq\n", "        \n", "        # 确保谐波频率在有效范围内\n", "        if harmonic_freq >= fs/2:\n", "            break\n", "            \n", "        # 找到最接近的频率点\n", "        harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))\n", "        \n", "        # 谐波峰值检测 - 在±0.5Hz范围内搜索\n", "        h_search_range = max(1, int(0.5 / freq_resolution))\n", "        h_start_idx = max(0, harmonic_idx - h_search_range)\n", "        h_end_idx = min(len(yf_mag), harmonic_idx + h_search_range + 1)\n", "        \n", "        if h_end_idx > h_start_idx:\n", "            # 在搜索范围内找到最大值\n", "            local_h_peak_idx = np.argmax(yf_mag[h_start_idx:h_end_idx])\n", "            actual_harmonic_idx = h_start_idx + local_h_peak_idx\n", "            harmonic_mag = yf_mag[actual_harmonic_idx]\n", "            \n", "            # 存储主要谐波信息\n", "            if h <= 19:  # 只存储前19次谐波\n", "                harmonic_details[h] = {\n", "                    'magnitude': harmonic_mag,\n", "                    'frequency': freqs[actual_harmonic_idx],\n", "                    'ratio_percent': (harmonic_mag / fundamental_mag) * 100\n", "                }\n", "                \n", "                if verbose and h <= 7:  # 显示前7次谐波的详细信息\n", "                    print(f\"  - {h}次谐波: {harmonic_mag:.4f} ({harmonic_details[h]['ratio_percent']:.2f}%)\")\n", "            \n", "            # 累加所有谐波功率（用于THD计算）\n", "            harmonics_power_sum += harmonic_mag**2\n", "    \n", "    # THD计算 - 严格按照MATLAB标准\n", "    # THD = sqrt(sum(H2^2 + H3^2 + ... + Hn^2)) / H1 * 100%\n", "    if fundamental_mag > 1e-12:  # 避免除零错误\n", "        # MATLAB THD公式\n", "        thd = np.sqrt(harmonics_power_sum) / fundamental_mag\n", "        thd_percent = thd * 100  # 百分比形式\n", "        \n", "        if verbose:\n", "            print(f\"\\nTHD计算结果:\")\n", "            print(f\"  - 基波功率: {fundamental_mag**2:.6f}\")\n", "            print(f\"  - 谐波总功率: {harmonics_power_sum:.6f}\")\n", "            print(f\"  - THD: {thd:.4f} ({thd_percent:.2f}%)\")\n", "        \n", "        return {\n", "            'thd': thd,\n", "            'thd_percent': thd_percent,\n", "            'fundamental_mag': fundamental_mag,\n", "            'fundamental_freq': fundamental_freq,\n", "            'harmonics_power': harmonics_power_sum,\n", "            'harmonic_details': harmonic_details,\n", "            'frequency_spectrum': (freqs, yf_mag),\n", "            'max_harmonic': max_harmonic\n", "        }\n", "    else:\n", "        if verbose:\n", "            print(\"基波幅值过小，无法计算THD\")\n", "        return None\n", "\n", "print(\"✅ MATLAB标准THD计算函数定义完成\")"]}, {"cell_type": "code", "execution_count": 3, "id": "test_thd_function", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 测试MATLAB标准THD计算...\n", "基波检测结果:\n", "  - 基波频率: 50.00 Hz\n", "  - 基波幅值: 0.9996\n", "  - 频率分辨率: 10.00 Hz\n", "\n", "谐波检测结果 (最大50次):\n", "  - 2次谐波: 0.0014 (0.14%)\n", "  - 3次谐波: 0.0993 (9.93%)\n", "  - 4次谐波: 0.0008 (0.08%)\n", "  - 5次谐波: 0.0493 (4.93%)\n", "  - 6次谐波: 0.0008 (0.08%)\n", "  - 7次谐波: 0.0006 (0.06%)\n", "\n", "THD计算结果:\n", "  - 基波功率: 0.999108\n", "  - 谐波总功率: 0.012289\n", "  - THD: 0.1109 (11.09%)\n", "\n", "✅ 测试完成！\n", "理论THD: 11.18%\n", "计算THD: 11.09%\n", "误差: 0.090%\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21540\\1534436041.py:90: UserWarning: Glyph 8226 (\\N{BULLET}) missing from font(s) SimHei.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21540\\1534436041.py:90: UserWarning: Glyph 9989 (\\N{WHITE HEAVY CHECK MARK}) missing from font(s) SimHei.\n", "  plt.tight_layout()\n", "d:\\miniconda3\\envs\\matlab\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 8226 (\\N{BULLET}) missing from font(s) SimHei.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "d:\\miniconda3\\envs\\matlab\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 9989 (\\N{WHITE HEAVY CHECK MARK}) missing from font(s) SimHei.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAPZCAYAAAD+1mNdAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3Qd4FNXbBfCTSug19F6kKKBCKBYQRfkrgr3yKagoiiiKomIBsYCKgl1EEBC72Av2rkgTkSZNOkIAaSmEtO85d5h1Uza7CcnO7O75Pc+QTXZ29yaTDTNn3nlvVG5ubi5ERERERERERERERKSA6IJfEhERERERERERERERUoguIiIiIiIiIiIiIuKDQnQRERERERERERERER8UoouIiIiIiIiIiIiI+KAQXURERERERERERETEB4XoIiIiIiIiIiIiIiI+KEQXEREREREREREREfFBIbqIiIiIiIiIiIiIiA8K0UVEREREJCgyMzORmprqd7309PSgjEdEREREJBAK0UVEREQkbN13333o3Lkz9u/fX+avtXnzZlx33XX49ddfzec7duzA2WefjQULFiDU/Pvvv37X4c/03nvvxeuvvx7w806aNAkNGjTAk08+6XOdZcuWoUaNGrjgggsCCtznzp2L3NxclNQnn3yCu+66y2y//LKzswN6jpdeesn8nvG56MUXX8Sff/5Z4jGJiIiIiLvEOj0AEREREZGysm3bNixatAgJCQmer82fPx/fffed+VpMTEyBxzCQzcrKQkpKignhvS1fvhxt2rQp9HHvvfeeCVPXrVuHb775BmvWrMH333+PX375xSzt2rVDKOCJgE8//dSMvVWrVj7Xy8nJwcMPP4z/+7//w+WXX+73eflzefDBB83PrnHjxub57ec5dOgQ6tevjw4dOmDMmDE4ePCg+Tl7n4BgoM2vd+nSBYmJieZrX3zxBc4880ycfvrpmDlzJurWrYuFCxdiw4YNhY6hVq1aOOWUU/J8jdvm0UcfNd9Do0aNPF+fOHEiPvjgA8yZMwcVK1Ys8nuLjY01v2f//PMPVq9ejREjRqBSpUrmuVu2bOn3ZyMiIiIi7qYQXURERETCVvny5c3HuLi4PCE6K48D4R2i//zzzyasZdg6depUREVF5Vn3zTffNB8ZAtNJJ51kQtj//e9/GDt2LN566y2EgjPOOAMvv/yyCZt/+OEHnyGw/TMtV65cQO1ZLrnkEnNiglhlnt+NN96Inj17mpMRNG7cOLPk99VXX6F3796en/GQIUMwefJkdOrUCR9++KEZ+wsvvFDoOLj98ofo8fHxeb4f265du/DTTz/h/PPPx2effVboiZPCnuOoo44y27p///4m4P/tt99Qs2ZNvz8jEREREXEvtXMRERERCQOs6mWoy+X222/Pc9+FF17ouS9/hS7DRn79mmuuKfR5GTjaj/W12BXF999/v/mcH4uDld/16tUzjy2spYb392YvDC2bN29uvlc7mPVVIUzegfdFF12EpUuXYuvWrThw4ECehS1K+PWVK1easN1bUlISTjvtNBPSDh06NM99bOHCsLRFixYmRN2+fbtZjjnmGFOdPmHCBFOlvGXLFlOR7f192j+3ohb7Z2r/LPIHwb7u834OVlMzEL/66qvxxx9/+PyZ8feFJwlYxX/qqadi/fr1ha5XVKjsjRXkrFZnpfbjjz9ufs633HKL58TDnj17TOsbhuyDBw82Fej79u0z67EdDrfhX3/9ZVrMcEwMzm38nvg7PGvWLHO7YcOGqFChAqpWrVpg27L63b4i4YYbbvAE8fbvRv6TIgzwuZ2//PLLAu8pXyG6jeO+9dZbcc4556BKlSoB/ZxERERExL1UiS4iIiISZvIHpIsXL/a5Ltt20Oeff17o/VdccYUntNy4cSNeffVVE0by67YmTZoc0XgZrjJwJlb8srK4MN6vy7YeX3/9NZ544gnTE5sV03ZgnpaWZiqC81cWs0VLRkYG6tSpYxZfKleubFqL5MeK63fffddUGLPymZ/bvb3ZDoQYkPOEgD+sxJ49e7a53aNHD9xzzz2e+9gihby/xnWOBJ+LJxvmzZuH6dOnm9YnzzzzTIGTAbZBgwaZ8PyBBx4w67G1SUlx+7AtC09CDB8+3GynE0880fzsePKB4TnxdbjtuE3t4LlPnz6mtzhD9NatW/t8DYb0PDnCbWL/HrCdCgP66tWre0Ly6GirhmjTpk3mRIk/Tz/9tDn50bdvXxPg8/eUr5H/BMLu3bvNR67Lsdo/Q/6+8T62mRERERGR0KUQXURERCTMeIfmrKz2VUnMIJq9wYkBIR933HHH5VnHu0Kdlc4M0Zs1a4aHHnqo1MbL4Nz7tq8QPf/rspc2q67ZcuPtt9/29OVmX22G2XYFNjFYZUU0q5WLqlz3hwEq243wdd944w3ccccdpqr8o48+MvcPGDDABLo23s8+6jxZYff+tvt/21jtzSV/iF6aP2Pv52J4f9lll+Hmm282/cU5IWZh2JamadOmJgw+EjwJs2rVKhNoE0Nmfv8M8xms26Ezf3YnnHCC2c7213jygNX/vtqhMKS228kU1laGJyt44oAngLwVdpKlMAzL7fYyjzzyCEaNGlXk+nfffbdZvPHkwLBhw/y+loiIiIi4l0J0ERERkTDDthestGXlNqvS2S6lMAzFWfnL0PrFF180AXb+ED0Y+LoMVdnTmhNyMmTO3x6jMKwqZkjKEJ0TONohOvteM3hlcP7888+brzHEZF/u/C07SoJBPHud792711S0s2WHje1S2APdxuCVr+n9NaexXcuPP/7oqTB//fXXff58r7rqqlLrTc+Fldpt27Yt9uNvu+020womPwb8mZmZmDFjhqk8z49V6Pwdr1atGkrj58bfUX4fDNe9f5d4IuXiiy82J53Ykob4e8yQn+19RERERCS0KUQXERERCSMMjxlEs6rcDtGJ/Z/Z/qSwCnC29HjttdfM594tRIKBkzey1QfDcAaUH3/8sWnNwiA8EAzGiX2wbfakkgsXLvSE6Hbbld9//x3XX3+96Y1tt/0oDE88sHKdz8+WHvmrnBs0aGAW/rz4Omxpk7/aOf/zsRKdgS9DXYa7pRHolxTb4jBEnzNnjudr7BO+evXqPBXaDIT79etn7uPP2O4pTvz5EHvHe/cM59f5PXKbcpJSb/Z2YiW/r8k/vfG5Bw4cmGf72hhQ8+fJFjusdOf3wp7o3rjd+NiitnVh7O1jt3/he+fYY4/1Ocnq0Ucf7Xm9bt26Feu1RERERMT9FKKLiIiIhJGOHTvi22+/NSE6K6QZoicmJprANz+GjgwL27dvj65du5rKdFax16hRI2jjZS92hsvskc0QnRjmBxKis9KXE1NSz549C9zP6nQbW7gkJyebCn1OmslqcgardpDNvtWsfmc/dDv0ZrsbBrXs0V1YqxDex77arExmuxQG02PHjjVLfnYYa9u5cydq1aoFp7DlDcfEanpOdsrwmeE3vyd+72y9wt+dM8880wTVnLjTribPH/6vXbvW/GxtfDxPPjBYzh+i22E2f9eKU5FeWAjObcLWNKNHj8aDDz5oqtLznyjiWAsL4P0566yzzGN5UoEneewe6Pyd48+MJxN4ssE+YcD3DH932BZJRERERMKPQnQRERGRMMIgr0WLFp4KdIbprKDNj+Egw09OmMiwkH2rWcH+xRdfmH7ZwWJXwzNEZ5jPYJRfmzRpUoF12dv93nvvNbdTU1PNevw+brrpJpx99tkF1mefdBurgzlZJcNbhsL5g2B+ft5553lC+UDYE416h/U8cXHuuefmaeeyZs0avPTSS+ZkgV3dzhD/SHj/LGxFVcIXNnaeQOHJAwb6DNGnTJmS5+QGA3SuxzYp/Jnlx5MMDNXZRocnJgJhn0xguM6rH/zhz5Y/z/wnIbxx8lOO49JLLy1wH3/e3tXzgbLHxisYGKLbJ1E4EWqvXr0KrM9tzBNVvuYfEBEREZHQphBdREREJMwwNGcbDAafK1aswPDhw/NUCnuH1wzP7RDb/nqwQnQGnAztGShzzKw2ZjU6g0pODMqTAd5YRW5Puuldec8K8Px4goDPY+vevbsJejkZKNuDHCm2OWH1OycR5c/w559/Nl/n9+E9ESd7dXMsV199NUpTYT+L4mLwbJ+Q8KWwCvwjwRMJ9OWXX5orJIr7OF+8J/z0Xpc92L17pfuaH8Afu0c/J1plaM+K88mTJ5tt3bx5c1OJzpY+/D3g6xcV+ouIiIhI6NHenYiIiEiYYZDLkJUTbrL9RGGV6J9++mme8JyV2mxZYbdXCYbffvvNtI9hKxm7XYc9Hnt83hhaMwS124288847por6hBNOMLe9Mehkdfmpp55qPh8/frwJje+77z5TQc31X331VXM7P1ar2z3UC8PQmZNxPvroo471Nff+WdjLd999V6znsL/3oqriS/v7y8rKMh95BUT+8Re22N+T/Thf+HvEvu3cNmxJY2PYzYp7W2HbuzgYovN36JJLLvFcecDPGaKzPQ1fn1dHiIiIiEh4USW6iIiISJg57rjjzMfp06d7Pmflr41B348//mhu9+jRo8BEn6xiD8bkiHY1PHu45w9red/NN9/s87HVqlXDhRdeaMJLTqY6bNgwXHTRRZ4WNmzHwRYvrBLm87P/+HXXXYdWrVqZkwWPPfaYmRC0bt26ZtJV24EDB0xbF37kz+b4448v8NofffSRaclSWHU52+iw+ty2fft2Ewbza/bEonzskCFDPJXgTuA42NubilMRfqTYP9w+SVKcgN5+nC88qcHKcG5POyjfv3+/aXHDkzQ2+z57otfSZJ+s4smhNm3alOpzi4iIiIizFKKLiIiIhBk7zGOlNYPao446Ks/9nHyRFeqcXLJfv36erzNUZnsVBtjBDNEZgFetWtXz9YkTJ+KHH34wwam/SSG7dOliPrJdDRe20bjgggtMZTsnm7RPJJBdXb5hwwbzvbLFi3eAbveUZx/z66+/Htdcc43ph51/Ukv2DuekkoW1keHPliGqd4U0w9o777zTfGRFNb8vtoFxMkRfvny5GUvNmjVNP3R/2AanT58+RzzpLAN7TsZq47bntl66dGme9dgW5dprr8Udd9yBq666qsjXZSD+/PPPm4p6VqMvWbLEvAe++uorc7KAv+f29rd/5vx6YX3e/Snqd9JujcSTNt4tfUREREQk9ClEFxEREQkz9evXR+3atU2ozMk6WXldWHjNyuwbb7zR83WGv3aIznYoZYm9qlm1zXE+/fTTeaqS7TCfYWRhE4Z68+71znCTj92xY4cJ0NkvvTD2BJq33XZboffz58ITEAxyObYRI0Z47mNAy3YvnOyysAru22+/Hffff7/n81NOOcU8D8fkJqzcpv79+/tdl1cxXHnllWaiUVbhFxcr73nShv3VeUKiZcuWnvtSUlLMdvP+Gv3999/mI0N++z47+GY1uXeo/t5775lgnicq+Pt0+umnm4Xhu90i6KmnnsLQoUPNyQ87DC+qF3xhXnzxRbPwxEph2MO/WbNm5neX/f75vmPA71TLHxEREREpPeqJLiIiIhLG1eh2axdvc+bMMR/POOOMPF9PSkoybVJ+//33Mg997SCfleD5Q0Z7XPY6RXniiSfMRwbmnECSoTUrmxlmF4btap577jkzCSSD8MJwPOyhTgzEGfjb7r33Xs/EoqHqk08+MScHWJU9cuTIItf95ZdfTHsbXilQ0hMr06ZNM79XfD2G2N7LvHnzTNuV/F9nz3RiMG5/jZN7stqcJ4lsrKYfPXq0uYLA+3thr/y33nrL9Cnn78Mtt9ySZ5vxRAhfN1CcxJVXJ7DfOq8usLE3/8svv2y+R7r44ovNiZ1vvvnG8/vD9kIiIiIiEtpUiS4iIiISpiE6K4jzTyrKXt+bN282EySyP7g3Vs5yIk5W9jJoL2lLCoa07AVeGPYvZ+9yOyBnxXB+9tfssN+2fv16T4jNMJwV3mxLwnB1woQJnvU6d+7suc1KYG9sD8LwlIFw/gr9/K05uPA1OVFkvXr1TKU+v7dGjRrhf//7X57187+OL3ZLF7sy266MLmv8ubH6mlXU/Lmx+pt94xkyF8au0p46darpJ89Q2G6LUlw8KcKJXBMSEszP3D5pwt+BZ555xkzSmf93jeNkOM6WOux9T3ZPef7sbGzRsmLFCtx1112mat129913mwr4e+65xwTbfC+88sorpj86K9IDtW/fPs/Pgb+XrGhn8G9X8tt98dnah2Nl9Tv77fPkDr9vnoTgVRGc6DeQtjkiIiIi4k4K0UVERETCkF2Bnj9ELyq8JgZ/DNG5XklD9EWLFpmlMAxSWQHO3uG+xsEWNAyt2bucAamNQSQrgokVyY0bNzbVwbfeemuBvu82O3DlR4btDL9ZZc9+23ZrEFbeE0NtbwxdOQ6Omdjihfia+QN4tu+gsWPHmiW/wlp68CRB/jC+rPDnxkpwVuAPHz7c/Mx42xdWahODabbV4TYpKW4b7+3Dau5nn30WL730kpn4lf3M8/c8tycA5f2+fkZsB8STAzwRcdNNN3m+/vHHH5vKcAbml19+ufnZM8Rv166daeHDqxV428aTSoVtI/7O8HHEIJ7bn6/Dli7c/ryKghXzvXr18pxgaN26tfn9fv/99021P08GsAq/QYMGJf75iYiIiIjzonIDLZsREREREQkxrBTm5KKsPmfLD2+zZs0yvb5tbPNSVJUyK6EZ+DKYzR/6chJLnoBgcFvUpKwM2/k8rJJmkH0k4XRZYisU/ixYgZ7/RIz3VQ1VqlQxP8OZM2f6fU4G0ny+2bNnY/fu3ebn9O677+Zpz+L9+pdeeqlphzJmzJhCn48nQzhGVtVzWxJDa4bbPEHy66+/5hk7T4qwpQt74vMEET/yygRWmzOIZ2sWnpzxNnDgQBOYsxKdk9byRA6vRmB1PPuvF2bjxo1mu/LnQ5xE1h6fiIiIiIQmVaKLiIiISNhi1bP9MX+Ifv7555sKbbY0Oeuss/z2OWeIOmzYsELvYyhOrJr2nlg0VLHFCiusfQXF9qSg3h/9Yc969g9nmxxOaMufv69JN72vIPClTp06JoT37lHOyV779etnTmjkD/8Z9jPctq/SOOecc0yrFV4NwfHkD9CJQTuvYLDHyasf/LWDYYU/q+EZtLNeiS1lRERERCS0qRJdRERERESCglcEsHo9ErAanodaDOFFREREJLQpRBcRERERERERERER8SHa1x0iIiIiIiIiIiIiIpFOIbqIiIiIiIiIiIiIiA8K0UVEREREREREREREfFCILiIiIiIiIiIiIiLig0J0EREREREREREREREfFKKLiIiIiIiIiIiIiPigEF1ERERERERERERExAeF6CIiIiIiIiIiIiIiPihEFxERERERERFxqe3btyM7O9vzeUpKClatWuXomEREIo1CdBGRMpCbm4vMzMyA18/JycFvv/2GPXv2eD7nc/jCdQcOHIgDBw7gq6++wt9//13kullZWXm+lp6ejq+//jqgsWVkZGDv3r1FLvv378eR6tmzJx599NECX//ll1/w2GOP+Xzciy++iNtuu80cWDz++OPmZ1Icy5Ytw6233ur3Z1DY9uB24s/S20cffYQFCxYUawwiIiIiEl64j17cfcIlS5bgzz//LPD1vn374pZbbvF8/u6776JLly7YsWNHkc/H/dR///3X8zmf++233w5oLJ988glGjBjhd73ly5djy5YtAT2niEgoU4guIlIGGOaecMIJWLdunQl3P//88wLL+vXr8zyme/funh3t66+/HjfffLPP509LS8Mrr7xinnvSpEno06cPdu/eXWC95ORknHTSSQXC6dWrV+N///tfkeG7beLEiahevXqRS9u2bQs87uDBg9iwYUOBhcFzYTjW1NTUAl9fsWIFXn/9dZ/j4/f95ZdfIjo62hys3HDDDQW+V37N+wDC2z///IPp06f7fH6eJEhISDDPHxUVlWeJiYlBo0aN8ozl6quvxh133JGnWkhEREREIsszzzyD999/3/P5XXfdZfbvb7/99jzLjTfeiFmzZpl1HnjgAbNfv2bNmjzPxf19rrNt2zbz+bPPPos777wTderUKXIMU6dORatWrbBr1y7z+Y8//ohrrrnGU7hTFO6Df/rpp3m+xn1uvja/lwsuuACNGzfGMcccg8suu6xYBUQiIqFIIbqISBm45JJLTOjKChEGx2eeeSZGjRqF+++/3ywXX3yxqe5gGM6dWq7LQDY+Pt48/oorrjA7vU8++WShzx8XF2c+xsbG4s033zQ7rS+//HKB9WbPnm2e88orr8wTZFetWhUXXngh/vjjjwIhd358fMeOHU0ldmELA+hy5coVeNz8+fPRrFmzAouvqnV+L1yK+jp/XgznvQPqxMRE87NjqP3cc8+ZihmuY1u7dq35ObRp0wbvvPOO+Vl5f7/8+fOx3l/bunWr5/H8WfES2ieeeAInnniiOehglRBt2rTJVN/Yhg4digoVKpjX45hEREREJHIweOZ+K4tDuL/OfVYWWfBzFl5wX7hhw4Zmv7J8+fLma82bN0etWrXM47lfXaVKFZx33nmm8ITrVqpUCf369TP7t+3atUO1atWwcOFCjB8/3uyn8nlmzpxZYCzc52VhD49D7OcfPHgwatSogZEjR/r9XngMULly5TxfGzt2LCZPnmz2yblf/PTTT5urOhmu28cnIiLhqmBaISIiR4xVGT/99BPee+89tGjRwoTkrERp2rSpuf+UU04xO5qsKGE1x9KlS/M8/uSTT8ZLL71kWq6wgpo7zXY1NNntWfbt24eKFSuaFiIMk73ZVernnnuuCbQvv/xy85oMjO2q6jlz5ph1uZPO9bmznb+KxA72i8Lnys8Ovjdv3mwOAFh9z514tltZuXKleV6uY4/l0KFD5gCD1fg1a9Y0BxT5n/vUU0/FvHnz/I6BFT38udJZZ52Fv/76y1SI86RB586dPc/tjQc13q/zzTffeJ6XVT4MxzleHriwOp1Yhc8DG+LBELfxDz/8YMYvIiIiIpGF+7fcp2aBCfchn3rqKTz//PNo0qSJCZtp48aNJsS+7777CuxnM0BnMQZxH5n7sCza4FWfSUlJGDZsmCmEYbjN4we2UWFAXti+LV+XhSCsbrfxeIL7rBdddBHOPvtsc5yQH1u+cFwM/7l/zqp4toVhgM/vi6/PoiARkUijEF1EpAywhzZ3Mi+99NI8Ae9pp52Wp90Id1ALq+Km//u//zMLL/Pkzm5hGE7bWC3OkNjGFiiswualllw4ppKwA3Y7OM6PlSiFXb7pqxrl999/Nz8Xu8LcPjHAEwIM3GfMmGHa2bByJj9WkvMEAp/bDul//fVXUwHO751j5ffJEws2fs5Q+8MPP/RUAxHb6fCkxvfff28OBuzLXPk1nsQoDl41wBYuvGyXbXlEREREJPLYVyLa+7dse2IHzryakfujDMZZiOHdrpBtFuvWrWtut2/f3vN1FqGwBUz+9i620aNHm4IdFt94Y0vJu+++2/RRzx+wc7+X7Vd4nMGCmvz7vV27djX7zLzilPvWbNfCMH3nzp2qNheRiKYQXUSklDFU7tChg6mGzj9hJVuFeIfRhVVwf/vtt6ZChfd16tQJDz/8MB588EETtts75KxOZzDMim62KeGOrffkoWyZwvYx3u1RWCXNHWlWVNsV6Vy4c8zHs0Kc/cO9A2hiFTwrZ7iz70th/RjtsebHavT8k3HSsccea3bq7733Xk+vcl7KynCd7VW4g88JRvOH1Kwg59h54MFgnBX5dnU4e75369bNtHjh5KP8votqs8LvlZXrnLwpUHwMW+kwRGeYn5KSYnpUcruxal1EREREwh9DZ7uwxHsfn1/nvjYnAb3qqqtw/vnnm6seGZDbRSK8knHx4sVmv5KPZZDOcJz7wtyvfOGFFwq8HotleOXrlClT8nydBSRsDcl9ZLZfKcy0adNwzjnn4PTTTzctWa677ro8xzIcQ48ePcxtto6x8diCxzL5W0DyeIJLy5YtS/zzExFxO4XoIiKljCE1d1g5aQ/DcO++5tzxzN9bMD9O+PPFF1+YgJwVJNyJ5g4sd74ZpHPHmM/B/ocMmhmis6Ld+3LQm266yXz0vkSTwfAZZ5xhnsPuIW7jTi9DePs5+DnDYF7yyR13LoHgGPlYPs5+fu+JN8n+OitqONGRLwzz2TueFeb8/nnZKU8O5D/xwN6TvJ/jZyU4W+gwTOd6rNTn2Lk9WO3z22+/mQMZ4rbhYxjQc8w8ECEG7t6tXVh1wyCfFez2ZKm8dNZ+Dv4sWc1u/+yuvfZafPzxx2a8bNsjIiIiIuGPIXnr1q3NbfYq577oBx98YIphuK/J9ot2gO1daMF1uO/MFi+sUuc++MSJEzFkyBBz/9tvv20KdDgPkrfatWub/Vu2kWSBCo8NWEE+cOBAUwDDKyVZke6rgIRXfbKSna/z1ltvmTYyLJrhuFlY8/PPP+P44483+9/cz+UYeZLgxRdfNFeOUmpqqimc4cKKd++5gkREwo1CdBGRMjBgwAAzaU9h1djcAS2KPfkoA1g7mGWFs/eEQbxks0GDBoVOBGpXgjBQ5iWjNobLvHST7Cp0uxLdDpZZ0c0qbu44s/diSTC0fuSRRzzf+9y5c02VOCtsBg0a5OnFzj6MDMDtg4382BOSgTZ30rkeL2VluxdiSM4DDJ5gqF+/vvkaQ24eKPTs2dMTtPPnxwr0/v3746uvvjInOOwrAfIH3Hagz76SPAiy8UCBPStt3gE7L2+1L8/la/GS3XfffdeE6ArQRURERCIHr8xkQQlDa+4H8wpKFnRwXiKG2+xtbmOhCAtGOJGojfvMxKsoebzAiUO5T81w3N6vZptHLrwildXsxP3r3r17m/1+Hi9wn5+3WVTDfeOisMCE+6zc9/e+6pQhPo8PuHA8HC+PT7j/zR7r9sSkfN2TTjpJPdJFJCIUfq29iIgcMbYtYU9BYlBN7LvNyhTvrxXFbsfy0EMPmUlGiZMIscc5w+fCqj24k80dcl7+6Y2tUlilYofs3JlnJbb9Oau57TYovHyUgTXDZAbErEQhVsfw88IWtpjhxKBsI+P9/fHggX3GvVu+sCKel3uOGzeuWD/Twtrf8OfJgxC2buHPhpel5seAvF+/fp4Ke3uiJh6g8MSBfZDQp08f05PS++QHDxL4s2G7FvZy53oM8YmVOfyZ8vXZ654HHKzk4fOIiIiISOTgfipbB7LIgi1auP/MQhAWvbDNH/c5bazototYfD3XmDFjzH4m97F5DOFrYTW4XThz9dVXm+MDHodwv5j7+tw355Wr9v4uF175al8xynaPnITUxkCdrQpPPPFEMw62qHz00UfNSQB+DywUEhGJRArRRUTKCHd4Gd7+8ssvZmeVbVgYNtuV08WZ6JNBdJMmTcxths+sFGG/dE6qWRjvXujeX/Nus+KNX2OVi40HAHxNXibKy00XLVqE4447zgT3/LywhWNiP3L7JAFD+sLwZ8EKHVbX8xJUO5AuKT4fq8PtXvKFhejEHpT33HOPacXCQJ/fy1FHHWUqx4nj+PLLL8163tj+hj8bnlBglXr+nxsfxz7tr776qnkunrzgwQxbwIiIiIhI5GCPcobmrBLnVYrc/2Zl+GuvveYpVrH3IXlVaSBYqMF9UIbz3guDce7bM/Qu7OpK7r9yn5utXbwnKyW2M+TY7MlMbSw44T4695NZtGO3KmSPdVbZsx0j9/dFRCKRQnQRkTLyyiuvmJ1N7oSyFyJ7nNs7zGxT0qtXLxM0c52isIKE1dP5de7cGb///nvAYfyff/5pXp8hd/4AnJXUdkhfGIbDnOzIuw1M/sWuVrfZlegMrXkwYbdIsb/O1jKsemFYHwgG09zh58EATyRwIlLeZoUOTyg8++yzOOGEEwqd5JQHLmwnw4MBnnho166dZwz25aq8DLVjx45muxSGBw2F/YxYjcNelLwEl9Xu3F68bJeTMXHMIiIiIhIZWLHNnuRst0IsoJkwYYLZ9/euPOecO3Zhja/iE+5TEq+QZMsVhuHeCycm5XGA97xI+bGo47vvviuwf8t9ar5+/taTPH5hYcrkyZPztEfk5KXc5+brcR9cRCQSKUQXESkj7FvOme5ZOXLvvfeanoOsWGZbFrZ54Q4od0SLCtF5P9uwcGJMb9yJ5SWW3OHmji559/EuDCuqiZd4si+498KQ2b6/MGwPwxC5sIWTFlH+3ub2wQC/Z1aKs1+j9wEBq9xZ2cKq96Lwcle2amGAzZMJfLz3wj7nPDDYvn27Ca/zY1ua4cOHm37lnBxp1qxZnglXhw0b5nkcv87elYVV6vPS1fnz55tq/PwY2rPCiBVBnHiU24vbmNuMJydEREREJDJwn5rFM7YXXnjBVIazzQr3jbk/y/17FsJ4h9Hch/TGfdy+ffuafW0WjbCgJX8Biz1PT2FzMNnYEpLhPVsS5g/R2colP1accz4iHmfkxxY1rFxn8YiISCRSiC4iUgY++OADrF271vQYtHtnM1TnzjC/dtFFF5kdaV4uyd7cheH9DGLZz5A73uypSHwO9jlkBTRbl3BHlzvaDOa5Q+5LUTvY/u5v3ry5qf4ubOFJgMqVKxe4tNMO0Rlic+edrVIY1Nshuj+cKJQTgrIPI1+Hleb2ZJ2svuGkRjwo4YHIH3/84blslUH7E088YV6HJxs4ERJ3+Dk56JtvvmnGw58/8bJabgN+nW1YfLWC4QERw3pOnOQLq+1ZfT5v3jx8//33nqBeRERERCIDr/hkAQ0n9OQ+fL169TB79mxTNMI2gtxfZJU3izBYgMFwnAF6z5498zwP93N5FSv3nbmPzqspvXuae8/R42sfnsUh3Cdmdbz3pKG0atWqQkN0BvZXXnmlue1dOc99ah7L8BhERCRSKUQXESll3Mm88847TUWy3etw/PjxZgeXgTB3llnVzEs7bWxx4r2jykmCuOPNnVtegslA9vzzz0fv3r1x1llnmUCZWGHNHfPHH3/c9APPX9XOz+3ntduo8DnzV7IwQA5kotPCsF+63XvRGw8W+P3b/dkZUPMgwe6Z7o2vzYp47ypwXgbLhZeq8ufFCppbb73VtH/p1q2bqcx55pln8Nlnn5m2MNzhZ7U527LwwIOTgfLrfCx3+jnpKS+l5TbggQtfk1/ndmC1PCdkYvWNHcjb2JKFBx/XXHONp1rf/lnxZ8uTBdOmTTNV7rRw4UIkJSWV6GcpIiIiIqGLxRp2i0CG6Wz3x6sv7f1HthHkcQGv8mSrRV6pyWIZHj/YuA/LqyA/+eQTU/Xta54hW/4JShm+c7+ZxyNcuO9rvz6r5Hml7BdffGEKcIrC4wj7tTlnEPelb7755gJXjAZaICMiEvJyRUSkVC1evDi3Zs2auT/++KP5/L333suNiorKff755z3r3Hfffbl169bNTU9Pz/3iiy9y+/fvz73f3N9//93cn5WVlTt48ODcHTt2mM/nzZuX+88//xT6eueff755LJ8vMzMzz33nnXde7m233WZuL1myxKy3fPny3D179uRZXn311dxGjRoF9P39/fffuU899VTu008/bb6P8uXL5956660l/Gnl5v7f//1fbu3atc3YPvnkkyLXfe6553KfffbZ3H379pnPOY64uLjcJ5980vzMLrnkEvM8Dz/8sLmfP49Zs2aZ2/w59+7dOzc5OTn3tddey+3QoUNunTp1PPdzeyUlJZnHt2zZMvejjz4yXx8zZkxupUqVcrdu3eoZx4oVK3JjYmLMc02YMMFs32HDhpntKSIiIiKRZ9OmTbmtWrXKrVGjRu64ceNyDx065LmPxwPt27fPrVq1qtkntS1btiy3adOmuSeeeGJudna22bfkfvE777zjWeeGG24w+7t8rPdSuXJls9+6fv16z7qpqam5xx9/fG6DBg3yPIftuuuuM/u5d911V25GRkaR388DDzyQ27Zt29y0tLTcUaNG5Q4dOrTAOl27dvUca4iIhLso/uN0kC8iEm7YZ9ue7Z6V5KxyfvLJJz33syKblePsI842Lazq4GWaN9xwQ7Ffi8/F5z711FPRpUuXPPdxok32W2RFdmlhuxRWg7ONCqtjWMXC12d7mZJgpTirdHgZa3EnKmI/eFbssyLfxglQ+TyF9TYn9j5n9TkraoYMGWLatHj76aef8Pbbb5vviZe02pe85u/5buN/o6o+FxEREZF//vnHtAtkq0NvbO348MMPm31W7wlFadOmTaYlo90KkNXs3q1WbrrpJvP4mTNn5nkcW8OwLzorz9u0aeP5OudJ4v5t/jEUF68S5RWZ9pW1IiKRTiG6iIgUGy/vjI+Pd3oYIiIiIiIiIiJlTiG6iIiIiIiIiIiIiIgPmlhURERERERERERERMQHhegiIiIiIiIiIiIiIj4oRBcRERERERERERER8SHW1x0C5OTkYNu2bWZW66ioKKeHIyIiIiIRgFMWHThwAPXr10d0dGTVvGj/W0RERETcuO+tEL0I3IFv1KiR08MQERERkQi0efNmNGzYEJFE+98iIiIi4sZ9b4XoRWAFjP1DrFKlStCrcHbu3InExMSIq0CKJNrOkUPbOjJoO0cGbefI4dS23r9/vwmS7X3RSOLU/rfe15FD2zoyaDtHDm3ryKDtHDlyHNjWge57K0Qvgn0JKXfgnQjRDx48aF5XfyDCl7Zz5NC2jgzazpFB2zlyOL2tI7GdiVP73762dU4OsHKldbttW8Dfr0FObg5W7rQe0DaxLaKj/DwgNwfYd/gFqrYF/K0vIf++luDQdo4c2taRQds5cji5rf3teytEFxERERERKUR6OnDMMdbtlBSgYkU/62em45gXrAekjEpBxXg/D8hOBz47/AIXpwCxftYXEREREUe48vTNrl270KxZM2zYsCGg9X/44Qe0bdsWtWrVwsSJE/PcN3v2bDRp0sQ0h3/jjTfKaMQiIiIiIiIiIiIiEo6i3Rign3322QEH6OyT079/f1x22WWYO3cuXnvtNXz33XfmvmXLlmHAgAG477778MUXX2D06NFYtWpVGX8HIiIiIiIiIiIiIhIuXNfO5dJLL8Xll1+OefPmBbQ+Q3NWmTMoZ+8aBuXTpk1Dr169MHXqVPNx8ODBZt1hw4Zh1qxZeOihh8r4uxAREREREREREZGy7qF96NAhp4chpbg9MzMzTV/00uqJHhcXh5iYmPAL0V966SXTymX48OEBrb9kyRITlNvN37t06YK77rrLc9+ZZ57pWZf3PfDAA2U0chEREREREREREQkGhufr1683wauEh9zcXLM9Dxw44Heiz+KoVq0a6tate0TP6boQnQF6cezfvx/t2rXzfM7ZW7dt2+a5z/v5vO8rTEZGhlm8n5u48YL9huTr2b84Er60nSOHtnVk0HaODNrOkcOpba3fLREREZGicR/tn3/+MRXGjRo1KrWqZXF+u2ZlZSE2NrZUQnQ+X1paGpKTk83n9erVC58Qvbj4Qy1Xrpzn84SEBPPD8XdfYcaPH4+xY8cW2nedlxEE++Bp3759ZmPrD0H40naOHNrWkUHbOTJoO0cOp7Y1K29ERERExDcGrcz42OK5QoUKTg9HXBqiU/ny5c1HBum1a9cucWuXkA/Ra9SoYUJu74OO+Ph4v/cVZtSoURgxYkSeSnSezUpMTDRV7ME+aOMvC19bB+jhS9s5cmhbRwZt58ig7Rw5nNrWLPwQd4iLA26//b/bftePicPt3W/33PYrKg5oe/t/t0VERCQg2dnZ5mNROZ+IzT7Rwn7rERuiJyUl4fXXX/d8vnjxYjRo0MBz39y5c3HNNdcUuK8wrFr3rly38aDJiYNkHrQ59doSPNrOkUPbOjJoO0cGbefI4cS21u+Ve/C4fMKEYqwfE48JZxTjATHxwHHFWF9ERETyKM2+2RK+okrh9yRk9tBZFc6zBfn1798fv/zyC77++mtz/2OPPYY+ffqY+y644AK8+eabWLp0KVJSUvD000977hMRERERERERERERCZsQvUOHDvj0008LfL1WrVqYNGkSzjrrLNSpUwerVq3Cvffea+7r2LEjhg8fjs6dO5sKdJbrDx061IHRi4iIiIhIqOEcrxs2WEsg873m5OZgw94NZuFtv7hOygZrCWR9ERERCYuJ61esWGHaUHv7448/TI93G9ezbdmyJU/Lam8nnHACvv322zIctbg6ROcvStOmTT2fb9iwAeeee26h615//fUmPH/ttdfw559/mjDd9vDDD5s2LrNnz8avv/7qaSYvIiIiIiL+7dq1C82aNTP744H44Ycf0LZtW1PsMnHiRISy9HSgWTNr4W2/62emo9lTzczC235lpwMfNbMW3hYREZGwNmfOHLRv3x6XXXaZ6ZrRrVs3zzJo0CDs3r3b3GYx8fLlyz2PmzJlClq3bo2pU6cWeM64uDizFBdfg21O7GXw4MGe+5ijNmnSxIT6b7zxhufrp5xyCp588knP5zNmzMCxxx6LSBDyPdFt3LHnUph27dqZRUREREREihegn3322QEH6KyQYrvF2267zRwcXnrppTjuuOPQq1evMh+riIiIiNv17dvXLNy3Ylj+22+/ee7766+/8L///S/P12wPPPCA6bjx1FNPmf2rypUro2rVquY+hvHs0MEOHAcOHMCzzz6LG264ochxpKWlYd26dUhOTvYE8PY8kcuWLcOAAQPw3HPPoWvXrjj//PNx/PHHmxA/koVNiC4iIiIiIqWLB2mXX3455s2bF9D6vDKUFUv33XefqWgaPXo0pk2bphBdREREgiI11fd9MTFAQkJg63Ked+9mFr7WrVgRJcbiA+8uHJzrsbCK8qysLDMfJOd+5ELx8fHYvn07EhISTHX4Qw89ZNpZ16xZE6eddprf12bXDlaiJyYmFriP1e7cd7Mr04cNG4ZZs2aZ14hkCtHFFTZvBlat4h8GoEULoGVLzpzr9KhEpCTYM3bpUmDrVqBCBeCYYzh/hdOjEpGSSknhTjYneQfYMa99e1apOD0qCZaXXnrJXO3JeYYCsWTJEnPQxQCdunTpgrvuusvn+hkZGWax7ecv2uF+oVyC3Z80/2tan0Z7jcn/83jf9vs95OR4+muadYP4PUcqX9tawou2c+TQto7c7Wx/zV5slSr5DpPOOisXn3zy3+e1a7Miu/D1e/bMxXff/fc5s+5duwqum5Pz32sXB8dcsWLFPPtYrApn6xTv74c2btxorvKrVKmSmReS7VOio6PzfO/8yErytWvXom7dugWeIz8WSLDPOkN0hvcsnGCbFj4H9+dYEW8/R1JSEh588ME8r+V92/44efLkAnNRvvzyy6ZNTXF/Nt4fS4M95sL2zwL9+6EQXRzD98I77wCPPgr8/nve+1q1Am69FbjmGp5dc2qEIlIc+/YBbH37wgs8o573DP7ppwOjR3PCEydHKCLFsWwZ8OCDwHvvWSe5bbxq9MorAc7jzgMPCW++2iX6whDcu41ilSpVsG3bNp/rjx8/HmPHji20MuvgwYMIFh487du3zxxc8aDUdnDVFgCdPWNKTS36YC4tM81z26wfV0SJG4Co7DTU8Vo/N6bo9aXstrWEF23nyKFtHbnbmcEvv84qbS7/8d0bnI/PysoOKBYNdN28rx04Po7tV7z3tVhdXthzNm7c2Mzz+MILL5gg3f6eOf8Mx1mhQgWcd955SE9PN4H3V1995ff12TqGE5Ly6kH+bAcOHIgnnngCd9xxh/mcr2mPg8+/detW8zlf7+677/bsvx06dAitWrUy91155ZW48MILzdc50Skr2Hv27FmsnxGfPzvb+rnbRRmlgWPg7wt7zuev9mcLnEAoRBdHbN8O8ETUF18AZ5wBvPUW0KkTJ0OwKlhffRW48UZWPwGvvw60aeP0iEWkKN98AwwcCOzZY5384hVmvKKE/xf99BPw/PPAiSdyImhg0qS8l9CJiLtwn5Xh+cMPA02aAI89BvTuDdSsaV059tFH1smyV14BXnwRuOQSp0csbhIbG+vpp0m8xJg9N30ZNWoURowYkSeEb9SokamKYgAfLDyo4oEaX9c7hEnd8N9ZYd7n75Lt1EOpedeP9/OArLzrI/YIrgmXI9rWEl60nSOHtnXkbmeebGf4yX0PLrYDB3yf8I6Jicqz7o4d/Lfw9aOj8667fn3h63qv4w+rxHv06GH6mfP7SE1NNVfs2c/B7+mff/4x88nwNnudc3LRGjVqmPsZSv/0009o2LAhqlevbqq8n3/+eXz66afmfs5jc/rppwc0phe5I++FYfozzzxjAnKGzAzO7edhcJ+enm4+53a4/fbbcfXVV3smIGU7P3s78HEsoGCFPScdLW5Bhq0kE6UWhWPjz5ztbrh/6i3/5z6fo1RHJBIATi585pk8awh89pl121vjxpxoAbj9dmDAAKBbN+D99wG10hRxp8mT+Z+59R6dNs16D3vjSTAG6/w/mjkJ20Lw/3gGciLiLunpVijOy1x59cioUXlbt9SvD3Ttar2Xb7qJ/bKtk98M3dWGTYgHeayotvHg1q6qKgwDd+/Q3caDnGCHITwozP+60V6/2NZ9RT9HnscG8j3kW9/vC0iZbWsJP9rOkUPbOjK3Mz/ya/Ziq1Qp8Ocsq3V9adGihZnMk0HzJ598gptvvhk///wz/vzzT3OCoEmTJujevTvGjBmDiy66yITsXNf+/hias1r83XffNZXiZ5xxhmmVwtCaIfePP/6IV199tUQV3HXq1DHV5nws9+c4ubz9PAzz4+PjPZ8ziLbDcbunun0fK745ufxVV11lJlAtLlai289VmpXo9u9JYX8rAv3bob8wEvRLw085BaheHVi4sGCA7o2V6fPns5emtd7XXwdzpCISiGeeATjpN9ueff55wQDdxv+TuN7PPwN//239Hfj332CPVkSKws4Z/fpZ/9/yRNf99/vufc6TYK+9BkyYYFWs33ab1aZNhJcQz507N8+kVQ0aNECoio3JxVA8h6HnbkMghWax0bEY2nmoWXjbr6hYoNVQa+FtERERCVsMaxlI33LLLaZS+6OPPjItWdimhRXmrFBnQM6J2dlbnFXpdpC8adMm3HnnnWbCdlZ/n3rqqSbYvu6660xlONe/6aabTIV6IBjWb+Zlpodx/40h/pHuz91zzz3mYzhOQqoQXYKG7TDZuoVVbJycIZD3H6/i/fhjq8L1nHOsClYRcYd33wVuvtm6auSpp6yZzv3hybEff7RaOp11llX1KiLOYwDOK0Z++cU6IVbUSW4b9+f5/n/uOatN0/jxwRipuAXbrrAXaX79+/fHL7/8gq+//trc/9hjj6FPnz4IVeXicvAchuG5W9cGNKFuudhyeK7vc2bhbb9iygFJz1kLb4uIiEjYYh/ylux7CmDmzJmm9UrTpk3x2WefmeCc1eVnnXUWFixYYMJzBt3s4c3WLqxMZ3U3JxWdMmUKBrB1A2DawbCdy6pVq3AvJy0K0NFHH40hQ4aYCUY5Fla438DKN7A96wV48803sXTpUhP6P/300wHtz3EcnJSeE4zyasS9e/cW2dYv1ChEl6DIyLB6JLMalX3QD7dzCggPWBjWtW0LnHceZ0Muy5GKSCCWLLEmFmTbB/ZLLs5VVmzvwpDuzz+t6nRVr4o4j9XknINkxgygR4/iPZZXoowZw6oT68S3RIYOHTp4+m96YzXVpEmTzAEgLwsu7gGdiIiISLhq06YNNm7ciCeffBInnXSSqTTfsGED+vXrh8cff9xUhv/999+mB/lzzz1neqizdcr06dNNu7wHH3zQ9EXv2rUrOnbsiO3bt+P66683rVVY5c7JQVnMwJYo/vD12FKvV69epn3MhAkTzOOJz81K+c6dO5sKdE6AOpQ7/X6888472LNnjwnoWRHPhZXy4SIqN5CfbARX2FStWtXMShvMiY3sSROSk5NRu3btkO/rxd+wa6+1JgvlBINJSSV7nk2brCrWjh2tAK4Ycze4VjhtZ4mcbc1Wt507Myix3tMVKpTseTgpIf+PZk/1IUMQFsJpO0vkbOf33rNOdI8da/VBL+n/9TzR/cMPwKJFQPPmCAtObWsn90FLy/r160211cknn2wOBN3+vfva1rmL/8Cu408HPvgQtfqf4PekMQ+tdqVZFR+1KtTy38uTb56MwxUi5WppcoEgCLe/4VI4befIoW0duduZ1dnc32CAHOjEkG7A/ucM0Nm6xZ48k9XanPjSnkeG/c1ZAc7vuWLFimb/YvXq1WjevLmpQm/VqhU++OADfPjhh7jxxhtNO5eMjAwThLM1DKvb2R7mSH8uK1asMH3Se/bsWeQcN6WJ3yv7qtuTmJaWon5fAt3/DIMYUtyOVW2cbHD69JIH6MRey2+/DZx+unWQP25caY5SRALBY/1Bg6zeyR98UPIAnVjJPm+e1RLmuOOs+Q9EJLi2bLHauDBEv+++kj8P92/5//3xxwMXXWS1hQmhYxkpAzxAsSecCmVp6VGojZ3AuZxUC6hY0c/6mWmo/XhtcztlVAoqxvt5QHYa8J61Pi5OAWL9rC8iIiIhfzUfQ9viYJjcunVrc5uhOUN3VqazbZ5drMCQ+4EHHsDdd9+NHTt2lMqJhXbt2plFLDpNJ2VqwwZg+HDgqqus4O1IsTc65yZ49FHg119LY4QiUhxTpgCffWadFGvU6Mifj32UeXXJ//0fz76XxghFJFA5OdbVIAwF+d4+0kKPatWs9mvLlwOjRpXWKEUcpot2RURExGUqVKhgeqIXdrUfw3N7glApXQrRpUwPzhmcs//5k0+W3vOOHGlVrPLAPzW19J5XRIq2Zg0wYoTVeoWTgpYGXhE2axbAScEPT+ItIkHC/5u//ZaTGhVvrpKi8KoS9lfnZMNs9yQS8hSii4iIiIhCdClLPIBmb1RWrJZmS8uYGKuX8tatwJ13lt7ziohv2dnWiav69TkBSek+N69Ks0O3H38s3ecWkcKtXm1Vi996K3DaaaX73LfcAnTrBlx9ta4wkTCgEF1EREREFKJLWVm/3qoqZa9jtmApba1aWS1dnntOlW4iwcBWD3PnWifFijE/XMDY9unEE63WTwrdRMo+Exw6FGjQwGqRVtp4spt/K9hvXVeYSMhTiC4iIiIiCtGlrI41hg0DatWyqkvLyo03WpVubC1x6FDZvY5IpNu+3apYHTwYOOmksnkNhm4vv2xdYVKWfzdEBHj9deCbb4Dnnz+yyYH9XWHy4IPWFSYLF5bNa4gEhUJ0EREREVGILmVh9mxr4sFnny2bilVbdDTw4ovWJeml3V5CRP5z221AXBzwyCNl+zq8wuSuu4AJE4CVK8v2tUQi1Z491twGF18M/O9/ZftabOvSvj1w/fVWSygREREREZFQpRBdStW+fVZbhnPPBfr3L/vX69DB6ufKard168r+9UQizVdfWVWrTzwB1KxZ9q/HEJ0Tid9wg4r/RMoCryo5eBCYNKnsXys2Fpg8GVi0yPooEopio3MwEDMw8PSt5nfa//qxGNhxoFl426+oWKDZQGvhbREREREv27dvx44dO5wehihEl9J2773AgQPA008H7zXvvx+oXdtq76LQTaT0MGhj3+SePYErrgjOayYkWC0mOCnxrFnBeU2RSMF5DXgFF1smcZLgYOjeHbj2WuDuu63WUCKhplxcDmbgKsy4bRnKlQtg/dhymHHuDLPwtl8x5YDuM6yFt0VERCTs7d27F/PmzcMeXibqZdOmTUhNTc3ztalTp+LCCy8s8vl27tyJnj17msCd+vXrhzlz5njuT0hIwKFDh7Bq1Sp8w76Oh61ZswYpKSmFPmf9+vXx999/l+j7C1cK0aXUzJ9vTfTJqvBGjYL3uhUrWq1jvvgCePvt4L2uSLhj+5aNG4EXXgCiooL3uqefDlx6qdVG5t9/g/e6IuEsM9Nqq9K5s3WlR7D/lsTHW21kREKOXaGhSg0REREpBe+88w6aNm2KwYMHo2HDhuZz22233Yarr746z/qzZ8/GXbxkuwiJiYkmROdzUrly5XDLLbegQ4cO+Prrr1GpUiWceOKJGDlyJFavXo3cw/s1Dz74INq0aYMPP/ywwHPGxcWZpSRmz56NXr165fkaX7NatWqIioryLA899JDn/ueeew516tRBixYt8N1333m+zp/VBx984Pn8/vvvx7lsf+EAhehSKrKyrIPzY4+1JhUNtn79gPPOs/qvsqWMiByZVauA8eOBO+4A2rYN/utPnGhNGMzWEyJy5HiF2LJlVlsVTuQbTDVqWHMdvPEG8PXXwX1tkSOVm5OLVFRAanp0QDk6DxBTD6WaJTewBwBZqdaioF5ERCSs7du3D0OHDsWPP/6IpUuXmuCYwbbthRdewK+//oonn3zSfM5q9SVLlmDAgAEmgPZe3nzzzTzPPWbMGFO1bps8eTLmzp2Lk08+2fNcH330Ea699loTYNPMmTNxzz33YNq0aVi7di2io6M9z79582Ycc8wxnuB7jldle1G++OILDBw4sMB+EKve+VysvrcX+3vnY26//XZMmTIFs2bNwvXXX4/du3fDbRSiS6lg+4U//rAOzgPpF1lWAQGvQmFLGREpOf5fxzYuDRsC99zjzBjq1QPGjQOmTLFaUIhIyW3aBIwebZ3k7tTJmTEMHAj06GG1XsvIcGYMIiWRlh6FSkhFpfPPQFpaAOtnpqHS+Epm4W2/stOAtytZC2+LiIjIEbFPZhe2HMw6GPC66ZnpAa1bHPv37zcBOSvE6fjjj88TFteqVcuEyGPHjjWBOyvFH3vsMdP+xV5YSc6WL7179zaPYXuWl156CdOnT0eFChXM1zIyMkyLFwbt2dnZ5mtZWVnIzMxEy5YtTY91jmXhwoW44YYbTLhesWJF1K5d2/M6jRs3xvLly/Hnn3+aivQe3Jn3g0H8sGHDcCN3+vNZsGABunfvnudEACvm7ZMHDN7POeccnHDCCaYdzfvvvw+3UYguR2zrViu45uXhXbo4Nw4Gfg88YLWUWbjQuXGIhLrXXgO+/dZ6L5Uv79w47NYT/MhWFCJSMjffDFSrZrVbcwqLXXjCnW0VH33UuXGIFJuqw0VEREKKfTK7sOWCty/Is27tx2v7XPfM187Ms27Tp5oWul5xNGrUyFSVEwPtSZMm4Ty2VfByyimnYOXKlZ5qdYbSn3/+uacv+scff4yuXbuawJ1Y0b1lyxZTyc3gnf766y9T8b548WITgDM4P+qoo9CqVSsTjrNtCsP4//u//zOtUdavX2+q0AvDljOsSq/IXsp+1KxZ04Tl7dq1K3Df/PnzzcLwnGH9vffe66lWZ7X9qaee6lm3c+fOWLRokd/XY5sb7/YwXL7//nuUFYXocsRuvRXgyS5OVOa0m24CeEKPodvhk20iUgzsQc5e5BdfDPzvf86OhS0nOAkiW1BMmuTsWERCFdsbcnnqKaBKFWfHcvTRwO23W1eZrF7t7FhEAqYQXUREREoZQ+O6deuacPxptlXIhyFz8+bNMWPGDJQvX95M/rmRE5aZfeqj8/QSZ7jOynWG03YPc/ZZZ1U4+4t/9tln+O2337BhwwazMJy3g+ply5aZ6vDq1aubSnVWr/P1KleubF7z2GOPNdXt/nqy2/g8HEdhGNqzwpzB/uuvv27azbz11lvmPlbFN2vWzLNulSpV8M8//3g+925n8wgnXPLqj263hpk4caL5flndX1Ycarwh4eLzz/nmtCpXfbxPgoqtZBi6de9uTYboRH92kVB2553AwYPuCa35/9/w4fzPEbjoIsDr/1UR8YMtznhy+ayzgAvyFt045r77rEnAebL7m2+CO2mxiIiIiIS/lFEpPu+Lic47OVDy7ck+142Oylt3vGH4BpQWtnP58ssvceutt5rJQDkRp41hdrdu3UxbF3tyTlaa21XmvM8fht/07rvvmop0Xxi6s9J9xYoVJjjv1KmTaavCNi7P8zJSwPRFL42JPOd49VRnYH7zzTeb7/vSSy9FbGysp7ULJSQkIM2rjx4r9s844wxzm+1weDLAXo8LK/bZ+oatbRjAlxVVokuJpadbvU1POw247DK4RteuwJAhwN13A9u2OT0akdDx008A5yHhid369eEabNPEK9X4f78KAkUCxz7ou3YBzz7rnrCaV65x/pTvvuNERk6PRiQA+o9HREQkpFSMr+hzSYhNCHjd8nF5e5v6Wq8k2HaEgTUn9nzvvfdMD3Ibv3bw4EHTesX23Xff4e+//8aqVasCen62dmEbFj4PK7RZdc6FQTWfx9u4cePM5KJs6cJWLxdccIGpZGeIzddleN+3b1+UNlbbb2V/aAA1atQwVfC2AwcOID4+Ps+6TZs2NUv+Sneuy2r88ePH47jjjkNZUoguJcbLsbdssfomu+Xg3Hts7OU8YoTTIxEJDZzojyefeFKbH92kUiXr7wyvfDl8tZeI+LFggdXCZexY913BcfrpvCTTah3lta8s4k4K0UVERKSU/PDDDxg5cqTncwbFDNTtfuQMhNma5ZlnnjHV2XZ/83nz5pkJRhmO233Ei8LnZYU5Jw71XurVq+dp+UL82nPPPWcm9nz77bdNX3K2mTn99NNx3333mefgR3ssJZWeno727dubj7a5c+eiSZMm5nZSUpL53PbHH3+gfoCVfddee605ITEkCEGGQnQpkb/+siYGY1uk1q3hOtWrA088YQVuX37p9GhE3G/CBGDNGmDKFMDHfCKO6tfPakfB1i579jg9GhF340S8gwcDHTta85a40cSJ1ked7BbXU4guIiIipYSTe06ZMsUsnKzz7rvvNm1K7BYkt912G7p06eJp47J7925ccsklGDNmDIYPH47k5GRcf/31ZlLSwnAyzptuusmE8mx7wlYs3ss2r3YNfO6LL77YtEFhH/QPPvjAVKETe66zhUqlSpVMu5kjVb58eTOZKVvLMLjnc7Mv+g033GDuZyU528ewMn3Hjh2mF3yfPn38Pi8fY59gYDU/F1bflxUXRiXidpyw86qrgKZNrRDdrVjlxsl9eTKKfWFFpHCc4I/zknDCv/bt4Vqcb4X/H7r5746IG/AkMifkfekla64QN6pdG3j8ceDVV4GvvnJ6NCK+xUTn4kK8gwu7bzUTXvtfPwYXtrvQLPn7rhYqKgZodKG18LaIiIiELVaCsw/4U089ZSYIZcuUV155xdw3f/58c3sCK9wOf85JPxmoM2yOiYnBJ598YiYGZcU4K9RtnESUATSrsU844QRTrX7LLbeYiUO9F1Z3s+c6MXhm1fl1112HQYMG4aqrrjJB97p160xQz+dh73HeZmX4kXr55ZfN5KgnnXQSXnzxRbz55pvo2bOnuY8Tjp522mmmhQ0nVO3YsSPOP/98v8/J6nmOsVGjRmZSUy7ek66WtqjcQK4DiFCcHbZq1aqm/09ZNqYvTE5OjjnDxL4/9mUdbsH3Mycf/Pln4IQT4Grr1nHCBuCaa6wAzm3cvJ0lMrZ1To41rwEn+mboxn7Fbsa5TTgXA/u3n3QSXMet21kiZzuvXWudDOP7hCG1m3EPlCe7N20Cli51598fp7a1k/ugTnPqe/e5rb/91vqP8r33gPPOC9p4JDL/hkvp0XaOHNrWkbudWXHMPt6cpJKTS4aDlStXom3btnjiiScwevRoPPLII6ay3Bt7h1955ZWm6pwTgtaqVcuE3HwMK7z5+R133GEqvRmSe+MEnAzTGVZnZGSYyvTExEQTajO4Zj92hvSjRo0y4fquXbvw8MMPY/r06SbMZ7U628+UlQULFiAlJQUnnniiaTtTmq9V1O9LoPufCtGLoBC9oBUrgOOPB/gePnxyzPXYE/aWW4AffwROPhmu4tbtLJGzrXlyiS1SvvnGCrNC4UqYE08EODH5779bcx+4iVu3s0TGduYeXe/eAOcK4kmxiiWb5yjoV8LwZDf/DrFNnNsoRA8+14Xo/A+Sb6x33wUCqIgS93Pr33ApXdrOkUPbOjJESohu27NnD/7991+0aNHC5zqsGC/q/uJiYP7NN9/g3HPPNROQ5t8/S01NNZX0ZY0xNavl2YfdbSG6/sJIwHjFx8CB1gRlDzyAkMHAn6Hb1VcDaWlOj0bEXeEVW6MMGxYaATrxUvqXXwbWrwfuu8/p0Yi4y+TJVtEs5zYIhQCdjjoKGD3aqpr/7TenRyNSBNUdiYiISJCwLYm/gLw0A3RiBTv7r+cP0InBcr0gBOhupxBdAvbYY1bl58yZ7qv+LApPUk6bBmzZYh2oi4hV0T1oENCgAfDIIwgp7dpZPdw5MSHbuoiI1caF8xpcfz1w+ukIKXfcAXTqZJ2o18lucZvUtChEIRdRF16A1NQA1j+UiqixUWbhbb+yUoHXo6yFt0VERETElRSiS0DYq/T++61e6F26IOS0bm1Vz0+aBMyd6/RoRJzHAJpVnzNmhE7Fqrdbb7XmZOCJAE0cLJHOPilWp07otFrzxslPOZ8Se6PffbfToxHJRxXoIiIiIqIQXQKRmWlVh/GS6zFjELIYuiUlqdJNZPly4N57gREjrFZHoYhtXXgCYPt26+SeSCTjCeJff7WuFKtUCSGpTRtg/HhrHpPvvnN6NCIiIiIiInkpRBe/xo0D/vzTOjgvpDVSSFW68XvYvFmVbhK57JNizZsDDz6IkNaypTUR4fPPA19/7fRoRJw7KXbPPdZJMbdNnl1cN98M9OxpzWFy4IDToxE5TJXoIiIiIqIQXfxZvNjqPcwDdPYrDXVs66JKN4lk7H/O93WozW3gy9Ch1qSoDN327XN6NCLOnBTjnEL8vzrUcQ6T6dOBnTut/u4irqAQXURERETcGKIvW7YMSUlJZibakSNHItfPjuugQYMQFRVVYNmwYYN5bLVq1fJ8/aFwOMoMkkOHrIPzo4+2QvRwYVe6XXWVKt0ksvzxhzU3wKhRoTm3ga/Q7eWXgb17rZZNIpF2pRjf1zwplpCAsNCsmTVnw5QpwJw5To9GRCG6iIiIOGv79u3YsWOH08MQt4XoGRkZ6NevHzp16oSFCxdixYoVmMGmt0V4/vnnsWfPHs/y2WefoVWrVmjUqBHWrFljQnTv+xnMS2AYtq1caR2cx8cjbNiVbrt2Abfd5vRoRIJ7UqxdO2D0aISVJk2sntB8X3/2mdOjEQkOhuesC+BJMc73EU6uvRbo08f6yBNkIo5SiC4iIiJBsmnTJqSmpub52tSpU3HhhRcW+bidO3eiZ8+eJnAnZqtzvCpSEhIScOjQIaxatQrffPON5+vMTVNSUgp9zvr16+Pvv/8+wu8ovLgqROcG3rdvHyZOnIgWLVpg3LhxmDZtWpGPqVChggnK7WXSpEm4//77ERMTgwULFqB79+557i8Xyk29g2jBAqvtA8O2jh0RduxKt5deAj7/3OnRiJQ9tjFi72Selwynk2I2tnP53/+s0G3PHqdHI1L2bVwGDQLatgXuuw9hJyrK+v+ZV4ux17uIk2Kic3EWPsVZx241k1r7Xz8GZ7U6yyy87VdUDFD/LGvhbREREQlrN998c56OGS052ddht912G67mwa2X2bNn46677iryORMTE02IPnjwYPM5s89bbrkFHTp0wNdff41KlSrhxBNPNIXFq1ev9nT9ePDBB9GmTRt8+OGHBZ4zLi7OLMWRnZ2NG264AVWqVDF57bXXXousrCxzn79uIc899xzq1Klj8uDvvPovN23aFB988IHnc2a+5557LpwQCxdZsmQJunXrZn7QxI3NavRAMTRfv349Lr30UvP5/PnzzcKNFB8fj+uuu878gnBD+aqE52Lbv3+/+ZiTk2OWYOLr8Rcs2K9L/BEMGhSFY48F7riDY0BYuuYa4N13o8zHP//MRfXqwR+Dk9tZImdbc2Lghx6KAv/f7dgxfN/TL77I/zeicMstrEp3pnJQ7+nI4PR2ZhuXZcui8NtvuWbS7HD8dWvQAHj8ceC666Jx/vk5OOusyNrW+hviHglx2fgUZwMjXwMSLve/fmwCPr3808BfICYBOKUY64uIiEhIY+eNTz/9FCeccIL5nEXAthdeeAHHHXccnnzySROCz5s3z2SlAwYMKPA8kydP9uSfNGbMGFOR7n1/ly5dEMsDBsA8V3R0tAm17Vx05syZZj0WMB999NE46qijTABuZ6LHHHOMWZcFz5999hnOPPPMIr+3Rx55BIsXL8Zvv/1mKt/POOMMMwaG6Xa3ELbftpU/PFHbF198gdtvvx1vvvkmatWqhSuuuMLkubztJq4K0bmBmrFE+DBuKP4ysQ0Le6T788wzz5gzHvylIJ5d4SUMw4cPx7p168wvF38BvH/JvI0fPx5jx44t8HX+Eh48eBDBPnjiLykP3OzvJ1gmTKiE1asr4ssvd2PPHuuMUbgaPz4avXrVwpAhGXj22eDPSujkdpbI2NasWL3yyppm4sFrr92N5GSELVbY339/edx6a1Wcfvpe9O7930nRYNF7OjI4uZ2XL4/FQw/VxE03paJhw5Swfk+ffTZwyinVcd11sfjuu12oWjU3Yrb1AU3a4r52LmrrIiIiEhqy8rZDyYNXffEEdiDrsnlHbHn/68ZWDHxoWVlYvnw5evToYarD82NoPGvWLJx33nm46qqrTCHwY489lqc1dXJyMho0aIDevXubz9mehW1XmKFefPHF5mssEGaWyVD6sssu87w212ELbAbqDLDZ3oU5Kpd//vkHtWvX9rSEYQX4zz//bPaHWS3fo0cPv9/frl278Prrr6N58+bmc4buDNXJu1tIfjx5MHDgQJxzzjlmv5tZ7vvvv2/CdzdxVYjOsyP5262wb09aWprfEP3ff/81lx889dRTnq959/9hOM9LJngZhK8QfdSoURjhdd0wQ332VudlEfaZmGDhLyl/ufnawTxoW7aMJyOsitWePWsg3NWuDTz9NCvvy+Pyy8sh2FeEOLWdJfic2tZsy7R8eRR+/TUXjRrVRri76Sbgyy9zcccd1bB0afCvMNF7OjI4tZ15UmzkyCi0acNq9AooV866ci+csQVV+/ZReOSR2pg2LTditjX3f8UlFKKLiIiElrcLhtMebJ/mfQXYu7WB7LTC163dE+j9/X+ff9gUyNhVcL3LA99HWLp0qdm/PPbYY7F161bTgmXKlClo3LixZ51TTjkFK1euxI8//mjWf+edd/D555+b3ujMND/++GN07drVU6XNwuMtW7aYPLRPnz4mv/zrr78wdOhQk39eeeWVJgRnlTnxtdg2hRXx//d//4e2bdua1th2V5D8GjZsiM2bN6NiRf8nC/g83hjSX3LJJX67hbDa/vLL/7vir3Pnzvjll1/8huhsc/Poo4/m+RpbwfBnGPYheo0aNbCMKW6+Shz+cP157733cPLJJxcZtvOMCn9JfWGAX1jPdB40ORGG8BcpmK+dnc3LpgG2Y7r3Xn7fhbe9CTdXXgm88w5wyy3RZiKzAP4uhPR2FucEe1tzYmBeXMOT1l27Rsb7mdhLmROo3ntvFF54Ifivr/d0ZHBiOz/xhNWe6bffeOljZLynOXEw5zDh/vPll0fh9NMjY1vr74d7pKZHozZSgGvKIfk8//uJqYdSUftx66R18u3JqBjv5wGsauMBPF2QXKxqNhEREQktbFndunVr00mDIfitt95qwmSG5PnzS1Zzz5gxw1SMc/LPjRs3mvvYdsW7lzgnHeXC1ix2D3MG76wknz59umnDwvYq+TGoZgbLeSmZpXJCU1av8/XsImeG/XxtVrNPnz69WN8rw2w+P0N/f91C8ncm4YkAVsbb2M7G/t7YKeR/nBDtcH90u188x8e+6scffzzKiqtC9KSkJLzE9OMw9jfnJQgM1/15++2381SYp6enm747PMth99iZO3cumvBoTHz2E54/H/j5Z55QQMRgKyhWox99NCdVsCp3RUIdC+ZuvJGXYPE/FkQU9lLme5m90TnnQefOTo9I5Mht2sS5DYBbb42832m+j2fN4iRMnD8nPCdHFhfLzUUaKgKHAn9IWqaPijZffFXAiYiISPFdnOL7vvyTePMEtk/5ihrO+a+Xd0kxDPbub/7888+b8Jghst0Bg21XOF8k27r06tXLfI2V5mwxSLzPH4bf9O6775qKdF8YTA8bNsyE+5UrV0anTp1MWxW2nOHYiEF3cSfyTE1NNVXk7NPOKzr9dQvJ35nE7kriXeHO/urEfvF2X3Wux4UV+6xqZ2ubsuwk4qoyF/bX4S+OfXZj3LhxpscP+6Lv3bvXzPJaGAbmP/zwQ55yfQbnvDyBvyy8RIE/cPblYZ8fKWjPHuC++4CrrgIOz20QUdiu6e67rSq/YsxlK+Ja773HM78AO1xFYlcA7ie0b2991Px8Eg54RUnVqtb/1ZHGPtm9ejVbzjk9Gok4auMiIiISWnhVl6/Fux+6v3W9+6EXte4RYMU527t4V12zopzV1uxd7l3Vzb7nbI8SCE7SyTYsfB5WmrPqnAuDaj6PN2av99xzjylkZquXCy64wFSyM8Tm6zK879u3b7G+LwbzfK7bbrstoG4hLJ72nhQ1f1cSrsvKei75e6pzXVbic55LTspallwVovPMA3v88IfNyxrY49zubcNLC3hmoTC//vqrud9uXG97+eWXzeUOJ510El588UXTUJ/9hqSgBx4ADh0CHn4YER1QsGqXPZV1vCShLD2d/2kC/H/u8FVOEYcTkD/3HCcvAaZNc3o0Ikfm++95xR3w2GO8tBERqWNH66QYr6zxOsYQKXvaKRQREZFSwglCWeBrY8cMtvHjfIx2IDx27FjT7oUZKbG/OScC5QSjDMc58aY/DKCZrbKo2HupV6+epy0K8WtsgcKJPdnh49RTT0XdunVx+umn47777jPPwY/2WALBCvYvv/wSb7zxhqdFIYuf27dvbz7avLuFsDMJP7f98ccfqF+/fkCvx4p3VtAPGTIEZc1V7Vyof//+pjfOokWLzCUKNWvWNF8v6pfktNNO88we641nPb799tsyHW844ImsZ5+12h/UrYuIxWpdzoHQrx8nJoTpjy4SinhFBU/ofvEFItpJJwFXXAHccw/ACckLmfxcxPV4ER7bmHTvzss/gUg/4f/mm9aVY8VsySgiIiIi4riOHTvi3nvvNZ0z2G3jpptuMhN/2pN6snKbrantNi67d+82E3OyLQp7p7/11lu4/vrr8eyzz+YJw23MUtlfvWrVqqbtyauvvprn/m3btnlu87kvvvhi0waF3Tw++OADPHK4vzF7rnMyUobbgwcPDvj7++677zBixAh88sknZiJS9lNndxHvbiE33ngjfvrpJ3My4euvvzaPYyU5v6+rrrrKrM9e8Bx/IIE9TzDw+djBxLvFS9hXott41oOXCtgBupR9BTZPerF/cKRj5S7b2fAAXYVHEop27bL6+jN0Ozz5dkTjyUH+X8oThSKhiKExL8TjPmSkzzXJuePHjAFeecUqABAJCu0QioiISCn5v//7PxOKs2UKJ+vkBJkMxIlzOr7yyiuYMGGC5/Pu3bubQJ2tqRkuM5z+8ccfTcU4K9Rta9euNa1RWI19wgknmELkW265xUzs6b2wups914mV7cxfGc4PGjTIBNgMulnYzECbz8Pe47zNyvBAPP3002ZuS1ays8c6lzPPPNNvtxBOOMoCabawYZcRnmw4//zz/b4eq+c5Rlbys0MJF+9JV8O+El2Ca9EigBPlvvZaZPZNLqzv6rhxANvrs6f0BRc4PSKR4pk40fo4apTTI3EHXh127bVWGwxOicGe0iKhVIXO6uuzzwa6dHF6NO7AQhieKOQJsnyFNSJlQyG6iIiIlCL27uaSHyvQFy9ebCbdfOKJJzB69GhTGc5qdRsn6WSIzup1zivJCUHZDpsV35ygk/NB8vM77rjDhPOcuNMbe6/bIfoDDzxgKtMzMzPRp08fE1xfccUV5vlHjRplwvVdu3bh4YcfNq/Vq1cvU60exeDMh/fff9/nfUV1C+FzciJVTjbK7+XEE0/0vI49iajtfvZ3POx79r0MoqjcQJrpRChOcspLINhEvyxndy0MJxZITk42zfPtHkJlgSd2li0DVq4EYvJNUhzJ2Ed640brZ1OWP5dgbWdxXjC29b//WqHxjTdaIZNYeMVaixbAXXdZVaxlSe/pyBCs7cyQmC2JFi4EOnUqs5cJOS+8YP2dW74caNs2PN/TTu6DOs2p793Xtk5/7T2c+X81gKNaY84f9VA+3xxj+aVnpuPM16yKqzkD5qB8nJ8HZKUD31vr45Q5BScxk1Kn/6sjg7Zz5NC2jtztzEkzORkmQ+eyat/hlD179uDff/9FCx7I+sCK8aLuLy4G5t988w3OPfdcMwFp/v2z1NRU01O9rDGmZtDPPuxFBfbFVdTvS6D7n/oLE8EYEPMkEStWFaDnxQlWeWUML6MXCRVs95CTA4wY4fRI3IXzkbAKnVX6PNEgEgpYIMJqa87ToQA9r6uvBho2tKr0Rcpa+fhsfI9e+H7kp34DdLN+XHl8P+h7s/gN0Imhee/vrUUBuoiIiJg2htX9BuSlGaATK9jZaiZ/gE4MlusFIUB3O4XoEYxtSxo3Zk8mp0fiPgws2LaJLSB0rYaEAvb9fuopKyyuXdvp0bgPq9APHbIqWEVCwVtvAatXl/3VE6GI+/X33mv9jLxaQYqUDXtHUDuEIiIiIhFNIXqEWrPGOvi8806gkAl95fCEq3/+CXz5pdMjEfHvxReBjAzg9tudHok78cTCwIHWBKP8OYm4HU+KnXGGqtB94fuZ72tegSNSphSii4iIuJq6VEuwfk8UokeoyZN5eYh1SbQUjpOLHn+8DtDF/djChSH6pZcCdes6PRr3uvVWYPt24PXXnR6JSNEWLLAW9v0W39Xo/Pm88gqwe7fTo5FwlpoejUQkI/G2K5CaGsD6h1KROCHRLLztV1Yq8G6itfC2iIiIBCTmcF/iQ7zkWMSPtLQ08zHuCCqJY0v8SAlZBw8CM2cCgwYBYTb3Qqni/AXDhlknGtatsyYmFHEjXi2xfr3CYX9at7baNLGly1VXOT0aEd/4O8p2a337Oj0Sd7v+emsOk2nTgDvucHo0Es52IRFIKcb6abuK9wIZxVxfREREzMSTFSpUwM6dO00wqollw0NuKU8syudjgM6JaatVq+Y5+VISCtEjECcTZdXWtdc6PRL3Y2XvbbdZgcbjjzs9GhHfV5Z07Ah07er0SEIjdDvnHGDRIrXJEHfi5LdvvAGMHq1Jv/1JTAQuugh46SWrBVsp7GOLFKRLxEVERFyJASsnu1y/fj02btzo9HCklDD0zsnJMSdFSiNEtzFAr3uEl+4rRI9AU6YAPXtaVZlStPLlrYpVVu5zItb4eKdHJJLXli3Axx8Dzz2nACkQZ50FNGxotb/h30IRt5kxw2rRdM01To8kNFx3HfDqq8D33wO9ejk9GglLCtFFRERcKz4+Hq1atVJLlzCSk5OD3bt3o2bNmqV2dQGvVDiSCnSbQvQIs3q1daD52mtOjyR0sO3NxInAZ58B557r9GhE8po6FahQARgwwOmRhIbYWOvEGOc64MSNPFEm4qasjlc+sbqak2aKfyedBLRpY/0tVIguZUIhuoiIiKsxaE1Qr+KwCtHj4uLMNnVbix53jUbKHC95rlEDOP98p0cSOtq3t9o+sDpQxE1Yrfryy8DllwOVKzs9mtBx5ZXAgQPAhx86PRKRvBYuBNauVc/+4uAVOHxPs1Ud39cipU4huoiIiIgoRI+8wI0TD7JiVSfpil+N/umnwC7N+yQuMm8esHmzFaJL4Fq2BE44wWrTJOImb71lVaCz5ZoEjvs16enAe+85PRIREREREQlXCtEjyG+/Adu2ARdf7PRIQg8vredJCFa6ibjFO+8AdepY7QykeK64AvjqK50YE/fg/zFvvw1ceKHVdkgC17ixdeKBvdFFSls0ctAZC9C58Q4EckVxdFQ0OtfvbBbeDuQVUKOztejQTERERMS1tKcWQd59F+BEtKzAlOJhUMleq2++6fRIRP4L3GbPtgK3UpgfI+KwpRWv0NeJMXHTiW5eWXLJJU6PJDRddhnw3XfAzp1Oj0TCTfm4LCxAFyy47a2A5tEoH1ceC65dYBbe9iu2PPC/BdbC2yIiIiLiSgrRIwTDIoboDI5c1pc/ZDDY4KSsO3Y4PRIRYP58K3DjVRJSfGyZwRNjrPwVcUsrl/r1dWVJSXHib+7raK4DEREREREpC4pTI8SiRcDGjcAFFzg9ktDFExCcwEwH6OIGDH/VyuXI8ATEt9+qclWcl51ttWfi76ROdJcM/x726GFdoSNSJhOLaoJRERERkYimQ7UIwYPKWrWsA0wpmZo1rcDyo4+cHolEOrVyKd3K1c8+c3okEul+/hn45x+1cjlSLBT45htg716nRyLhJO1gNJpiPZqOvQppaQGsn5mGpk82NQtv+5WVBnzY1Fp4W0RERERcSSF6BGBIxMCNgZEmKzsy55wDfP01kJLi9Egkki1YoFYupVW52qUL8PHHTo9EIt0nnwD16gFduzo9ktDWvz+QlQV8+aXTI5Fw24/eiKbYuKdKQMXoubm52Lhvo1l4O4BHAKkbrYW3RURERMSVFKJHgGXLgHXr1MqltA7QMzJ0gC7OmjMHqF5drVxK6z39xRfW+1rEKZ9/DvTpo1YuR6pxY6BDB+ukhEipURsXEREREVGIHhkYECUkAKec4vRIQl+LFkC7dmr/IM6/p3v3ViuX0tCvn3VlCScNFnHCli3Wye7//c/pkYSHs8+2/o9mn3mRUqEQXUREREQUokeGr76yeqEzSJcjx6CDVYM6phIn7NkDzJ8PnHGG0yMJD8ccAzRqZL2nRZzAK5tYgc4TY1I6Ifru3cC8eU6PRMKGdvhERERERCF6+Dt4EPjxRwVupYmX3G/dCixf7vRIJBJ9+601saje06UjKsr6WapFkziFJ3DYm5+TV8uRS0oCqla1JhgVKRUK0UVEREREIXr4++UXK0g//XSnRxI+WNVfvrwqV8UZDHvbtLF6/0rpnRhbscJqqyESTJwEk1eL8XdQSgcnUO/Z0zrhKKVj2bJlSEpKQvXq1TFy5Ei/k2Xy/htuuAE1atRAtWrVMGjQIKSnpwdtvCIiIiIiZUEhepjjwXmdOkD79k6PJHywLQ4P0PmzFQkm5hbsh64q9NJ12mlWRbqq0SXYFiwA9u5VP/TSduqpwNy5gHLbI5eRkYF+/fqhU6dOWLhwIVasWIEZM2YU+ZhZs2Zh1apVWLx4MX766ScsX74c48ePR6iKQi7aYTna1d5l/q/wu35UFNoltjMLbwfyCqjazlp4W0RERERcSSF6mGPQyz6rAe3DS8A4SSur/DMznR6JRJI1a4CNG1W1Wtpq1AA6dwa+/trpkUik4RVN1atbLUikdEP0jAwrSJcjM2fOHOzbtw8TJ05EixYtMG7cOEybNq3Ix8yfPx8XXnghmjRpgvbt2+Pcc8/F2rVrEaoqxGViOY7B8hHTUKFCIOtXwPKhy83C237FVgD6LrcW3hYRERERV4p1egBSdnbtAhYvBm6+2emRhB9Wot91F/D770DXrk6PRiIFK6Xj4qzfPyld/Jm+8YZV7a+TjhIsvLKE7dZiYpweSXg5+mggMdFq6cJAXUpuyZIl6NatGyocTo87dOhgqtGLcvTRR5tq9AsuuAAHDx7Em2++iREjRhRZ7c7Ftn//fvMxJyfHLMHC12IrmgKvmZ1tqo7M14M4HnFgW0tY0XaOHNrWkUHbOXLkOLCtA30thehhjJNqMRBiJbqUrk6dYKqROGmrQnQJZoh+0klAxYpOjyQ85zp4/HFgwwagWTOnRyORIDUVWLgQuOoqp0cSfqKjgV691Be9NDDQbub1R5HtSWJiYrBnzx7TI70wgwcPxgsvvIC6deuaz9kOZuDAgT5fg61exo4dW+DrO3fuNCF8MA+eWHXPg7Zo/hIdVv7AAVTle/bAAaQmJwdtPBL8bS3hRds5cmhbRwZt58iR48C2PnDgQEDrKUQPY2xN0K4d0KCB0yMJP6wGPuEE4IcfgJEjnR6NRAKeGP35Z+CWW5weSXg68UTr408/KUSX4Jg3zxS4mhNjUvpYgX7jjdwhBipXdno0oSs2NhblypXL87WEhASkpaX5DNGfeuopM6Hoxo0bTeg+ZMgQMyHpE088Uej6o0aNylOpzuC+UaNGSExMRJUqVRDMAzaOl6/rfcCWFl8NR2MZMK025t1d029Ll7TMNHSdalVYzBs8z39Ll6w0RH1prZ97xjy1dHFwW0t40XaOHNrWkUHbOXLkOLCtuX8bCIXoYYw9u9m7W8qu/cOECVYIokvxpaz99RewZ89/Ya+Ufl90TsDMq0uuvNLp0Ugk4EkxZpBt2zo9kvAN0fn/M0+MnXWW06MJXTVq1MCyZcsKVOrEx8f7fMxrr72GBx54AI0bN/ZUmvfs2dNniM6QPn9QTzxoCvZBMg/Y8r9uVC6wAkcDyVa7L39D4nOs2LUiz/MVKToK2H94fd5WMODYtpbwo+0cObStI4O2c+SICvK2DvR19JsXpv79F1i50qqWlrJr/8C2nX/+6fRIJFJOivFkjdoHlZ2TT7YCN5Fgheg8KaZjgLLRsiXQsKFauhyppKQkzPWaoXX9+vWmfznD9aKqh5K92p5s374d2TyjEarYG1FEREREIp4O3cKUfbyjEL3sdOnC6imrpYtIMEL0jh2BSpWcHkl4nxhbvZqBj9MjkXCXlWX9P61WLmWHFcO8Gk//Rx+ZHj16mPYq06dPN5+PGzcOvXv3Nn3R9+7dW2g4fvLJJ+ORRx7BjBkzMGXKFAwdOhT9+/d3YPQiIiIiIqVHIXqY+vVXoE4d9fYtS2yZ1K2b1f5BJBghulq5lH0lOqkaXcoar2BKSdF7uqx17w788QeQnu70SEK7J/rUqVMxbNgw1KpVCx9++CEeffRRcx97oi9durTAYx566CF0794dd9xxB4YPH4727dubPukhS5XoIiIiIqKe6OEdorMKnZVYUraVq88/b036qEvypazs2AGsXavArazVrw80b26F6Bdd5PRoJNxbubCldOfOTo8kvPFEN6v+Fy/WlXlHglXk69atw6JFi9CtWzfUrFnTfD3XR7jMSUVfeeUVhA2F6CIiIiKiSvTwlJkJzJ+vA8Zghei7d1uTPoqU5UkxUogenPe0ri6RYIToSUnWFU1SdjhZMH/Gv/3m9EhCX926ddG3b19PgB5RFKKLiIiIiEL08L1MPC1NIXowMAShBQucHomEeyuXxo2tSfKk7Fu68G/ovn1Oj0TCOY9jiK5+6GUvLs6q9p83z+mRSCiLQi6aYAOaVN0b0BWeUVFRaFK1iVl4O5BXQMUm1sLbIiIiIuJKrgvRly1bhqSkJNNnceTIkT4vFfXWoUMHs5NqL4MHD/bcN3v2bDRp0gT169fHG2+8gUipWuVl4scf7/RIwl/VqkDr1grRpWypH3rwdO1qhZy//+70SCRcrV8P/POPQvRgtnRRJbociQpxmdiAZtgwfBIqVAhk/QrYcMsGs/C2X7EVgHM2WAtvi4iIiIgruSpEz8jIQL9+/dCpUycsXLgQK1aswIwZM4p8TFpamunTmJycjD179pjlmWee8QTyAwYMwH333YcvvvgCo0ePxqpVqxAJITorr3SZeHB06WK1zxEpC5wQb9EihejB0qYNULEisHCh0yORcDV37n+TXkpwQvRNm4Bt25weiYQsu6BHbV1EREREIpqrQvQ5c+Zg3759mDhxIlq0aIFx48Zh2rRpRT5m8eLFphI9MTHRTGTEpXz58ua+qVOnolevXqYyvX379hg2bBhmzZqFSJlUVILX0mXJEuDQIadHIuGIFdGc50CBW3DExFhX8ejqEikrPCnWrBkQia2lnbq6hNTSRUpMIbqIiIiIuC1EX7JkCbp164YKh6+VZDjOavSizJ8/H1u2bPGE6DfccIOpaLef79RTT/Ws26VLFyzi0WsYY6UVK64UuAU3RGeAzj7KIqWNf7LYnumYY5weSeTglTyqRJeyfE936uT0KCIH55Jo0EAtXaTk0jNjkYT5SJo2xFwd5n/9dCS9lGQW3vYrKx34PMlaeFtEREREXCkWLrJ//340Y3nWYexvHhMTY1q0sEd6Ydie5aSTTsL999+PvXv3mvYtkyZNwl133VXg+apUqYJtRVzPy/DdDuDt8VBOTo5Zgomvx37wxX1dK/iJxvHHc8xlNTrx1qEDEBsbhXnzcovdh76k21lCT0m39aJFUYd/x/jYMhueeGHAOWlSNJKTc1CrVvEeq/d0ZCjpdubqv/8ehXvu0fs5mLp2jTIhek5O8SuJnXpP62+Ie3BTLEQS8I912+/6uTlYuG2h53YAjwD+tc/caruLiIiIuJWrQvTY2FiUK1cuz9cSEhJM33NfIfrkyZPzfM6+508//bQJ0fM/n/1cvowfPx5jx44t8PWdO3fi4MGDCPbBE1vb8MAtOjrwCwZ+/rkiqleviHLlkpGcXKZDFC9t29bETz9l4oILrBMvZb2dJfSUdFvPn18TSUmZSE4u3u+WlFyzZjEAEvHNN3vRq1fx+jTpPR0ZSrqd16yJQUpKIpo334PkZPUAC5ZjjqmACRMqYdu2ZMQWc8/Xqff0gQMHgvZa4ofauIiIiIiI20L0GjVqmMlA8x9ExLOXQYBq166NrVu3ep6PAXigzzVq1CiMGDEiTyV6o0aNTKsYVrEHEw/aWInP1y7OQduqVVGmirJOndplOj7Jq3v3KPzySyxq104IynaW0FOSbc1zfqtXR2H48Jhi/25JybH6vGrVXKxdWw2XXFK8x+o9HRlKup2/+sr6eOqp1VCjRtmNT/JiZ78HHojGzp210bFjaLynWfghLqEQXURERETcFqInJSXhpZde8ny+fv16016FYbgv3bt3x9tvv23Cbpo7dy6aNGnieT5+fs0113gmIW3Axpg+sGo9fyU88aDJiTCEB23Ffe3Fi4HLL+eYo8p0bJJXly6cyJahZxQqVSr77SyhqbjbmucUeel4UhIfV+bDk8P4s2ZfdLbSKcnPXe/pyFCS7cyJgps354ka/W4Ek91q7c8/o3HccaHxntbfDxdRiC4iIiIibptYtEePHqb6e/r06ebzcePGoXfv3qYvOvudZ2dnF3jM0UcfjSFDhmDevHmYOXMmnnjiCTO5KF1wwQV48803sXTpUqSkpJg2L3369EG4YtH95s0o0QGiHHmIbvW6dXokEm4TEMbFaVJRJzBEX7DA6VFIuNGkos6oXBlo2RL44w+nRyIhSSG6iIiIiLgtRGcP86lTp2LYsGGoVasWPvzwQzz66KPmPvZEZxie3+OPP26qx3v16oUxY8ZgwoQJGDhwoLmvY8eOGD58ODp37mwq0BnGDx06FOGKVehU3Mkt5ci1bQtUqKDQTUoXT8owQC/kAhkpY0lJAOehLmIuapFiYR0A/59WiO6MY4/9bz9JpFgUoouIiIiI29q5UP/+/bFu3TosWrQI3bp1Q82aNc3XOaFTYapVq4b333/f5/M9/PDDGDBggOmT3rNnz2L1Vw/FwI3VVi1aOD2SyMOJyngFAKsMRUoLf59YES3BZ//cuQ3q13d6NBIOVq8GUlL0nnYyRJ8wwcpDo9TxToqpFnYC5csDCKxnX60KtYr3AuWKub6IiIiIBJ3rQnSqW7cu+vbtW2rP165dO7OEO4boDHLVRtMZnKzshx+cHoWEi4MHgeXLgSFDnB5JZGrcGEhMBBYuBPr1c3o0Eg7sk6y6WswZ3D/atw/YuBFo2tTp0UgoqRh3CDtRGxg2Eqj4mP/14yti58idgb9AbEXggmKsLyIiIiKOUNwaRniZsg7OndOhA/DXX0BGhtMjkXDA7lVZWWr94BRWqvLE2J9/Oj0SCacQnZOKVq/u9EgitxKd1Bddis2+GlZtXUREREQimkL0MMHqqrVrFaI7HaKz5+2KFU6PRMLlypKYGKB9e6dHErn4sy9kKg6REr+ndVLMOfXqWVeXKESXYlOILiIiIiIK0cOHfVCoEN05dtipylUprcCNXahMC1Zx7MTYunVWH2uRI8HsjSdk+Dslzl1dwmp0hehSXOmHYnAKvsMpb12P9PQA1s9MxykzTjELb/uVlQ58fYq18LaIiIiIuJIre6JLyQK3hASgdWunRxK5KlWyJnVViC6lgb9HdvsBcYYdeC5bBnTr5vRoJJRt2wbs2aMrS5zGv6lvv+30KCTU5OQAP+AUYIt12+/6uTn4YeMPntsBPAJItifVCWR9EREREXGCKtHDxJIl1sF5rE6LOB66cVuIHAkepDO4PeYYp0cS2XglACdqVksXOVL275BCdOdDdE4syhMaIgFTGxcRERERUYgePhS4uQMnImSIruMtORKbNlktRPSedpZ9dY+uLpHSCNErVgSaNnV6JJHtuOOsj2rpIsWinToRERERUYgePlWrnMxSgZs7KtF37QJ27HB6JBLqJ8VIVavO4zZQiC6lEaLz/2he2SDOOeooa54JhegiIiIiIlJcOpwLA+vXw0x0pBDdPT2UFbrJkQZuVaoADRs6PRLhe5rvZxUiypG+p3VSzHkxMda+kkJ0KRb9ByAiIiIiCtHDq2pVIbrzmjWzJhhVX3QpjfZMUVFOj0QYou/dC2zd6vRIJFRlZQErVypEdwv+beXVeyIBU4guIiIiIgrRwydwq14dqFfP6ZEIL9VX+wcpjfe0Ajd30NUlcqTWrAEyMvSedoujj7ZCdLbCEwlIbi4qIBUVYjMCfkiFuApmCVhMBWsREREREddSiB4GVLXqzvYPIiWRmWlVrerKEndo3NhqraP3tJSU5jhwX4ielgZs3Oj0SCRUVIw7hFRUQuqQ28wEwX7Xj6+I1LtTzcLbfsVWBC5JtRbeFhERERFXUogeRiG6uEPHjlYIeuiQ0yORUK1aZZCuwM0deHKSJ8bY01qkJPi7U7cuUKuW0yMRatfO+qiWLlLsdi5q6yIiIiIS0RSihzgGtX/9pRDdbQfoDEHXrnV6JBKK7LCW1ZLiDmrRJEdCk4q6S6NGQOXKwPLlTo9EQoZCdBERERFRiB4eVauctEwhuvuq3FiNLlKSK0tUteouDEB5spInx0SKSyG6+64u4f/TCtElUAczY9AXn6Dvpzfg4MEA1s86iL6v9zULb/uVfRD4vq+18LaIiIiIuFKs0wOQI2MfBKpq1T0SE4GaNa1LxS+4wOnRSKjRpKLu07atdbJy3TqgTRunRyOhJDUV+PtvvafdhvtMS5Y4PQoJFdk5UfgMfYFNQHZ2IOtn47M1n3lu+5WbDWz77L/bIiIiIuJKqkQPk6pVhrbirtBNlehS0qpVXVnivvcz6T0txcWTqewAofe0u7ASne/nnBynRyIhQW1cREREREQheujTpKLuPUDXpGVSXGlpVtWq3tPuUrs2UL26QnQpPrYBIl3B4L5KdP693bjR6ZFISFCILiIiIiIK0UOfQnT3Vq6uWhXYZb8iNv7O8Fhd7Znc10NZV5dISfB3hhNZVqrk9EjEm/03Vn3RJSAK0UVEREREIXpo4+RG7NGrwM19GLhx+6jKTUpStdq6tdMjkfwUoktJ39OqQnefhg2BypUVoouIiIiISOAUooewNWusfp46QHdnOxdSSxcpbuDGOQ6qVXN6JJIf/85y+6iHshQHf2fsnvrirqtL1HZNAqZKdBERERFRiB76rR9IIbo7q9x4+b4qV6U4VLXqXgxCU1OBLVucHomEisxM62S33tPuxKv4VIkuAVGILiIiIiIK0UM/cKtZE6hVy+mRiK8eyqpyk+JQiO5edjWxToxJoDhJcFaW3tNuDtH5ftbVJeJPxdgM5CIKuVddjYoVA1g/viJyx+Sahbf9iq0IXJ5rLbwtIiIiIq6kED3EAzf1TnYv9VCW4uAktKtXK3BzqyZNgIQEvaclcPbvitq5uBO3S1oasGmT0yORkKGKdBEREZGIphA9xNu5KHBzf4iuYy4JBIMcTkar97Q7xcRYJy0VoktxTnRXrQrUqeP0SKQwdhGC3RpPxCftyImIiIiIQvTQ3p9X6wd346Rl+/cD27Y5PRIJBXw/k97T7qWrS6Q4+LvC9zPbe4k7ry4pV04huvh3MDMGF+FtXPTdUHOy2+/6WQdx0TsXmYW3/co+CPx0kbXwtoiIiIi4kkL0EMVgNiVFgZubqYeyFDdEL18eaNTI6ZGILwrRpbjvabVycffVJS1bWm20RIqSnROF2bgIszcmmdZr/tfPxuwVs83C237lZgObZ1sLb4uIiIiIKylED/GqVfVEd69mzYD4eE0uKsWb4yBaf5Vdi4Horl3WIlIUXS0WGvg3V5Xo4pfauYiIiIiIQvTQxYO+uDgrqBV3io0FWrVSlZsUr/WDuJeuLpFA/fOP1c5L72l3U4guAVGILiIiIiIK0UMXK9x4GTKDdHGvo45SiC6BUdWq+/GkGK8UUIgugV4tpnYu7v8/evNmIDXV6ZGIqylEFxERERGF6KHf+kHcTSG6BGL3bmDnToXobsdJCJs2BdascXok4nY80aKrxdzP3o9au9bpkYirKUQXEREREYXooYuXHytwC40QfdMmID3d6ZGIm9ntBPSedj+dGJNA6Gqx0ArR1dJFRERERET8UYgegnjZMYNZBW6hEbixgGndOqdHIm4P3KKirN8XcTfNcyCB4O+IrhZzvxo1gFq1FKKLH6pEFxERERGF6KHJDnB0gO5+diiq0E38hehNmgDlyzs9EgnkPc2TYtnZTo9E3Ix/83nCRULjPa0QXYpSISYDKaiIlAsHoUKFANaPq4CUUSlm4W2/YioAF6dYC2+LiIiIiCspRA9B9sGeQnT3S0wEqlZViC5FY49tVaGHBm6nzExg40anRyJulZFhXS2mED00cF9KIboUJQq5qIg0VIw5aK4a87t+VBQqxlc0S1RgDwBiK1pLIOuLiIiIiCMUoocgHuwxnK1e3emRiD92iw5NRChF4e8H+yeL+9nBqN7T4svffwM5OToxFkohOk90q2OH+GT/cuiXRERERCSiKUQPQQxvVOEWOtRDWYrCsI3tQfSeDg2NGwPx8XpPi2/2CRa9p0MnRN+/H9ixw+mRiFtlZMVgEKZj0LwbzJUm/tfPwKAPBpmFt/3KzgDmDrIW3hYRERERV3JdiL5s2TIkJSWhevXqGDlyJHIDqPoYO3YsatSogXLlyuG8887DgQMHPPd16NDBXEppL4MHD0aoW7tWB+ehhNWICtzEly1bgIMH9Z4OFTEx1lUDek+LL/zdqFgRqFfP6ZFIIOwrBtTSRXzJyo7CTAzCzI2nICsrgPVzsjBzyUyz8LZfuVnA+pnWwtsiIiIi4kquCtEzMjLQr18/dOrUCQsXLsSKFSswY8aMIh/z2muvmeXzzz/H8uXLsXLlSjzyyCPmvrS0NKxbtw7JycnYs2ePWZ555hmEOlWih94BenIysHev0yMRN1LVaujhtlI7F/HXnkmtjUNDixZAdLRCdCmC2riIiIiIiNtC9Dlz5mDfvn2YOHEiWrRogXHjxmHatGlFPmbz5s2YOXMmunTpgpYtW+KSSy7B4sWLzX38yEr0xMREVKtWzSzly5dHKPv3X2tR4BZ6VW4K3aQw/L1gdXOzZk6PRAKlq0ukKJooOLSUK2f9/dV7WkREREREihILF1myZAm6deuGChUqmM8ZgLMavSh33XVXns9XrVqFVocT5vnz52PLli0mRM/MzMRll12GJ5980rR98VUJz8W2n00yTc/iHLMEE1+PrWzyv65VKRWN5s05pqAOSY6gyo3b7K+/ctCpU2DbWcKPr229enUUmjZlkM77HBueFPM9vXFjFNLTc00A503v6chQ1HZesyYK3bpxHVWvhooWLaJMq7zCtplT72n9DXERVaKLiIiIiNtCdIbWzbzKMdnDPCYmxrRhYY90f1avXo33338fv//+uydQP+mkk3D//fdj7969GDBgACZNmlQgeLeNHz/e9FfPb+fOnTjIpsVBPnhiVT4P3KJ5nfFhv/+eAIBV9TuRnKyd+lBRu3Yi/vgjHaefnhLQdpbw42tbL19eDY0bRyE5eY+j45PAJSbGISenJhYs2IWjjsrOc5/e05HB13ZOS+M8B3VRp84+JCenOzpGCVyDBpUxd248kpN3F7jPqfe09/w+4jCF6CIiIiLithA9Nja2QJV4QkKC6W3uL0TnQc7VV19tJg49+uijzdcmT56cZ53Ro0fj6aef9hmijxo1CiNGjMgT6jdq1MhUslepUgXBxO+HJxH42t4HbcnJUahTJxfNmycGdTxyZNq0icLWrRVRu7Z1lYW/7Szhx9e23rQpCr1780RLbUfHJ4Hr2tX6uHt3TeTfbHpPRwZf2/nPP62Pxx9fGbVrV3ZugFIsxxwDvPkmt2ftAr3snXpPc/9XXEIhuoiIiIi4LUSvUaMGli1bVqASJz4+3u9jH3zwQfz777+YMGGCz3UYUm3dutXn/QzwC2v1woMmJ8IQHrTlf21ebsxuNdHRmrEslLA/Llv1F7bdCtvOEp7yb+vsbODvv63fD72nQ0f9+kClSsC6ddyWBe/XezoyFLad162zPrZpU/jvhrgT96vS04EdO6LM+9sN72n9/XARhegiIiIi4raJRZOSkjB37lzP5+vXrzc9yhmuF+Xjjz82k5G+++67nn7q1L17dzPxqI3P3aRJE4QyhugtWzo9CinJATonLdNxmHjbtAk4dEgTBYcaVqra72mR/JOKVq0K1Krl9EikOOz9Ku5jieRXISYDyUhEcp8r4HWY4Xv9uApIvj3ZLLztV0wF4Pxka+FtEREREXElV4XoPXr0MC1Upk+fbj4fN24cevfubfqis6d5Nss281m5cqWZMPSZZ54xrVdSUlJM+xdiW5chQ4Zg3rx5mDlzJp544gnccMMNCPUDdAVuoXmAzvamu3Y5PRJx2/uZ9J4OPbx6wN5+Ivn/j87fEkTcjdPxcJspRJfCRCEXidiFxPh9Ab23TfufiolmiQrsAUBCorXoj4eIiIiIa0W7rSf61KlTMWzYMNSqVQsffvghHn30UXMfe6IvXbq0wGOmTJmC1NRUDBw4EJUrVzZLu3btzH2PP/64ac/Sq1cvjBkzxrR64XqhavduYM8eBW6hqEUL66MO0MUbfx9iY4EQv0AmYk+MKUSX/HSiOzSx/XjDhvo/WnywLyPU5YQiIiIiEc1VPdGpf//+WLduHRYtWoRu3bqhZs2a5uu5PnZcJ02aZJbCVKtWDe+//z7ChapWQ1fz5v/1y+3e3enRiJve0/zdYJAuoXdijFNssI9y+fJOj0bcgi1+Tj3V6VFISU+M2T3tRbxlZMdiBJ4FljbFxAzOoeRn/awMjPhihLk9sc9ElIv184DsDOB3a30cPxGI8bO+iIiIiDjCVZXotrp166Jv376eAF0sdoWUXdUsoaNyZaBOHR2gS16qWg39Hsrr1zs9EnELtuzasUPv6VB+T6sSvXDLli0z8xbxqtCRI0f6LGzJLycnByeccIJppxjKsnKi8TxuxPMb+yIrK5D1s/D8wufNwtt+5WYBa563Ft4WEREREVdyZYguvgO3unWtQFZCD09+6ABdvClED132yUydGBPb339bH3WiO7RDdHXsyCsjIwP9+vVDp06dsHDhQqxYsQIzZswI6LGTJ0/Gvn37cPPNNyOk6ZdCRERERBSihxYFbqGNwYoCN7Gxmo1VzHpPh6Z69aw2LnpPi83+XVCIHroh+v79mgA8vzlz5pggfOLEiWjRogXGjRuHadOm+X3ctm3bcPfdd+OZZ55BXFwcQppCdBERERFxY0908Y0VUu3bOz0KKSkGK1984fQoxC22bAEyMxW4haqoKKufva4uEe9K9EqVgFq1nB6JHOnVJYmJTo/GPZYsWWLmKKpQoYL5vEOHDqYa3Z9bbrkFTZo0webNm/Hrr7+ati5FVbtzse3n2YzD7WC4BAtfi61q8r9mrtfn1pj8P0/e9f0+wFPVZNYN4vccqXxtawkv2s6RQ9s6Mmg7R44cB7Z1oK+lED2EMKw57zynRyFHcoCenGz1zVVLHlHVaujT1SXijb8L/J3gCRYJPfbfYu5rdevm9Gjcg4F2s2bNPJ9HRUUhJiYGe/bsMT3SCzN37ly88847OOuss7Bu3To89NBD6NOnD5599tlC1x8/fjzGjh1b4Os7d+7EwYMHEcyDJ1bd86AtOvq/i3VjvcbAMaWmFl2ZnpaZlnf9uNQi14/KTkMdr/VzY4peX8puW0t40XaOHNrWkUHbOXLkOLCtDzCoC4BC9BCxZ4+1KHAL/YkIGbQce6zToxE3VK3y/4PGjZ0eiZQU/x5/8onToxA3vad5dYKEJl5FwHlndHVJXrGxsShXrlyeryUkJCAtLc1niP7SSy+ha9eu+OSTT0zofu2115qq9JtuugmtW7cusP6oUaMwYsSIPMF9o0aNkJiYiCpVqiCYB2wcL1/X+4AtLT7ec5v3VaxY9POkHkrNu368nwdk5V0fsX7WlzLb1hJetJ0jh7Z1ZNB2jhw5Dmxr7t8GQiF6iE1YpgP08LhUXCG68D3dqBHgdWwuIfie3rAByM4GYmKcHo04jX/bzz3X6VFIaUwuKv+pUaMGli1bVqBSJ76I/7y2bNliqtB58EN2IM6q9MJCdIb0+YN64kFTsA+SOeb8r2t/H/+Nqejn8H5sQN9DvvX9voCU2baW8KPtHDm0rSODtnPkiArytg70dRSihwi1fgh97JPLNi5q/yDerR8kdHH7sa/95s1A06ZOj0acnih440a9p0Mdt9+qVU6Pwl2SkpJMZblt/fr1pn85w3VfGjZsiPT0dM/nKSkp+Pfff9GgQQOEovLRGViPpsBJJ6N8+Vn+148rj/XD13tu+xVTHui//r/bIiIiIuJKOn0TQlWr1aoBPq6clRDAQib1UBabWj+E19UlEtk4UTCDdL2nQ5sq0Qvq0aOHaa8yffp08/m4cePQu3dv0xd97969yOalOPlcdtllJnj/5ptvsHHjRgwdOhRt2rQxk5KGomjkoCk2omnC9oCKxKOjotG0WlOz8LZfXKdSU2sJZH0RERERcYT21EKEqlbDgw7QxaYQPfQ1aWJdda8QXXS1WPj8H71rF7B3r9MjcVdP9KlTp2LYsGGoVasWPvzwQzz66KPmPvZEX7p0aYHHnH766WadG264wYTna9aswezZs/O0RQkpubl5P4qIiIhIRFI7lxChwC08MGBZsMDpUYjTNFFweGBLYE4MqxBdNFFw+E0A3qmT06Nxj/79+5t+5osWLUK3bt1Qs2ZN8/XcIkLla665xizh4FB2DO7BY8Caxnj4kP+5TA5lH8I939xjbj982sOIj/HzgOxDwJ/W+ujwMOBvfRERERFxhCrRQ4RC9PDA0JT9kw8dcnok4iRNFBw+1KJJiL8DDNA1UXBoU4sm3+rWrYu+fft6AvRIkpkdjccxEo9vusTMg+F//Uw8Pvdxs/C2X7mZwMrHrYW3RURERMSVFKKHAAaumzapajUccBvm5AAbNjg9EnGSQvTwqlxV4CY60R0eOO8M58tU2zXJQ21cREREREQhemhggM7gVQfooc8+EaID9MjG0JUTBTOskfCoRFfGEtn4O6D/o8OD5i6RAvQHXkREREQUoocGTVgWPho2tC73V+VqZFPVavjg3+UDB6zJCCWy39P6Pzo8KEQXEREREZHCKEQPkYPz2FgrgJXQFhMDNGumED3SqWo1fKiHsnCS4L179Z4OF5rnQApQJbqIiIiIKEQPDTyYa9rUCtIl9OkAXVS1Gj7s4FTv6cilq8XCrxJ92zYgNdXpkYhrKEQXEREREYXooUGtH8ILgxZdKh65MjOteQ70ng4PlSsDtWsrRI9kmig4/EJ07+0qohBdREREREi1zSGA4cyJJzo9CinNA/QpU6zJYiXybNxobXtVrYYPnRiLbPw/unp1a5HQZ/9tZojevr3ToxE3KB+dgWU4GjjueJQvP8v/+nHlseyGZZ7bfsWUB85a9t9tEREREXElheghUPzCA7krrnB6JFKaB+gZGcDWrUCDBk6PRoJNVavhRy2aIpuuFgsvvLKkQgVVost/opGDo7ECKF8toGt4o6OicXTtowN/gahooFox1hcRERERR6idi8vt2gWkpKhqNZxoIsLIZk8U3KiR0yOR0qIQPbJx2+v/6PARFWWdFFGILgXauaiti4iIiEhEU4jucnYwoyq38NGsmXWQrvYPkenvv6PQpIkmCg4nDFB37LBOeErkUSV6+OH21IkxsR3KjsH9GIP7N12NQ4cCWf8Q7v/+frPwtl9c58/7rSWQ9UVERETEEQrRXU6tH8JPuXJWFbIO0COTArfw7qEskYWB2ubNek+HG1Wii7fMnBiMxf0Yu3WwmRzc7/rZmRj7w1iz8LZfuZnAsrHWwtsiIiIi4koK0V2OB3GJiUDlyk6PREqT2j9ELoXo4UctmiKXJgoOT/wbvX69JgCXw9TGRUREREQUoodG6wcFbuFHVW6RPVGwArfwm4iwUiWF6JFIV4uFJ/6N5lUG27Y5PRIREREREXELhegup8AtPClEj0z//huFAwd0YizccI4D/p3WezryaKLg8GT/jdZ7WgxVoouIiIiIQnT3U+uH8MTAbc8ea5HIsXGjNZuo3tPhRxMRRu7VYk2bAjExTo9EShO3KSlEF0MhuoiIiIgoRHe39HRg69YoVaKHIVW5RaaNG62kTSF6+NHVJZGJJ070f3T4SUgAGjTQe1oOU4guIiIiIgrR3W3zZgVu4UoheuSG6DVr5qJqVadHIqWNQeqGDUBWltMjkWDi5JP6Pzo86eoS8VCILiIiIiJlEaLv3bu3tJ8yYm3aZLV+UJVb+KlRAyZIZQAjkRWiK3ALT9yuDNC3bHF6JBLsiYL1ng5PkXJ1ifbb/UuIysB8JGH+Uf9nrlLwu35sAuYPnm8W3vYrOgHoM99aeFtEREREQjtEv/fee5GdnV3kOpmZmWjXrl1pjEvAqsYYlCuXi3r1nB6JlMVEhFaVW5TTQ5Eg90RX4Bae7JOdkRC6iWX37mikpKjlWrgK9RBd++2lJyYqB0lYiKQKywOa/yAmOgZJDZLMwtt+cZ2aSdYSyPoiIiIi4gir1DkAs2bNwgMPPIDnnnsOO3fuRHR0wfw9NzcX5cqVK+0xItKrVgv5UUsY4LZVJXrkvadPOcXpUUhZaNzY+lvN9g/HHOP0aCQYNMdBeOPJkeRkICUFIUn77aVI7VxEREREpDiV6DExMWYHfMaMGShfvjyeffZZs+M9efJkz8eEhAREscRWSu0AvVkzp0chZXmAHspVblI8Bw8C//wTjWbNdDAejuLjgUaNeGJM/wdG0tVipBA9PNnbNVRPdmu/vfQcyo7BBNyOCTuuxKFDgax/CBN+mWAW3vaL66yYYC2BrC8iIiIi7g3RN3C2tMO4s33nnXeiatWq5mPNmjXzfGRVi5QO9U8Ob9y2mzbxcmqnRyLBsHEji9nU+iGccdtqIsLI+j86MTEXlSs7PRIpC6E8Abj220tXZk4M7sAE3PHPrQHts2VmZ+KOr+8wC2/7lZsJ/HGHtfC2iIiIiIRmO5f9+/ejefPmZie8fv36ngmI7MqV/B+ldOTkWBOLtmjBgxv9bMM1cMvOjsLWrTFo0MDp0UhZs8NVnRgLX9y2ixc7PQoJ5hwHOikWvmrXBipUsCrRu3dHyNB+exnQiQYRERERCaQSvXLlyli9ejUaNGiAd955B414vbqUue3b2f4hSu1cwpgdptp9dSW8MYiJj8/VCZMwphZNkUUt18JbqE4Arv32MqAQXUREREQCCdFZqdKyZUvExsbixBNPRJUqVXD33Xdj165d5uP27dvzfCyNypZly5YhKSkJ1atXx8iRIwO61HT27Nlo0qSJqbp544038tzHSZXq1KljKnO+/fZbhFLVqqrcwhePa2Nicj19dSW8/f13FBo1ykaMNnfYYuC2Z08U9u4NrdBNSmbTJrVcC3d9+wJNmoRWgOrEfnvYU4guIiIiIsWZWDQzMxPZ2dkYNGgQ4uLiMHz4cDMx0Y033pjnYw77kByBjIwM9OvXD506dcLChQuxYsUKMymSv9B9wIABuO+++/DFF19g9OjRWLVqlbmPn99+++2YMmUKXn31VQwePBi7d++G29nVjKpyC19xcTw4t4IYCX88Mda4cbbTw5AypKtLIkd6OicKZoiucC2cPfIIcPvtTo+iZIK13x4RFKKLiIiISCA90enZZ5811dzR0dFmp7uoHfYXXnjhiAY0Z84c7Nu3DxMnTkSFChUwbtw485pXXXWVz8dMnToVvXr1MgE5DRs2DLNmzcJDDz1kxjNw4ECcc8455j5+fP/99z3rurlqtW7dbJQvrwqhcMaTJOyrK5HRziUpKYunT5weipQR+8ohvafDnz1voyrRxY2Cud8uIiIiIhIp/B7p79mzx+yM8zLQq6++GnXr1vW5blZWFoYMGXJEA1qyZAm6detmAnTq0KGDqUb395gzzzzT83mXLl3wwAMPeO67/PLL89z3448/hkCIbletKowJZwxg5s5V1WokFLHxPX3BBapED2fVqwPVquWqEj0CqOWauFWw99sjgirRRURERCSQhJZ9yf/66y/Mnz8fDz74IB5//HGzQ37BBRcUWJeXjbKq5Ujs378fzbx6mLBXY0xMjDko4FgCeQz7P27bts3vfYW1kuHi/bzES12DfbkrK9GbNs1CTk7AHXckBLEVwJtvxiA7W5dTh/tEwWlp0WjcmO9pbetw1rx5lAnRtZ3D27p1uShXLhd16nD/wOnRSFnie5lz8wT7PV3S1wv2fnskSIjKwHc4BWjUGAkJr/hfPzYB3w38znPbr+gE4LTv/rstIiIiIq4UcJkzK7g//vhjfP3117juuuvw559/4tNPP0WlSpVKd0CxsaZHo7eEhASkpaX5DNHzP8Ze3999+Y0fPx5jx44t8PWdO3fi4MGDCKbbbotFTs4BJCeXM5fjSniqUSMeBw7UwKpVO1Crllr3hKtFi9jCpSZq1tyL5OSDek+Hsfr1q2Lt2lwkJydrO4ex5csroUGDOOzatVvbOcwxzGabQQbpwdzWBw4cOKLHB2u/PRLEIBun4AegQmt+4n/96Bic0vSUwF8gOgaoU4z1RURERMS9IfrWrVvRoEEDc7t3795YvHgxvvvuuzLZEa9Ro4aZKDT/gUR8fHyRj2HQXdj6Rd2X36hRozBixIg8leiNGjVCYmKiqWAPpnPPzcHOnZnmtXWAHr6OPdaqNNu/PxHt2mk7h6t//7U+Hn10BdSuXUvv6TDWti3biOWgdu3a2s5hfnVJ8+aHtJ0jJETnVZHB3h9j0UdJBXO/PaLauaiti4iIiEhE8xuiswI7KSnJtEDZsWOH+Rov/WQPcrZJ4f08uCBW6fA+9mEsKb7WSy+95Pl8/fr1psUKw/CiHjN37lxcc8015nMeLNgHD/Z9p512WoH78mPFev4qeOJBkxMHyfy5OvXaEhytWlkfN2yIxgknaDuH8ySEtWvngvmF3tPhrUWLHGzdGoOsrFwkJGg7h6v163PRvXs2oqPj9X6OAE7sj5X0tYK93x4JMrOjMQVDgT21cV0mEOdnfvDM7ExMWTTF3L6u03WIi/HzgJxMYK21PlpeB0RrAnIRERERN4oOpBLGDpbPPvtsnHvuuejcuTPmzZuH8uXL47fffjNV2gyqq1atikWLFh3RgHr06GEqwKdPn24+HzdunKmiYV/0vXv3mv6N+bHP45tvvomlS5ciJSUFTz/9NPr06WPuu/DCC/H888+bqhweTEybNs1zn4jTqlZl/9IcrF/v9EikLHFSUU1AGDmTBefkRGHTJqdHImWFrar5nm7SRBMFi/sEe789EhzKicUwPIdhO8fg0KEA1s8+hGFzhpmFt/3KOQQsHGYtvC0iIiIirhRQmYtdsULc6e7Xr5+nN2TlypVNwG1/bNKkyRENiD3Mp06dimHDhqFWrVr48MMP8eijj5r72BOdQXl+HTt2xPDhw81BAqvMOY6hQ4ea+zhWVqG3atXKVOAcd9xxOP/8849ojCKlqXHjbKxbp37o4WzdOsBrfmMJY/bJEoasEr6tXA4ejFKILq4VzP32iKA2LiIiIiJSnIlF8++UEyu7GWBzAjX7Y2no378/1q1bZ6pjunXrhpo1a5qv8wDAl4cffhgDBgwwFec9e/b09D3nmGfNmoWbb74Zqamp5r7834eIk5o2zcL69cV6K0qIYaDaq5fTo5BgaNiQJ4NzzYkTCU/2tm3SJMvpoYi4Yr9dRERERCQSRB/JTnm9evWwefNmtG7d2nw86qijSm1gdevWRd++fT0BeiDatWuH008/vdCJQ9kf8pRTTlGALq6sRFfVavhKSwP++YdtPlTJFgliYxmkZ2P9ev1fE67sv9f82y0SCoKx3x7WVIkuIiIiIoGG6HYFOHfCednnW2+9ZW6np6fjo48+Mj3M+fHAgQP4+OOPcSiQhoEiYrAlwObNQEaG0yORsmD3u2evbImc97Qq0cM7RK9fPxflyzs9EpHCab+9lClEFxEREZFA27nUqVPHfOSkRN66dOmC119/Hd27d8drr71mqr1feuklnHjiiahRo0bZjFgkzDRtmo3c3Chs3AioKCx8q1Y1sWhkhehLljg9CikrPEGik2LiZtpvL2UK0UVEREQk0BD9t99+w+LFi/HUU0+ZSYlsdtsUfhw0aJDZKReR4mncOMsTtipED8/ALSGBLaqAXbucHo0Ea56D99+3chd1EAs//FvdsqXToxDxTfvtIiIiIiKlL+DZDLdt24YtW7Zg9OjReS4T5e333nsPjz76KD744IMyGKJIeKtfPwdxcZyIUGlbuAZuzZoBXjmGhDn2yj5wIMqcNElMdHo0UhYnxvr0UWWquJv220tPOWTgE/QFEmujXLnp/tePLYdPLvvEc9uv6HJAz0/+uy0iIiIioRuin3feedizZw82btyIyZMnF7h/3759qFixIi655BLTV/F9luCJSEBiYli5+l/bDwkv3K5q5RJ57Vzsba8QPbykpADJydaJMRG30n576YpFFvriMyChUUBHTrHRseh7VN/AXyA6FmhQjPVFRERExL0hOneyly9fbqpXLr30Up/rcUc8Q7MjihQbAxmF6OFbtXrGGU6PQpwI0bntu3Z1ejRSmjTHgYQC7beXUU909UYXERERiWgBhejcAf/0009Nb8Xbb7/dc1mot9q1a+OXX34pizGKhD1OUqe3T/jJyQHWr9ckhJGmcuVc1KqVi7//VoumcA3R9Z4WN9N+e+nKzI3FaxgIpFTHgEwgLs7P+tmZeG3pa+b2gPYDEBfj5wE5mcAGa300HQBE+1lfRERERNzdE/20007DmjVrEBcXZyYpsvsqcsnOzjaLiJRMixa5mDWL7ylNRBhO/vkHYJGfqlYjD0NWXV0Sfnh1QcWKDCCBnTudHo2Ib9pvLz2HcmJxFWYAe4GLDvkP0Q9lH8JVH15lbl/U7qIAQvRDwG/W+mh8kUJ0ERERkVAP0RMSEswiImXTziU11QplGM5I+ARupKrVyMNtbm9/CR/cpty2Otkpbqf99lKkNi4iIiIiwqlsnB6AiPwXsip0Cy/29uTEsRJZVIkevu9pXVkiEmEUoouIiIiIQnQRd4XoCt3CL3CrXx+oUMHpkUiwNW+ei61bgYMHnR6JlCaF6CIRSCG6iIiIiChEF3GHypWBxESF6OEYuLVs6fQoxKkTY8xdNmxweiRSWrKygI0bFaKLRByF6CIiIiKiEF3EPRjMqJ1LeFHVauTS1SXhZ9MmK0jXe1okwihEFxERERGF6CLuoR7K4UcheuRq0ACIj9eJsXBib0u9p0UijEJ0EREREQEQ6/QAROS/EP2HH5wehZSWPXuAf/9V4BapoqOBZs10YizcQvSYGKBxY6dHIiLBVC7qEN7GRUDlKihXbpr/9WPL4e0L3/bc9iu6HHDS2//dFhERERFXUogu4hIMWzkRYXo6UL6806ORI6WqVVGLpvDCbdmkCRAXB+TkOD0aEQmWWGThIswGytUK6MgpNjoWFx19UeAvEB0LNC7G+iIiIiLiCLVzEXFZD2VNRBge7PBUE4tGLrVoCi9qzyQSwe1coqLU1kVEREQkwilEF3EJTUQYfoFb9erWIpEdoit3CQ8K0UUiU1ZONN7BRXgno5+ZXNj/+ll4Z/k7ZuFtv7jOpnesJZD1RURERMQRauci4hL16wPlyqn9Q7hQ4Cbc/mzPtH07UK+e06ORI8ETIXxPX3GF0yMRkWDLyInDxblvASlASgYQ6+foKSMrAxfPvtjcThmVgth4Pw/IyQB+ttbHxSlWexcRERERcR1Voou4hCYiDC8K0UVXl4SP5GQgNVXvaZGIpMuJREREREQhuoi7qIdy+FCILnaIrqtLQp8mChaJYArRRUREREQhuoi7MKBR4Bb62MJjyxYFbpGuQgWgbl2dGAsH9t9l+8SIiIiIiIiIRBaF6CIuookIw8P69dbHli2dHok4TVeXhE+IXqcOUKmS0yMRkaDTTpmIiIiIKEQXcV/gdvCgNRGhhC61fhCbri4JD2rPJBLBFKKLiIiIiEJ0EXexQxqFbqGN2y8hAahXz+mRiNNUiR4eFKJLJFu2bBmSkpJQvXp1jBw5ErnFCJX37t2LevXqYcOGDQhZCtFFRERERCG6iLs0a2Z9VOgW+oEbw9No/YWNeAxeeWVJaqrTI5EjoRBdIlVGRgb69euHTp06YeHChVixYgVmzJgR8OMZum8P8cvr4nEI08sPxfT4IYiPD2D9mHhMP2e6WXjbr+h4oNt0a+FtEREREXGlWKcHICL/0USE4UGBm9jsiSjZJ/+YY5wejZTEgQNAcrLe0xKZ5syZg3379mHixImoUKECxo0bhxtvvBFXXXWV38f++OOP+Oijj1CzZk2EsjhkYlDCmzyjAMS96H/9mDgMOnZQ4C8QHQc0L8b6IiIiIuII1UmKuIx6KIc+bj9NKireIbpOjIUue9spRJdItGTJEnTr1s0E6NShQwdTjR5IBfuQIUPw9NNPo1Koz8jLdi68tExtXUREREQimirRRVwYuilED13Z2VbVsQI3IV5ZUr683tOhTBMFSyTbv38/mtm95gBERUUhJiYGe/bsMT3SfWHF+lFHHYVLLrkEd955p9/AnYv3a1JOTo5ZgoWvxX7v+V8zOycaX2SeAWSl44xDOYj1c/SUlZOFL9Z9YW73adEHsdF+HpCTBWy31kfdPoC/9aXMtrWEF23nyKFtHRm0nSNHjgPbOtDX0l6aiAtD9C+/dHoUUlKbNwOZmQrcxBIVpclFwyFEZyFtYqLTIxEJvtjYWJQrVy7P1xISEpCWluYzRF+5ciUmT56MxYsXB/Qa48ePx9ixYwt8fefOnTh48CCCefDE1jU8aIv2mtSkQk4c+u1/3dxet2UHKlQouiI9LTMN/d/sb61/9TpUiLOq+H2Jyk5DnR+t9Xf0XIfcmKLXl7Lb1hJetJ0jh7Z1ZNB2jhw5DmzrA+zhGQCF6CIuwzYgO3ZYfXgrV3Z6NFJcqlqV/NSiKTzmOOAJEZFIU6NGDSxbtqzAQUa8jxk2ebBz3XXX4aGHHkL9+vUDeo1Ro0ZhxIgReSrRGzVqhMTERFSpUgXBPGBjpT1f1/uALc3rzc/7KlYs+nlSD6XmXT/ezwOy8q6PWD/rS5ltawkv2s6RQ9s6Mmg7R44cB7Y1i0QCoRBdxGVatbI+rl0LHHec06ORkgRu/DvfpInTIxG3YCX6Z585PQopKU0ULJEsKSkJL730kufz9evXm9YrDNcLs2nTJvz8889YunQpRo4c6QnF2Uud1emXX355gcew0j1/tTvxoCnYB8k8YMv/ulFevdCt+4p+Du/HBvQ95Fvf7wtImW1rCT/azpFD2zoyaDtHjqggb+tAX0e/eSIuY09IyRBdQjNwa9wY8FGkJxH6nmaf/Kwsp0ciJaEQXSJZjx49TAg+ffp0T6/z3r17m77oe/fuRTYnAvHSoEEDE7T/8ccfnoUV6Z999hn697daloQcTSgqIiIiIqpEF3GfmjUBthlds8bpkUhJAzf7RIiIfXUJ++Rv2mRVpUvosLebQnSJVOyJPnXqVFx22WWmspxVOt9//725jz3R2ff82GOPzbN+06ZNCzxHw4YNUYmTC4QihegiIiIiohBdxL2hm0L00MQrCLp1c3oU4sYWTatXK0QPNRs2ACy01YkxiWSsIF+3bh0WLVqEbt26oSbP9h/ufx6IDXwjhTKF6CIiIiKidi4i7qQQPXSPs//+W1Wrkpfd3kfv6dBjbzP7RIhIpKpbty769u3rCdAjikJ0EREREXFbiL5s2TIzgREvD+Ulo4FWuIwdO9ZMcMRJic477zwcOHDAcx8nMmJDensZPHhwGX4HIqVDIXpoSk4G+OdHVaviLSbGqkDXezr08OoBTtTesKHTIxERp8TnZuDZWvfjWQwLaL6T+Jh4PHvms2bhbb+i44HOz1oLb4uIiIiIK7kmRM/IyEC/fv3QqVMnLFy4ECtWrMCMGTP8Pu61114zy+eff47ly5dj5cqVeOSRR8x9aWlp5vLT5ORk7NmzxyzPPPNMEL4bkSMP0RnI7t/v9EikuIEbHXWU0yMRt+HvhEL00MNtxpNiQZoUXkRcKC4qCzdWfx034jnExQWwfkwcbuxyo1l426/oOOCoG62Ft0VERETElVxzWDhnzhzs27cPEydORIsWLTBu3DhMmzbN7+M2b96MmTNnokuXLmjZsiUuueQSM8kR8SMr0RMTE1GtWjWzlC9fPgjfjciRsVsHKHQLvRA9KkrtXKQgXV0Suu9pnRQTiXC8MlZn0kREREQinmsmFl2yZImZrKhChQrmc4bfrEb356677srz+apVq9DqcAI5f/58bNmyxYTomZmZuOyyy/Dkk0+ati++quG52PYfLgPOyckxSzDx9djOJtivK3DFdrYmH4zG6tU5OO44p0YnxbVqVRSaNGH/a27TvPfpPR0ZfG1nnljZsCEKGRm5AVUyijusWROFSy7hds3bXk7v58jh1LbW75Z7ZOdE4af0ruwMj5OzrRZdRa+fjZ82/WRun9z4ZMRE+3lATjaw01ofiScD/tYXERERkcgI0c8991x8//33Bb4eExODSy+91PM5+5fza2zBwh7pgVi9ejXef/99/P77755A/aSTTsL999+PvXv3YsCAAZg0aVKB4N02fvx40189v507d+LgwYMI9sETK/N54Bat6pewVdR2rl69Nv74IxW9eqU6Nj4pnmXLqqFJkygkJ+8pcJ/e05HB13ZOTIxHdnYNLFy4Cy1aZDs6RgkM/9vftKkO6tbdj+Tk9Dz36f0cOZza1t7z+4izDuaWQ69NM83tlPRcVKwUVfT6WQfRa2Yva/1RKagYX7HoF8g5CHxjrY+LU4BoP+uLiIiISGSE6C+++CLS0/MejNJTTz1lgnNvCQkJpq95ICE6D3KuvvpqM3Ho0Ucfbb42efLkPOuMHj0aTz/9tM8QfdSoURgxYkSeSvRGjRqZSvYqVaogmPj98OfB19YBevgqaju3bh2Ff/6phNq1dTAVKjZtikLPnkDt2rUL3Kf3dGTwtZ2TkqyP//5bE927Ozc+Cdzy5eziEIXjj6+M2rUr57lP7+fI4dS25j6wuKidS57bRYfoIiIiIhKegh6i16lTp9Cv161bF8uWLStQhRMfH9gs9Q8++CD+/fdfTJgwwec6DLa2bt3q8362eSms1QsPmpw4SOZBm1OvLc5vZ3YlWruWv386WAsF2dnW9rruOt/bTO/pyFDYdm7UiKEYsG4dv+7o8CRAfD9TmzaFbzO9nyOHE9tav1duDtFFREREJBK5Zg89KSkJc+fO9Xy+fv1605+8Ro0afh/78ccfmwlJ3333XU9PderevbuZeNTG52/ChsUiIUATEYYW/qnhlAqahFAKwzysZUtrokoJDfz7W7kyT8A7PRIRcZRCdBERERFxU4jeo0cP0z5l+vTp5vNx48ahd+/epi86sad5Nks981m5cqWZMPSZZ54xrVdSUlJMCxhiW5chQ4Zg3rx5mDlzJp544gnccMMNQf7OREoeou/axd99p0cigbBPeBye11ikAJ0YCy084cGTYvk6zYlIpFGILiIiIiJuCtFjY2MxdepUDBs2DLVq1cKHH36IRx991HM/+6IvXbq0wOOmTJmC1NRUDBw4EJUrVzZLu3btzH2PP/64ac/Sq1cvjBkzxrR64XoioYBVq6TQLXQCt7g4QBe7iC8K0UMLt5VOiolIHgrRRURERCJW0HuiF6V///5Yt24dFi1ahG7duqFmzZqe+3J97LROmjTJLIWpVq0a3n///TIbr0hZssMbBjn2pITi7hC9RQueEHR6JOLm9/SmTcDBg1Z/dHH/e/qUU5wehYg4TsG5iIiIiLgtRLcnGO3bt6/TwxBxXNWqnIhXPZRDrfWDSFEhOrOYv/8GDl8wJS514ACwfbsq0UUEiMs9hMcaPQNs3oS42If8rx8Th8d6P+a57VdUHHDsY//dFhERERFXcl2ILiL/adMG+Osvp0chgeAVA+ee6/QoxM3skyw84aIQ3d00x4GI2OJxCCMbvgFsngvEPeh//Zh4jDxxZOAvEBMPtCvG+iIiIiIS2T3RRaQgheih4dAhYP16VaJL0erWBapUAVatcnok4o9CdBHx4CVE9gzDau0iIiIiErEUoou4WOvWVuCWk+P0SKQobM/BbaQQXYrCDEYnxkIDrxbgtCw1ajg9EhFxWnZOFBaktMUCdEZ2lv8QPTsnGwu2LjALb/vFdXYvsJZA1hcRERERR6idi4iLMXDjJIScjLBpU6dHI77YfesVoos/CtFDA09e8iSmiMjB3HLo8udUczslPRUVK/tZP+sgukztYq0/KgUV4ysW/YCcg8AX1vq4OAWI9rO+iIiIiDhClegiLg/cSO0f3B+iV6wI1Kvn9EgkFN7TK1eqI4DbcRu1bev0KETEFbz/YOuPt4iIiEjEUogu4mKNGwMJCapcDYXAjeGo3TJVxBcGs/v2ATt2OD0S8YWtmfg3VyG6iBSgEF1EREQkYilEF3GxmBirRYhCdHdT1aoU9+oSvafda/NmIC1N72kROUzBuYiIiIgoRBdxP/VQdv+xtUJ0CVSLFkBsrN7TbmZvG/uEh4hEOrVzERERERGF6CKupxDd3diWY+9ehegSmLg4K0jXe9q9eFKMbbSaNHF6JCLiOgrRRURERCKWQnSREAjRt2+3glpxnxUrrI8K0SVQ/F1RiO7uEL11a6udlohEuPyhuUJ0ERERkYgV6/QARKRoDHNo1Sqga1enRyOFBW52dbFIoCfG3njD6VGIL2rPJCIeubmIQybGtHwNWLsGcbHD/T4kLiYOY3qO8dz2KyoOOGbMf7dFRERExJUUoou4HCcWJYXo7g3cWrWygnSRQEP0jRuB1FSgYkWnRyP58SqB3r2dHoWIuEJuLuKRiftbvwGs/RSIu9nvQ+Jj4nH/KfcH/hox8UCHYqwvIiIiIo5QOxcRl6tUCWjUSO0f3EpVq1Jc9oSVq1c7PRLJb/duYOdOTSoqIvnat0QfPmRSOxcRERGRiKUQXSQEaHJR91KILsVlB7R6T7vz/Ux6T4uILQdRWJ7SBMvRDjnZ/kP0nNwcLE9ebhbe9ovr7F1uLYGsLyIiIiKOUDsXkRAJ3b76yulRSH779gH//KPATYqnalWgXj2F6G7EbcKCU7uNlohEuNxcpKM8jvnuGfNpSvou+OvClZ6ZjmNeOMZaf1QKKsb7eUR2OvCZtT4uTgFi1edLRERExI1UiS4SAo4+GlizBsjIcHokUljVart2To9EQo2uLnHve7p5c6BcOadHIiKukL99i9q5iIiIiEQshegiIeCYY4DsbGtyUXGPFSuAqCigdWunRyKhhlcvLF/u9CgkP7VnEpE8FKKLiIiIyGEK0UVCpBKdli1zeiSSP3Br2hQoX97pkUioad/eOil26JDTI5H872lNKioiHgrRRUREROQwhegiIaBaNaBhQ4XobqOqVTmSq0uysnR1iZukpgIbN+o9LSJeFJqLiIiIyGEK0UVCKHRTiO4uCtHlSN7PtHSp0yMRG9vrMC/r0MHpkYiIa6gSXUREREQOU4guEkKhmwI390hJAf7+22rLIVKSq0saNdJ72k3+/BOIjtZEwSLiRSG6iIiIiBwWa98QEXdjWPv448CBA0Dlyk6PRuyrAlS1KiWlq0vchSc0WrXSHAciklccMnF7u8+AFcsRF3O5//Vj4nB799s9t/2KigPa3v7fbRERERFxJYXoIiHW/mHFCqBrV6dHI6xajYlROxc5shNjb73l9CjE+z2tK0tEJI/cXMQjExOS3gZWzATi/Yfo8THxmHDGhMBfIyYeOK4Y64uIiIiII9TORSREMKyNilLlqpsCt9atgYQEp0cioXxijBNZ7t/v9EiEHRpYia4rS0Sk0PYt7PXk/bn8P3t3AmdT/cZx/DuMMfZ9yb4XQmWJNllaZGmREi0qEonSX1FRsu/SIlFo06JNSaWkKBWSspelRDK2wYyxzf2/nt/t3mbGrMzMnZn7eb9ep7n3rL97zox+57nPeX4AAABBhyA6kE1YiYEaNQiiZ6UgOgE3nAlf1jN/04H399/S3r1kogNIwONRrEK0LaqUtqmyYk+mHESP9cRq24FtbrLXKR8jVjq8zTulZn0AAAAEBEF0IBuhhnLWYIloBNFxps45x1sSiL/pwPMN8MrfNIB4PB4dUT5VfXuMqmqbjhxJeZMjx4+o6tNV3WSvU3TyiDSvqney1wAAAMiSCKID2QhB9Kzhzz+lyEgCbjgzVgrIBrL0BXAROPalWIECUpUqgW4JgCwlYfkWyrkAAAAELYLoQDYLou/aJe3ZE+iWBDcLuBmC6DhTVj6EIHrg2TWwa+ErewwADkF0AAAA/IvbRSCbBdEN2eiBD6IXLSpVqBDoliCnBNGJywT+b5p66ABOQRAdAAAA/yKIDmQjtWp5S0D8/HOgWxLcfPXQQ0IC3RLkhC/G9u3zPmGCwDh+XFq/nidLAKQCQXQAAICgRRAdyEZCQ6UGDaSVKwPdkuDGoKJIL/b3bPhiLHA2bZKOHSMTHUAiCJoDAADgXwTRgWzmggukn34KdCuC15Ej3qAbQXSkh6pVvaWB+GIs8GMcEEQHcArKuQAAAOBfob4XALJPEH3aNCkqSipQINCtCT7r1kmxsQTRkT6sJJD9TRNEDxx7CsDGNyhePNAtAZDleDwK1Qn1brBUWr1aobmuSnGT0Fyh6t2ot/91ikJCpZq9/3sNAACALImeGpDNNGzoDeJa9mSzZoFuTXAG3CzwWbduoFuCnPQ3/eabgW5F8FqxQmrcONCtAJAleTzKq2N6rtX70uqJUtimFDfJG5pXz7V9LvXHyJ1XapyG9QEAABAQlHMBshkL3ubJQ+ZqoCxfLtWuLRUsGOiWICcF0bdvlyIiAt2S4GNfSNq/pY0aBbolALIkX/kW30jilHMBAAAIWlkqiL5mzRo1btxYxYoV04ABA+RJZUe1fv36CgkJ8U/du3f3L5s7d64qV66scuXKac6cORnYeiBzhIV5a/dSFz1wQXSyVpHeQXTDF2OZb/NmKTKSIDqAJHg8sruRiJhCilBJeWJTvjex+5eIqAg3pepextaJifBOBOkBAACyrCwTRD969Kjat2+vhg0basWKFVq3bp1mzZqV4nbR0dHavHmzdu/erf3797vpmWee8Qflu3btqsGDB+uzzz7TkCFDtHHjxkz4NEDGYnDRwIiJkX79lSA60lf16lKRIvxNB6qUS9wvMgAgoWjlV+nnnlBpRSg6OhXrH49W6fGl3WSvU3QyWnqvtHey1wAAAMiSskwQfcGCBYqMjNTEiRNVvXp1jRw5Ui+99FKK261atcplopcqVUpFixZ1U758+dyyGTNmqEWLFi4zvV69eurTp49effXVTPg0QMYH0deu9QZ1kXmsDv3x4wTRkb4YXDSwQfSqVaUSJQLdEgBZUsLMcDLFAQAAglaWCaKvXr1aTZs2Vf78+d17C4xbNnpKfvzxR/3111/+IHqvXr1cVrtvny1btvSv26RJE60kSoEcwLImT5ywpy0C3ZLgK+Vi9egbNAh0S5AT/6b531Ng/qYp5QIgSQTNAQAA8K9QZbLrrrtOixcvPmV+7ty51blzZ/97q21u86w8i9VIT4qVZ7nkkkv05JNP6sCBA658y6RJkzRw4EAdPHhQVS3F7F+FCxfWzp07k9yXBd99AXhj25vY2Fg3ZSY7ntVRzOzjQtniOtvgorlzh2j5co/LYEXm+PHHEFePPk8eu2Zp25a/6eBwutf5vPOk8eNzKSIilqzoTHLypJXQCdHgwfw9I+tda363sggy0QEAABCoIPq0adN05MiRU+Y//fTTLnAeV3h4uKt5nlwQ/YUXXoj33uqeT5kyxQXRQ0NDlTdv3lP2l5RRo0Zp6NChp8yPiIhQTCbXzbCbJytvYzduuXJlmQcGkIWuc61aJfTdd8d1/fXeL3uQ8X74oYSaNDmu3bvTfs75mw4Op3udq1TJLamUvvzygC6//FiGthFeGzfmVlRUKVWvvl+7d6ftnPP3HDwCda0PHTqUacdCMgiiAwAAIFBB9DJlyiQ6v2zZsm4g0IQ3EGFhYWnaf+nSpbVjxw73unjx4i4Antr9DRo0SP3794+XiV6xYkVXKsay2DP7ps2+VLBjc4Oec53JdW7SJESrV4eqdOnwDGsf/nP4sPTbbyF66KHcp3XO+ZsODqd7nUuWlAoV8mjLlqK66aYMbSL+9emn3p8tW9p4Kmnblr/n4BGoa22JH8gCCKIDAAAgUEH0pDRu3FjTp0/3v9+6dasrrWKB8OQ0a9ZMb7/9tgt2m2XLlqly5cr+fdr7u+++2z8Iafny5ZPcl2Wtx81c97GbpkDcJNtNW6COjax/nS++WLJxcqOjQ1SwYIY1D//6+WcLpkgXXmjX6/T2wd90cDid62yrWl30FStO//cLafPTT1LNmvaF++mdcP6eg0cgrjW/V1kEQXQAAAD8K8v00C+77DKX+T1z5kz3fuTIkWrdurWri26s3vlJK2CaQN26ddWzZ0/98MMPmj17tiZMmOAGFzUdO3bUm2++qV9//VWHDx92ZV6uuuqqTP5kQMawILoFdX/4IdAtCZ4BCPPlk+rUCXRLkFNddJH07bfEaDLLihUMKgogBR6PQnVCd5y/WndolkJzp/wPdGiuUN3R4A432esUhYRKVe/wTvYaAAAAWVKWCaJb/fIZM2aoT58+KlmypD788EONGTPGv9zqolswPKHx48e77PEWLVroiSee0Lhx43THHXe4ZQ0aNFC/fv3UqFEjl4FuAfnevXtn6ucCMso558iVILCgGzIn4GaDuIZyf4sM/GJs925p8+ZAtyTnszHELRO9SZNAtwRAVpdXxzTrxvmapTuVN0/KA77mDc2rWdfNcpO9TlHuvFKzWd7JXgMAACBLylLhoA4dOmjz5s1auXKlmjZtqhIlSviX2YBOiSlatKjef//9JPc5YsQIde3a1dVJb968eZprrANZlT3pbZmr330X6JYEh++/l667LtCtQE7WrJmVjZCWLpVq1Ah0a3L+kyUWSL/00kC3BECW5rv/8JXX4VEhAACAoJVlMtHjDjDatm3beAH0M1WnTh1dccUVBNCR41gQfdkyKZFKR0hHf/4pbdtmZacC3RLkZMWKSeeey9MlmWHJErmxJBo0CHRLAGRpHo8sbB51Iq+ilD9VMXRL/Ik6FuWmpJKAEh5DJ6K8E0F6AACALCvLBdEBpK38w8GD0rp1gW5JzvbNN96fZK0iM/6mLRMdGcvOsX0JSXkmAMnyeBSt/Co4+EEVVJSij4SkuEn08WgVHFXQTfY6RSejpbcLeid7DQAAgCyJIDqQjTVuLNnYu2SuZnwQvW5dqWTJQLcEOd0ll0gbNkh79wa6JTmXPblj/2bypRiQOmvWrFHjxo3d+EQDBgxIVXb10KFDVbx4cTdu0fXXX69Dhw4pW0r4WckUBwAACFoE0YFsrEAB6fzzqYueGUF0SrkgszLRDX/TGWfNGikykiA6kBpHjx5V+/bt1bBhQ61YsULr1q3TrFmzkt3m9ddfd9Onn36qtWvXav369Ro9erSyJYLoAAAA+BdBdCAHBN3IRM84//wjbdxIEB2Zo3JlqXx5SrpkdD30PHmkJk0C3RIg61uwYIEiIyM1ceJEVa9eXSNHjtRLL72U7Dbbt2/X7Nmz1aRJE9WoUUM333yzVq1apWyJIDoAAAD+RRAdyOasru+WLdLffwe6JTk34GbIWkVmCAnhi7HM+Ju2Ulj58gW6JUDWt3r1ajVt2lT58+d37+vXr++y0ZMzcOBANWvWzP9+48aNqlmzprIlgugAAAD4F0NqAdmcL7i7eLF0yy2Bbk3OLOVSvbo3OxjIrLro//ufFBMjhYcHujU5i8W/LIh+++2BbgmQPRw8eFBVq1b1vw8JCVHu3Lm1f/9+VyM9JZs2bdL777+vn376KdmSMTbFPaaJjY11U2axY1m993jHTHD82JMnU2xT3OWp+gyxsf6sJrduJn7mYJXotUaOw3UOHlzr4MB1Dh6xAbjWqT0WQXQgmzvrLKlePenzzwmiZwTqoSOzNW8uHTvmzUZv1SrQrclZfE/t8GQJkDqhoaFucNC4wsPDFR0dnWIQ3W5G7rrrLnXv3l11bXTuJIwaNcoNRJpQRESEYuzbxExi7bXSNXbTliuXN6yde88eeXPwvezLg6jdu5PdT/Tx6HifISpPVLLrh5yMVpk463tyJ78+MuZaI+fhOgcPrnVw4DoHj9gAXOtDhw6laj2C6EAOcMUV0ltvebMsrRwE0sf+/dIvv0gPPBDoliCY2Jdi9uXYZ58RRE9vX38tWT/MN4ArgOQVL15ca2w03gQ3GWFhYSluO2zYMO3bt0/jxo1Ldr1Bgwapf//+8TLRK1asqFKlSqlw4cLKzBs2y7S34/pv2Pbt0zGdVMfzf1fIqlUqWbyiwkuXTnY/MSdi1LF2R/e6bJmyCg9N4ZGikzHyVPSuX6p0WSk3jyAF5Fojx+E6Bw+udXDgOgeP2ABca0sSSQ2C6EAOcOWV0sSJ0vr1Up06gW5Nzgq42RcTlhkMZBb7Isz+pi2IPnZsoFuTs9gTO40aSUWLBrolQPbQuHFjTZ8+3f9+69atrvSKBdeT89FHH7nBSL///nt/PfWkWKZ7wmx3YzdNmX2TbDds8Y4bEqJwHdXcexdJPXtK4cu838QlI39Yfs29aW7qD5orv3Spd33yIAJ4rZEjcZ2DB9c6OHCdg0dIJl/r1B6H3zwgB7DSBHb/uXBhoFuSs1gQs0YNKU45WCBTXHWV9ykIBgxOP1bm7osvvF9QAEidyy67zGWGz5w5070fOXKkWrdu7eqiHzhwQCdPnjxlm/Xr1+uWW27RM8884zLKDx8+7Mq/ZEu+gUR9N1YMLAoAABC0CKIDOYAledlghJZlifRh98kWRLdgJhCIEk2Wkc7fdPpZtUrau5cgOpDWmugzZsxQnz59VLJkSX344YcaM2aMW2Y10X/99ddTtnnxxRcVFRWlO+64Q4UKFXJTnez6mJwvaO6rlUcQHQAAIGgRRAdyCAsMLV4sHT0a6JbkDL//bo+tE0RHYJQsKTVs6P0iB+nDvpAoWFBq2jTQLQGylw4dOmjz5s2aPXu2yzL3BcRtsKfzzjvvlPUnTZrklsWdtm3bpmzJ41GU8iuk+90KkUdR0SkXXIk6FqWQoSFustcpOhElvRHinew1AAAAsiSC6EAOCqLb09LLlgW6JTmDBS/z5JFatAh0SxCs7AscK9FkZUhw5ubP9w7Uan/XANKmbNmyatu2rUqUKKGgkjDznEx0AACAoEUQHcgh6teXSpcmczW9fPyxd0BRy1wFAhVE37NH+umnQLck+7PzaF8wtm8f6JYAyNYIogMAAAQtguhADmFjXl19tTRvXqBbkv0dPCgtWmSPsAe6JQhmVnakUCFpwYJAtyT7++QTb0Z/27aBbgmAbIVMdAAAAPyLIDqQg9x4o7RunXfC6bNs/uPHCaIjsKzsyDXXSO+9F+iWZH8ffSQ1bmwlKQLdEgDZCkFzAAAA/IsgOpDD6qJb5urcuYFuSfb24YdSgwZS5cqBbgmC3U03ST//LG3aFOiWZF9Hjniz+flSDECakYkOAACAfxFEB3KQvHm9gaJ33gl0S7KvmBhv1ur11we6JYDUpo1UoAB/02f6ZElUlNSpU6BbAiDbIYgOAACAfxFEB3IYCxStWSNt2BDolmTfgJvVRLcMYCDQ8uXzfjH29tuBbkn2ZV9A1KsnnX12oFsCINvxeJRbJ3XN+X/rGs1X7lwpB9Fz58qta2pe4yZ7naKQ3FK5a7yTvQYAAECWRBAdyIElXQoWpKTL6bJgpQXcatcOdEsAL/tC55df+GLsdEu52JMlHTsGuiUAsiWPR+E6qvmPfqv5aqfwPCdT3CQ8NFzzu8x3k71OUe5w6fL53sleAwAAIEsiiA7kwMzVdu0Iop8OK/kwbx5Z6Mharr7a+8UY2ehpZwH0Q4ekLl0C3RIA2ZKvfEuuf2+ZKOcCAAAQtAiiAzmQBYFXr5bWrg10S7KX996TDh+Wbr010C0B/hMeLl17rfTWW8Rv0uqVV6SmTaWaNQPdEgDZGkF0AACAoEcQHciB2raVSpeWXnwx0C3JXmbNkpo3l6pUCXRLgPgsk3rdOmn58kC3JPvYvVv69FPp9tsD3RIA2ZbHoyjlV4HO7VRAhxUVHZLiJlHHolRgZAE32esUnYiS3irgnew1AAAAsiSC6EAOFBYm3XmnNwvTagIjZX/8IX31lXTHHYFuCXCqq66SKleWpk4NdEuyj9mzpdBQyjMBOAP/Zp5HHw1VtAqkerPo49FuSrWT0d4JAAAAWRZBdCCH6tFDOnCAOsqpZVn7hQoRcEPWlDu31LOn9Oab0r59gW5N1hcbK02bJnXqJJUoEejWAMi2KN8CAACAfxFEB3Ko6tWlK66QXngh0C3J+o4dk2bM8JZ9KJD6RDMgU919t3TypDfDGslbtEjavNn7xQMApFsQnaA6AABA0CKIDuRgFkD6/nvpl18C3ZKs7d13vfWTe/UKdEuApNk4Bx07eku6WKY1kvbMM9K550oXXxzolgDI1giiAwAA4F8E0YEcrEMHqWxZb0AJibP74fHjvVn7deoEujVA8uyLnt9+82ZaI3GbNkkffSQ9+KAUkvIYgACQNILoAAAA+BdBdCAHy5PHG0iy8g9//hno1mRNixdLP/0k/e9/gW4JkLJLL5UaNJBGjw50S7KuSZOkUqWkLl0C3RIA2R5BdAAAAPyLIDoQBJmrNmDm2LGBbknWNGKEVL++NxMdyOoss3rwYOnLL6Vvvw10a7Ie+7Lw5Zelfv2k8PBAtwZATpBLsWreYL+aa7F7neL6IbnUvHJzN9nr1BxBpZt7J27NAAAAsix6akAOZwF0y0a3gTP//jvQrclavvrKG4x88knKPiD7uP56b73voUMD3ZKs+aVY4cJS376BbgmAHMHjUT7FaPHk1VqsFsoXdjLFTfLlyafF3Ra7yV6nKDSf1Hqxd7LXAAAAyJIIogNB4P77vVmZVvsb/z2R/dhjUqNG0nXXBbo1QOrlyiU98YS0cKH03XeBbk3WsWWLNwv9kUekggUD3RoAOYKvfIv9wwsAAICgRo8QCAJFingzM6dOlf76K9CtyRo++URatkwaPpwsdGQ/N9zgzUa3pyjgNWyYVKKE1Lt3oFsCIMcG0amJDgAAELQIogNB4qGHvNmZgwYFuiWBFxvrrSttgzReeWWgWwOkncVznnrKm41uXwgFu3XrpFde8f77lj9/oFsDIMfweBSl/CrV/kKV0m5FHUn51inqWJRKjSvlJnudohNR0rulvJO9BgAAQJZEEB0Iomz0kSOl116Tvv9eQW3mTGnVKmnUKLLQkX1ZGaKWLaUHHpCOHVPQssRQK1lVrZp0772Bbg2AHOXfzPM9B/Joj0qlOhN9T/QeN6Xa0T3eCQAAAFkWQXQgiNx5p3TeeVK/ft5s7GC0d6+3ZvLtt0sXXxzo1gCnz74Aevppby1w+xms5s6VFi2SpkyR8uYNdGsA5CgJg+aUcwEAAAhaWSqIvmbNGjVu3FjFihXTgAED5ElFR7Vbt24KCQk5Zdq2bZvbvmjRovHmD7cCyECQyp3bG2z78Udp9mwFJRtM9PhxaezYQLcEOHNWF91qgFtpl7//VtCJipL695c6dJDatAl0awDkOATRAQAAkNWC6EePHlX79u3VsGFDrVixQuvWrdOsWbNS3O7555/X/v37/dMnn3yimjVrqmLFivrtt99cED3ucgvOA8Hsssukrl29NdL/+UdBZfly6cUXvYOJlikT6NYA6WPoUClfPqlPn+CL79iXBxER0qRJgW4JgKAQbP/IAgAAIOsF0RcsWKDIyEhNnDhR1atX18iRI/XSSy+luF3+/PldoNw3TZo0SU8++aRy586t5cuXq1mzZvGW5+VZb0CTJ3uz0vv2VdA4evS/cja9egW6NUD6KVZMeu456b33pHfeUdCwJ2rGj5eGDPHWQweAdEcmOgAAAP4Vqixi9erVatq0qQuKm/r167ts9LSwoPnWrVvVuXNn9/7HH390kwXPw8LCdM8992jYsGGurEtS2fA2+Rw8eND9jI2NdVNmsuNZOZrMPi4UFNe5eHFv5uZtt+VS586xuvZa5XhDhoRo0yb7d8KjXLkyvyY8f9PBIVDXuWNH6YYbQnTffVLz5h6VKqUcLSZGuuOOEF1wgfS//9n5ztzj8/ccPAJ1rfndyiIImgMAACBQQfTrrrtOixcvPmW+ZY77gt/GAt02z0qwWI301HjmmWfUq1cv5bIImaRNmza5EjH9+vXT5s2b3f7PPffceMeJa9SoURpqz8UnEBERoRi7Y8/kmyfLzLcbN9/nQc4TyOvcqpXUunVR9eqVR7Vr71HRojn3RnHFijwaP764HnnksMqUidLu3ZnfBv6mg0Mgr/OTT+ZS8+Yldc89RzVtWqRysuHDC2rLlgL6/PO92rfvRKYfn7/n4BGoa33o0KFMOxaSYdddsWpUN1pau9a9TkmukFxqVK6R/3XKcknFG2W1h4QBAAAQ6CD6tGnTdOTIkVPmP/3006dkiIeHhys6OjpVQfR9+/bpww8/dPuJWyLGp2rVqurbt6/mzp2bZBB90KBB6m8jlMXJRLfa6qVKlVLhwoWV2Tdtdj7s2Nyg51yBvs5WMal+/RCNGFFas2fnzCB6dLQNPBiixo0tyFhAoaEFgvJaI+df59Kl7ctk6dZb8+nWW/O67PSc6PvvpalTQzRsmEeXXlo8IG3g7zl4BOpaWx8YWYDHo3yK0fI3t0j1mkhhb6e4Sb48+bQ87/3SRRdJefKlfIzQfNLVy9OnvQAAAMg5QfQySYzmV7ZsWa1Zs+aULBwrw5Ia7733ni699NJkA+6lS5fWjh07klxu9dITq5luN02BuEm2m7ZAHRvBcZ0rVbIvsKRu3aQbbwzJkWVdHntM2r5d+ugjKSws8VJOmYW/6eAQyOvcpYu3LnqfPrnUooVUsqRyFHso7O67pYYNpYcftvMcuLbw9xw8AnGt+b3KYuVcfIk+qS3v0q+fdwT3xx/PuLYBAAAgU2WZHnrjxo21bNky/3urbW71yYtb8eZUePvtt3XDDTf431u2e7169eJlvdv+K1eunM4tB7K322+X2rWTevaU9u5VjvLVV9KUKVaqSTr77EC3Bsh4Fud54QXpxAm5+ug5zeDB0pYt0syZUmiWGdUFQI51ukF0ewwukSdvAQAAkH1lmSD6ZZdd5sqnzLQ7Y0kjR45U69atXV10c+DAAZ08eTLRbS1Q/vXXX+vyyy/3z8uXL5/Leu/du7dWrFihSZMm6Y033nA10wH8x+4Lp02Tjh2T7r9fOYaVk73rLvu3RerbN9CtATJP2bLSs8/al8veKadYulSaMMHqoUt16wa6NQCCgsejaOVTlStqqoq2Kjom5Vun6KOHVaX3MVUJfVbRx6NTPsaJaOnDKt7JXgMAACBLyjJB9NDQUM2YMUN9+vRRyZIlXX3zMWPG+JdbmZZff/010W2/++47t7xatWrx5r/88sv6448/dMkll7ha7G+++aaaN2+e4Z8FyG7KlfPWUp4zR3r3XeUI//ufDQrszVjlqXgEGxv6w2qi9+4t/fOPsr3Dh6U77pCaNbMxDgLdGgDBxKMQ/bEzj/5QFXliU85E9xw5oj+KSn/kOugGpE3NERT1h3ey1wAAAMiSstTD0B06dNDmzZu1cuVKNW3aVCVKlPAvS64T2qpVK+3ateuU+ZUqVdKiRYsyrL1ATmK1lC2Abg9rWPZ2qVLKtj79VHrxRRt8UErw3RoQNE+Y2O+/ZWxbqab33/+vGkF29Mgjkv1v/rPPpH8fUAOAjJfa8i1xxVDGBQAAICfKcvmZNsBo27Zt4wXQAWRe0C02NnvXUo6MlLp3l664whs8BIKVfRFm9dE//FB67TVlW198IT3/vGQPp9WoEejWAAjqIHpqgupHYjKsOQAAAAicLBdEBxA4Zcp4g1XvvJN9aykPGOANpM+Ykb0zb4H0YONt21MmNt7Bjh3Kduxv2cY2aNnSW5oGALJ8EJ1MdAAAgByJIDqAeG66SerUKXvWUraM1enTpXHjrJxToFsDZA023kH+/N4nNE6nMkEgPfigDSxuY5wwtgGA7JKJThAdAAAgJ+KWFMApnnvOG7Cy+ujZJeh26JA3SNiihXTPPYFuDZB1FC/u/XLJxgqwYHR2YfXPbWDgSZOkypUD3RoAQem0aqIfzYiWAAAAIMAIogNItJay1Ue3wQjnzFG2MHCgFBHhLeNCxioQX9u20p13ejO7t29XlhcVJd17rw0c7i3nAgAB4fEoRB7VqXVcdbRWIZ7YFDcJiYlRnd1SnUPhCklVXbkQqUgd72SvAQAAkCURagKQqI4dpVtukfr0kXbuVJa2ZIm3lvvo0VK1aoFuDZA1WUZ3wYJS377K8oYOlXbt8g6MytgGAAIpv45o7VcRWqtzlT/sRMrrH/No7fPS2gXVlD9P/pQPEJpfarvWO9lrAAAAZEkE0QEkW0s5LEzq109Z1rFj3ozVZs2k++4LdGuArKtIEWnyZOmDD6R585Rl/fyzNHGiNHiwVKNGoFsDIKj5yrn4HnFLS010aqMDAADkKATRASSpRAlv9urcudKCBcqSLNi2caM3Y5UyLkDybNDgNm28T5gcPqws5+RJ75gG55wj/e9/gW4NgKCXMIieGgTRAQAAciRCTgCS1bmz1Lq1N8s7q90Pbt0qPfWUt85z/fqBbg2Q9VlpFBs4eM8e6cknleVYWably6UXX/Q+BQMAAeXxKFr5VPey4qqrNYqOSfnWKTrqgOr2lup22q3o49EpH+NEtDS/rney1wAAAMiSCKIDSFXQbccOacQIZankMAvslywpPfFEoFsDZB9Vq0pDhnhLu1jplKzir7+kRx/1lme66KJAtwYAvJ0NG1p03cZQrVPdVFVz8RyN0brS0roSsfKkZgN5pMh13sleAwAAIEsiiA4gRbVqSQMHSmPHShs2KEt47z1viRmr226DJQJIvYcekmrXlnr29JZQyQpswFP7Wx41KtAtAYB/JQyCp6omesx/r7PKP7AAAAA4YwTRAaTKoEFSpUpSr16pu4fMSIcOeQNuHTpI114b2LYA2VGePNK0adKPP3p/BpoNdPr++9KUKVLRooFuDQCcSRA9Tu27mDgBdQAAAGRrBNEBpEp4uLde8eLF0uuvB7YtVoriwAFvwA3A6bGSKT16eL8g+/vvwLUjKkq6/37p6qulG28MXDsAIF2C6DFxg+hZbDAZAAAAnDaC6ABS7corpZtvlvr3l/bvD0wbVq3yBs9tUMTKlQPTBiCnGDNGypvXOzhvoAwbJu3eLT37rHcMBgDIstJaziXuawAAAGRrBNEBpMnEidLRo97s1cxmpUWthnOdOtIDD2T+8YGcplgx79/0W29Jn36a+cdfs0aaMEF67DGpevXMPz4AJItMdAAAAPyLIDqANClXTho50ltH+bvvMvfYL7wgLV/uPbbVdAZw5rp2lVq1knr3lqKjM++4sbHeMRYseD5gQOYdFwBSzeNRiDyqXClWlbUtVU/LhMQcVeUDclNIqjLRQ6QDodK+3N7XAAAAyJIIogNIs3vvlRo39maFHz+eOcfcuVN69FFvDWer5QwgfVhQaOpU79/Y8OGZd9zZs6WlS71jLVhJGQDIcjwe5dcRbVt3RNty11D+PCl3evJHH9e2OWW0bbKUPzV9pND80pPFpIfDvK8BAACQJRFEB5BmuXNLL74orV/vLQWRGawOuwXaRo/OnOMBwaRmTe+XVOPGSWvXZvzx9u71Zp9bFnzLlhl/PAA4Lb7yLfZto02pqol+RCpe/L/XqTnGvn3edVOzPgAAAAKCIDqA03Leed665EOHSlu2ZOyxPvvMW7PZAva++1IA6euRR7ylVewJEyu1ktHHOnHCWw8dAII6iH74sHfQFxOoUdsBAACQIoLoAE7bk09KpUpJ992XuvvK02H3llY32Wo2W9YqgIxhT3pYWZdvv5VefjnjjvPFF9JLL3mfKilTJuOOAwBnzOPREYWr8SV51fjEdzpyNOVbpyNHD6vxRWvUuId0JOpAyseI2Ck9Je8UsSN92g0AAIB0RxAdwGkrWFB67jnp00+lmTMz5hgPPyz98493MNHUDOgF4PS1aCHdcYe31MqODIjlHDwo3X239zj33JP++weAdOXxKFa5tOKnXFrhaZSqp3RiY45oRYFIrSgvxR5JxWjN+/dK1eWd9u1Nl2YDAAAg/RFEB3BG2rWT7rxT6tdP2ro1ffe9cKE3M9bqNFuZCQAZz8om5c8vdeuW/mVdLDhvpX8t0z0XPRAA2U1qHruLOfrf6yMxKa+/f1+c15RzAQAAyKq4hQVwxiZPlkqU8AbdfGU9z1RkpDdj1cq43Htv+uwTQMqslO+sWd6yK88+m377XbDAOyDx+PFSlSrpt18AyLSgeaqC6EcSf52UfXEC5/YtIwAAALIkgugAzljhwtLs2dKSJd46x2fKsl+tpISVfrDayWSsApnriiukvn29A4CuWXPm+/vjD+nWW6VrrqGMC4Bs5HQGfIk7mGhqMtEPxKmbTiY6AABAlkVoCkC6aN5cevxxafBgaf78M9vXqFHShx9Kr70mVa6cXi0EkBb2hVjNmtK110p7z6BM79GjUqdOUqFC0quvMrYBgByeiR43cJ6aTPS4gXMy0QEAALIsgugA0s2TT0rt20tdukgbNpzePmyQUgvEDxnirbcOIDDy5ZPmzfM+EXLjjdKxY2nfh8WbbLyE1auld9/1looBgKAp5xI3Kz01NdEJogMAAGRZBNEBpBsru2KZphUqeIPpf/+dtu1XrJBuuklq00Z64omMaiWA1LLa5e+/L337rdS7d9oHGh05Upo2TXr+ealhw4xqJYCMtGbNGjVu3FjFihXTgAED5ElFIHnu3LmqXLmyypUrpzlz5ijb+vezlizpUUntSTmIbgPDHDuukrkKquTR3PEHGU2uJnp0qBSTh3IuAAAAWRhBdADpXh/9o4+8yVc2KOhff6Vuu++/l666SqpbV3rrLeqgA1nFJZdIM2ZIL78s9eqVusGDLc40bJi3xNNTT3kHCQaQ/Rw9elTt27dXw4YNtWLFCq1bt06zbOThFILuXbt21eDBg/XZZ59pyJAh2rhxo7Ilj0cFFK2I3VJEoWoqEHY8+fWPHlWB41JEtRcUsfB8FThyIuVj7Dkkvdta+uJGKeJgujUdAAAA6YswFYB0V62atGiRFBUlNWniHXA0uWCbDUrasqVUp470ySdSwYKZ2VoAKbn9dm8Q3YLpVmYpIiLpde3v/s47vSWZhg/3BtIBZE8LFixQZGSkJk6cqOrVq2vkyJF6yUb8TsaMGTPUokULde/eXfXq1VOfPn30qj2mlh3FzTy3AR1SesTOV77F6mHZlKpyLvu9ta5KlDizASgAAACQoQiiA8gQtWpJP/wgVa/uHXTUMlHXrv3vftTKQnzzjXT11VK3btLNN0uffy4VKxbolgNIjP2dfvaZ9OOPUu3a0qRJ8SsPWPDcAu227J13pFdekR57jIFEgexs9erVatq0qfLnz+/e169f32Wjp7RNS/tm/F9NmjTRypUrlW1YB2X7du83/PZtoGUG2D9kt90mjR8vtW3r/WkDPVgduh07pOPHEw+ir18vffGFtGqVtHWrdODAqY/zWB10C6LbtHOn9NVX0s8/S7/95g3a2z+0kZHS4cNSdLQUE+M9nu0nNTXaAQAAkC5C02c3AHCqsmWlxYu99ZBHjPAG2GxeyZLeMi92L3n22d6ay9ddF+jWAkhJ69bemNDAgdLDD0sPPeT9oix3bmnLFm9cx/6Wx42TatQIdGsBnKmDBw+qatWq/vchISHKnTu39u/f72qkp2abwoULa6cFh5MpGWNT3O1NbGysmzJL7DffqETv3grZts0fDPdceaWin3tZbS/3SJ5nNH9qY+Wf87ILrockyDL3WBA8LEwxoVKbTY9KlxzUJxP/Uf4rrjjlWJ5Chbz17yzQvn2zdH+kZPOOHVZInC8gUstj/whbHTzf5Pv2Mr1/ZsQ+EztGWqVxW1u75MmTCsmdW55MPC7bZu62tlUJu86hofIEwecNZiEej/dan+nfNLyy6DnkOgeP2PfflydfvsztB6byWATRAWQou6+7/36pRw/pyy+9tc8tocqC6ZdeKl18MfXPgeykdGnvF2JWqsUy0+0JkxMnvE+fXHONdzBSADlDaGio8ubNG29eeHi4oqOjkwyiJ9zGt35SRo0apaFDh54yPyIiQjGWdZ1JcuXKpTwNGijXTTcptnp1HW/QQLGlSys6OkRff+29Wf9n5tXKf91VLgM8ZO9e5d6xQ7kjIpTLpj17XGA9smYVfb27n1t/108bVeiffQo5cEC5Dh5USGSk9+ehQ8p1+LBCoqN18qybVTj/COmk9M+vG5Vr+26FHD6sXNHRCrFHfI4eVYjd2PmmfzPQQ+K+t582z/fa+LLUE/70STDfghMpbpeaddKyz6S2T6s0bm+D49oXN/Z7al8MpUsbzlQmn4PEhAS6Dem8vbvOx44pb1jYf9c5I4+fHvsI9O9hNmVn7djRowqzv+lANwYZxv6m/deZIHqOdigqSgeOHnXX3PpnmXLMQ4dStR5BdACZIjzc+wS0TQCyv3LlvLXPAeRcxYsXdwOFJrzJCAsLS3YbC4Cndv1Bgwapf//+8TLRK1asqFKlSrks9swSW7KkImrWdMeNe8NmcWwfW1agwL9vypTxDuaSgOdYlDTGG0QvddZZKlA5hcdyTkRJc0f411dFHuPJjGyzwxERKprgWiPnXedDEREqwnUOmmtdmGudo3Gdg0fe2Fh5IiJO6ZNlJEv6SA2C6AAAAABO0bhxY02fPt3/fuvWrS6D1wLlyW2zbNky3W2DocjKga9S+fLlk1zfsoETZrsbu2nK7Jtky2xLeNy4TfAuS34f8bdNxWdIsD6P5wXuWiPn4ToHD651cOA6B4+QTL7WqT0Ov3kAAAAATnHZZZe5zPCZM2e69yNHjlTr1q1dXfQDBw7oZMJBMiV17NhRb775pn799VcdPnxYU6ZM0VVXXRWA1gMAAADphyA6AAAAgETrm8+YMUN9+vRRyZIl9eGHH2rMmDFumdVEt0B5Qg0aNFC/fv3UqFEjl4FuAffevXsHoPUAAABADg6i79mzR1WrVtW2bdtSvc3XX3+t2rVru879xIkT4y2bO3euKleurHLlymnOnDkZ0GIAAAAgZ+rQoYM2b96s2bNna/369arzbx1wG+zpvPPOS3SbESNGuDIu1g//7rvvlC9fvkxuNQAAAJCDg+gWQG/Xrl2aAug2cJF17m+55RZXf/H111/XV1995ZbZQEhdu3bV4MGD9dlnn2nIkCHauHFjBn4CAAAAIGcpW7as2rZtqxIlSqR6Gwu2X3HFFckOKppd5M/vnVK9fp78bkq13Pm9EwAAALKsLBVE79y5s7p06ZKmbSxoblnmFiivWbOmC5S/9NJLbpk9ftqiRQt1795d9erVc4+ivvrqqxnUegAAAAA5SYECUlSUd7LXKa4fVkBRj0a5yV6nKLSAdHOUd7LXAAAAyJJClYVMnz7dlXKxOoqptXr1ahcot5FbTZMmTTRw4ED/sjZt2vjXtWVPPfVUkvs6evSom3xsICUTGxvrpsxkx7PHZDP7uMhcXOfgwbUODlzn4MB1Dh6Butb8bgEAAABBHkS/7rrrtHjx4lPmDx8+3GWKp5UFun21GU3hwoW1c+dO/zILyie2LDGjRo3S0KFDEy0ZExMTo8y+eYqMjHQ3brlyZakHBpCOuM7Bg2sdHLjOwYHrHDwCda0PHTqUaccCAAAAkAWD6NOmTdORI0dOmV+8ePHT2l9oaKjy5s3rfx8eHq7o6OgUlyVm0KBB6t+/v/+9BeErVqyoUqVKuQB8Zt+0WXa9HZsb9JyL6xw8uNbBgescHLjOwSNQ19r6rMgaLI+mY0fv63fftWuTwvonYtTxbe8G7970rsJDU9jgZIy05N8DXPqulJtrDwAAkBVlehC9TJky6bo/C75bpnjczB3fAEbJLUuMBdzjBt197KYpEDfJdtMWqGMj83CdgwfXOjhwnYMD1zl4BOJa83uVdZw8KX3yyX+vU1w/9qQ++e0T/+sUeU5KOz/57zUAAACypGzfQ2/cuLGWLVvmf79q1SqVL18+xWUAAAAAAAAAAOSYILqVVjl+/Pgp8zt06KBvv/1WX3zxhVs+duxYXXXVVW5Zx44d9eabb+rXX3/V4cOHNWXKFP8yAAAAAAAAAAByTBC9fv36mj9//inzS5YsqUmTJumaa65xpWI2btyoxx9/3C1r0KCB+vXrp0aNGrkM9Ny5c6t3794BaD0AAAAAAAAAIDvK9JroqeHxeE6Zt23btiTXv/fee12G+YYNG3TppZeqYMGC/mUjRoxQ165dtWPHDjVv3jzZmugAAAAAAAAAAGT5IPrpqFq1qpsSU6dOHTcBAAAAAAAAABCUQfSMzIi3euyZLTY2VocOHVJ4eLhy5co2VXeQRlzn4MG1Dg5c5+DAdQ4egbrWvr5nYk9n5nSB6n8nda2jov5bx5p08mTy+4k6FiXF+NY/qJNhKWxwIkqKjnOA0BTWxxnj3/DgwHUOHlzr4MB1Dh6xAbjWqe17h3iCsXeeSn/99ZcqVqwY6GYAAAAgCG3fvl0VKlRQMKH/DQAAgKzY9yaInsK3Hzt37lShQoUUEhKSqce2b0HsBsIuYOHChTP12Mg8XOfgwbUODlzn4MB1Dh6ButbWPbcMnHLlygVdtlWg+t/8XQcPrnVw4DoHD651cOA6B4+DAbjWqe17U84lGXbiAp39Y78w/AOR83GdgwfXOjhwnYMD1zl4BOJaFylSRMEo0P1v/q6DB9c6OHCdgwfXOjhwnYNH4Uy+1qnpewdXagsAAAAAAAAAAGlAEB0AAAAAAAAAgCQQRM+i8ubNqyeeeML9RM7FdQ4eXOvgwHUODlzn4MG1Dh5c6+DBtQ4OXOfgwbUODlzn4JE3C19rBhYFAAAAAAAAACAJZKIDAAAAAAAAAJAEgugAAAAAAAAAACSBIDoAAJns999/16pVq3Ty5MlANwUAAADIcg4cOKAffvhB+/fvD3RTAMAhiJ6J1qxZo8aNG6tYsWIaMGCAUlOOfu7cuapcubLKlSunOXPmxFv23HPPqUyZMqpWrZoWLVqUgS1HIK+zb3mLFi0yqMUI9HW2QGqvXr1UuHBh5c+fXz169NCJEycy+BMgENc6NjZWnTp10mWXXabrrrtO5557rnbv3p3BnwCB+rfbbNmyxf1dI+ddZ9u2aNGiCgkJ8U/Dhw/P4E+ArPJ7gaxlz549qlq1qrZt25aq6/r111+rdu3aKlmypCZOnJjmf9sRGB9++KG79w0NDdV5552n9evXu/lc65znnXfeUZUqVdS9e3dVqFDBvTdc65zr6quv1qxZs87oWhIjy5r69u0br79co0aN7Pv3bAOLIuPFxMR4qlSp4unZs6fn999/91xzzTWel19+Odltfv31V09YWJhn+vTpnl9++cVTo0YNz4YNG9yyTz/91BMeHu754IMPPN9++62natWqnj179mTSp0FmXWfftc6fP7+nefPmmfAJEIjrPHz4cM+FF17oWbt2rWfVqlWeUqVKeV588cVM+jTIzGs9c+ZMzyWXXOI5duyY5+TJk56WLVt6nnjiiUz6NMjMf7t9rrjiCusNZmDrEajrvHHjRk/lypU9+/fv9092DOT83wtkLREREa4fZf/Wbt26NcXrunv3bk/hwoU9Q4cO9WzatMlzwQUXeBYtWpSmf9uR+ew6FitWzPPWW295du3a5enUqZPnoosu4lrnQAcOHPCULFnSs3r1an//2f5/y7XOuV577TX3b7hd69O9lsTIsq5mzZp55s+f7+8vHzx4MNv+PXNXl0nef/999z/9qKgo9/7nn3/2XHzxxclu069fP89VV13lfz958mTPY4895l5fe+217pfN54EHHnC/RMhZ1/m3335z/ygMGDCAIHoOvs7297t582b/sttvv93Tq1evDGs/AnetP/nkE/dFic+DDz7o6dOnT4a1H4G5zj6vvPKKp3bt2gTRc+h1thu+zp07Z3CrkRV/L5C1tGrVyvP000/HC6Ind10nTZrkOeecczyxsbHuvQVcunbtmup/2xEYH330kWfatGn+9xZQyZcvH9c6B/rzzz/d/2N9LJhesGBBrnUOtXfvXk+ZMmU8Z599tguin+61JEaWNR0/ftwFxA8dOhRvfnb9e6acSyZZvXq1mjZt6n+ku379+lq3bl2K27Rs2dL/vkmTJlq5cmWKy5BzrnOJEiW0fPly1alTJ4NbjkBe50mTJrlHznw2btyomjVrZlj7Ebhr3aZNG/f4sbFHzu3RVCvrgpx1nc3evXvdY4kzZszIwJYjkNf5xx9/dJOVdCldurQef/xxyoAEye8Fspbp06e7R8VTe11tmZVJtEfKDfdY2UO7du10zz33nNJf5lrnPBUrVlTXrl3d6+PHj7t7peuvv55rnUM99NBD7vratT2Ta8l1zpp+/fVXV9LU7oHz5cvnyvb8+eef2fbvmSB6Jjl48KCr0+djvwy5c+dOdpCMhNtYveSdO3emuAw55zpbbSi7OUfOvs5xffXVV6422K233poBLUdWudZDhgxRrVq11LZtW7Vq1SqDWo9AXuf+/fvr5ptv1kUXXZSBLUcgr/OmTZvUvn17N0jwG2+8oRdeeEFvvfVWBn8KZIXfC2Qtca9faq4r91jZ37FjxzRhwgTde++9XOsczIJlZcuW1aeffqopU6ZwrXMgu/f98ssvNXbsWP+8072WXOesad26dTr77LP16quv6pdffnFjWtgXotn175kgeiaxX5S8efPGmxceHq7o6OhUbxN3/eSWIedcZwTXdY6KinKDij7xxBMqVapUBrQcWeVaP/zww27gHAu4ffTRRxnQcgTyOtvNwJIlSzRy5MgMbDUCfZ0XLFigyZMnu45869atXSasDXSEnP97gex9XbnHyv6sr1ygQAE36CTXOueyzNTPP//cPXHAtc55YmJi1LNnT02dOlWFChXyzz/da8l1zpq6du2qFStWqFmzZu5v+fnnn9fChQtddnp2/HsmiJ5JihcvroiIiHjzDh06pLCwsFRvE3f95JYh51xnBNd17tOnjypVquQeaUPOvtYFCxZUly5dXNDt5ZdfzoCWI1DX2W4ILDNu2rRp7gYfwfP/aCvpsmPHjnRsMbLq7wWy93XlHit7W7RokZ577jn3BFCePHm41jmYZaY2bNhQs2fP1nvvvce1zmGGDRumxo0buydz4zrda8l1zh5Kly7tAuj2lEl2/HsmiJ5J7B+HZcuW+d9v3bpVR48edb8Aqd3GHhcuX758isuQc64zguc62zeylmkxZ84c5crFP8059Vo/8sgj7rFFH/sfvj22hpxznX/44Qdt3rxZnTp1cuW4fCW57OfSpUsz+JMgs67zkSNHVK9ePffTx9arXLlyBn4CZJXfC2Tv68o9VvZl1/GWW25xQXTfmFFc65zn66+/duPKxO0vW0C9du3aXOscxL4I+/DDD/39ZXvfu3dv96XJ6VxLrnPWNGDAAHdtfewaWbzD+tHZ8u8504YwDXI2Im2pUqU8L7/8snvfvXt3T7t27dzr/fv3e06cOHHKNjY6bYECBTy//PKLG8n2vPPO84wfP94t+/DDDz1nnXWW56+//vLs2rXLU758ec/cuXMz+VMho6+zj41S3bx580z6FMjs67xo0SJP3rx5PQsXLnTLbIqOjs7kT4XMuNbPPPOMp2bNmp7ly5d7fvrpJ0/FihU9b775ZiZ/KmTkdT5y5Ihn69at8SbrbtlPW4ac8/fcqlUrT7du3dzf88SJEz2hoaGexYsXZ/KnQkb+XiB78f1bm9J1jYiI8ISHh7t+17FjxzxXX321p0+fPqnulyMwrG9cp04dT48ePfz9ZZvsGnKtc5adO3d6Chcu7Jk2bZrnzz//9Nx+++3u2vF3nbNs3749Xn+5Y8eOnnHjxp32tSRGljW9+uqrnqpVq3q++OILz2effeapVauW6z9n179nguiZyP6o8+fP7ylRooT7ZVm7dq2/w7dq1apEt3n00Uc9YWFh7n8iDRs29AfWYmNjPbfeeqsnX758brJfNpuHnHWdfQii5+zrfN1117nt4k5c75z7b/eAAQPcvsqVK+cCb8i5/3b7kLOQM6/zH3/84WnRooX7EvTss8/mRi0H/l4g+wbRU7quU6dO9eTJk8dTrFgxd3NvAZe0/tuOzPXBBx+c0l/2XXOudc7z+eefuy9NChUq5Lnxxhs9u3fvdvO51jnXHXfc4eIep3stiZFlXQMHDvQUKVLEU7x4cU/fvn09hw8fzrZ/zyH2n8zLe8euXbu0cuVKNW3aVCVKlEj1aLZWY7N58+an1PpZvny5G4zQltkjTsiZ1xlZE9c5eHCtgwPXOThwnZFevxfI3tfVHh3fsGGDLr30UjdWSVz8zWc/XOvgwbUODqd7LYmRZS+7stnfM0F0AAAAAAAAAACSwOh1AAAAAAAAAAAkgSA6AAAAAAAAAABJIIgOAAAAAAAAAEASCKIDAAAAAAAAAJAEgugAgFR7+umnNX78ePf6xIkTOnr0qGJjY9Nt/1OmTNHBgweTXWfixInuuAAAAEB2YX3mY8eOxZvn8XgUExOT7HYbN25M175vavrbyZk2bZr27duXbu0BgOyCIDoA4BRPPPGEbrzxxlPmn3/++Xrssce0fPlyvfHGGypSpIibihYt6p/y5MmjZ599Ns3HfOWVV/Txxx+rQIECya4XFRWl++67L837BwAAADLDgQMHtH//fg0bNkwPPvigm2f953r16qlOnTrKmzevatWqpbp16+q8885LNvDeokULzZs3L13aldr+dnLy58+vzp07uy8AACCYEEQHgCCTO3duFS5c2B/wfuaZZ05ZJywszE0JXXbZZerdu7fLXrn99ttd5syhQ4fcjYJvuuqqqxQeHp6mNu3du9fdZLz55puufckZPHiwtm7dqm+++SZNxwAAAAAyw+TJk93TmxYstz61ZZJbMoplla9bt84lofz000/u9YYNG/zbtWvXTlWrVtUFF1ygRo0auQC7BbwtwcXe21S/fn2VL19eL7744hn1t7t166YHHnjAv3zx4sXu/iAlt912mypUqKBXX301jWcFALK30EA3AACQuSxwbp32GjVquI56aGionnzySTc/V65cCgkJ0bfffqtdu3a5jva1117rOuvVq1fXc889p0mTJqV4DNtnWsvEWHZ58eLFU7W+3ZQ89NBDWrRoUZqOAwAAAGQ061dbprb1rWfOnKkPP/xQ999/v+vvWkDd+tsFCxY8Zbv333/flUopU6aMez9gwADt3r1bs2fPdqUUrY9t+/3+++/VrFmzDO1vJ2fkyJFq2bKlS6oBgGBBEB0AgkzCALd18u2xUptvk3Xqf/vtN9fBt0dMLVPGWBaNZdMYC6onrKXYs2dPDRo06LTa9MEHH+iLL75I9fqWybNnzx53U1G6dOnTOiYAAACQEXyJKeauu+7S6NGj3WvLKP/hhx9c/9tKJx4/ftxlmw8dOtQtt+xue/3ZZ5+5bPH33nvP9ZGnTp3qSrpYKZaHH37YBeV//vnnRAPx6dXfTk7ZsmVdxvzKlSvVsGHDdNknAGR1BNEBIAg79faIqP20+uIdO3bUTTfd5DrivpqM9qjpyZMndcMNN/i3s46872bAstR//PFHValSxb0fOHCgjhw5clrtsawa2zZhMNwy3seNG+fKxVxzzTUuAydumZjGjRtrzZo1LgsGAAAAyAq+/vprTZgwwfWdbbI+tQWwff1sK6NiNcXN3Llz9fbbb/u3tYC79YvXrl3rMtethro9CTp//ny98847+uWXX7RixQrXD09LAD2p/nZSPv30U7Vp0ybePPsCwJ5e9WnatKlWr15NEB1A0KAmOgAEGQueWzkXX/1yC4xv2rTJdYS3bNmS6n2kZl5qREREqFSpUvHmWW1Ie3zVajZaW3///XcXRI/LbgIsEx0AAADIKmwMoR07drh+tZVQsdIn1rd9/fXX/U+E2hOcvoB0wj60BdJr1qyp559/Xm3bttWXX37ptl22bJnOPfdc3XzzzWkefyix/raxDHerg26TlXn0ad26tRsY1aalS5e68ZQ6dOgQb1v64gCCDUF0AAgysbGxp8yrVauWe6TUaqCn1iWXXOIy0W2yDvjpssGSDh8+HG+er2zMsWPH3KOi9tjr3XffHW8d2yYtGTgAAABARrMEFcs8t6Dzn3/+6Z7wtMSQuOMKWRmX/Pnzn9JHt3IulrFumeAWNB8zZoyrO26Z6FbP/Nlnn3XJLxZMt4z0M+lvm65du7qnUW2aMWOGf74F+y2wbj+7d+/uytHYk6xx0RcHEGwIogNAkLFHSuOywYnMPffco3z58qV6P5aVsm3bNjf16tXrtNtjmS2RkZHuMVMfC5xPmzbN1Vi3rJnbbrvNZcLEtXnzZlWqVOm0jwsAAABkBBtM1BJArJyLZZpbv7ZChQr+frcF0ROWVrHgu5VsseQUq3tufV/7OWXKFFeD/I033nAB9MmTJ7uAfI0aNc6ov+2b70uKsWMkZPcHFtRPrK9PXxxAsCGIDgBBGERv0qSJSpYsqc8//9zfmbZHT+2x0dTw3QCklOGeWs2aNXP1I3127typRo0aafny5S5Ib4OIxs2St8wXK/FidSIBAACArCImJsYFpTt16uTeW1a69WXtqU9b5gtAV69e3b329cUtsG710Vu0aOHev/jii+rXr5+KFCniEkxse9/Tn1Z6xQYnPZP+dkrsWDZwqLUjsXsBG6SUsYkABBMGFgWAIGMddRuMyLJXrANuHfa0KlOmjC6//PJ486y24+m699579dRTT6lVq1buvQ0Yatnn7733nipXruxvt48N1mSPtvoGOgUAAACyAqtX/sILL7jXVhv9n3/+cYkhVhpl6NCh7r0F0evWratixYq5AUTNK6+8onHjxrmyhnHrpFs5GOsrxy2dYskrR48edfu1bPLT6W8nx8YkeuSRR1wZGUvAsbGUrP2+NlhbLQGHci4AgglBdAAIMlYexTeo0fTp00+px2gOHjx4SoA6OjraH8j+5ZdfEt23ZaVYpk1i+0yOBeTtZuO5555z9R6vvPJKF5S3DB7rtF900UV6/PHH3bqWETN37lx30wAAAABkNdZfHTVqlPu5aNEif+kVK+9iwWwryfLxxx+7oPqcOXPcMqs9blNCVk5l/PjxbrDPM5Gwv52cefPm6dChQy5Q7nPxxRe7co5//PGH+2xpyWoHgJwgxJPYM/kAgKBkZVKaNm3q6i1aJ/uuu+7yL7O6jS+//LLLXk+MBc9r1qzpstTtZqFcuXJpOrZ11K+66iqX8WJZOUmx49vgRjagEgAAAJCVWLJJ+/btdf3112vIkCEqXry4m/+///3P1Uq3ALo9cWmsfIsFzm0QUctMT0zt2rVdEL1t27Zn3LbU9reTY7Xeu3TpkqqMdgDISQiiAwDiWbdunQuYW830tLJa5mkNnsdlj6bGfXz1dNcBAAAAAsVKoFjWecJ+spVR9JUqjNv3rlOnTqa17Uz70vTFAQQrgugAAAAAAAAAACSBrw8BAAAAAAAAAEgCQXQAAAAAAAAAAJJAEB0AAAAAAAAAgCQQRAcAAAAAAAAAIAkE0QEAAAAAAAAASAJBdAAAAAAAAAAAkkAQHQAAAAAAAACAJBBEBwAAAAAAAAAgCQTRAQAAAAAAAABIAkF0AAAAAAAAAACSQBAdAAAAAAAAAIAkEEQHAAAAAAAAACAJBNEBAAAAAAAAAEgCQXQAAAAAAAAAAJJAEB0AkCYHDhzQxo0bA90MAAAAAACATEEQHQCysNdee02zZs3yv3/yySd1zz33+N9v3bpV7dq1U0xMjHvv8XhSve+vv/5aw4YNS3Obhg4dqrp162r79u0KhL/++sv/eteuXXruuedOaz+tW7fWmDFj0rFlAAAACGbWFz9+/Hiq14+NjdX333+v/fv3+98n15+3de+44w4dOnRICxcu1JYtWxJd7/PPP9eKFSsSXfbrr7/q8ssv1759+1LdTgAAQXQAyNIsSN6nTx9t3rzZ37E+duxYvHXmz5+v6OhozZgxQ2effbaWLFniD5IPHDjQLUvMpk2b9O6776apPdZZnzJlikqVKqXHHnvslLYkJTQ0VDVr1tR5552X5BQeHq7Zs2cnux/r9FerVs3dGJiIiAjdf//9rl1psWHDBn355ZcqUaJEmrYDAAAAkjJ+/HhddNFFru9+8uRJffrpp6dM1r+Pq1mzZlq+fLl7fe+996pv375J7t/69a+88orb96RJk3TVVVdp7969p6z31FNPqWvXrjp69Ogpyyz5xu4TcuVKOhy0Y8cOhYSEpGrq1KlTGs8SAGRPoYFuAAAgaY8++qg+/vhjjRo1ygXJrbP75ptvat68ea5T/N1337n1zjnnHF1wwQW6++67VbJkSTfPMlOeffZZvf3223rppZfUtGlT/fPPP/HKslgHfNu2bf55+fLlU5kyZRJty7fffquOHTtqwIAB+t///ucyWC677DK9/vrrql69erKfw/Z7zTXXqGrVqkmuY5/R1kvOyJEjVaFCBbVo0cK9r1evnm688UY9+OCDWrlypfLmzZtowDx37twukG8dfTNx4kQVLVrUtT/u5/d9UWEZRLZNjRo1km0PAAAA4HPzzTdr7ty5atKkiX788Ue1adPGJYv4+qjr1q3TiBEjXJ/dAuLWb7c+Z1hYmFt+22236corr3R96wceeOCU/efJk8f9tH6t3RPUr19fL7/8suufx006sXuERYsW+Y9rT3LafYRNkZGRbp4F3y2gbvcDdl9x1lln+fvivu0sOeeSSy5xrz/44AN1795de/bs8R/L3icWqAeAnIggOgBkYdaptiC1dWp9br31Vj3//POu4+vLBP/777/dunHdeeedatmypbp06aLff//dzbP3CcUNbN91110u4B6XPVJqwfhHHnnEBauts243BV999ZXbtwWy+/fv7zr6vgB+YooUKZLs8oTtT8iC5O+8844rb+O7gTBjx47Vueee67LuLSMnISt348vkT8gy95PSvHlzLV68ONk2AQAAAD6VKlVygef33nvPBcItaP3++++rSpUqbrkloVg/9tVXX3X9awt4x3XppZdq+vTp+uKLL1y5FQty29OavqzxEydOuJ8WCC9QoIBLrLEnROOy8o/XX3+9O5Y9eVmuXDmXLW4BfAvW+8rFWHlG3z4tkB43YJ5SvzyutKwLANkZQXQAyOISy4a2DrAF1n31wX2dVyvtYlnU1113ncsKqVy5sr755hu33ALfxtdxtmC0dd6tXqJ10C0z2zruCbPPBw0apJ9//tl16O2x0MmTJ+uhhx5ygfnPPvvMPbZqtdUnTJjgjmuZ4ddee63LkPGxzrlly+TPnz/Jz2k3A0llstiXBfalQKNGjVwb4rKbEsss79mzpypWrOgC+nH98ssv7mbFzoHdgHTr1k0//PCDVq9erZ9++sll6iTcBgAAAEgr68taFnfnzp3de99TkK1atVKvXr3i9eUTe4LSlzBjkz35af3rxNiTmT4NGjRwfXVjwfuPPvrIBcyt/23942LFisV7YtPWPf/8811GecGCBd08C6LHLe/iu19IeG8Q9zP53H777ak+PwCQnRFEB4AsyGqDT5061QWiLUhsA4waG8zTJutUWz1Fq09urCSJBcztcU4LoltGipVeseC2PWaZUpaIDWZkj49axoqv42zlV6xuoz2GasFmXzDfbgCsw+17JPThhx92nWc71syZM11WjB3bOu52I2GB8yNHjqT6s9s2dnzLuvGxQL5lk9sNQMKOu7HBVv/8809/cN+C6r7t4wbuLXveMn/sp928rFq1yg1MShAdAAAAZ8LKs9gTm/fdd597ejMu66tbKUWfxPqzVn7ljz/+cMsaNmzoyr5Y/9qC374At2Wn25g+69evd+UcLdHEl51uy6w/b/cBlo2+a9cu97TqJ5984u4prD8et38dt+9t+497r2DlDVNbzsWScQAgGBBEB4AsqE6dOq4TbNnjlkntY51h62BbYNgyVIoXL66yZcv6S7JYB9k6ypaRYrUWrRNvNdFtIE5fRomvtMvu3btdp9n33gLVvkdNrfM+fPhwPf744y7AboOCJtSjRw/304LRFpS2YPTQoUP9HXDL8L7iiivc8kKFCiWbhW6sfQcPHtThw4ddLfjBgwe7+ZYtb0Fxu4mwLwjspiGxGw87H2b06NFuoFHLfLcbEB/78uGWW25xGe02OKll8R86dMgt82X0+9h5Tam9AAAAgI/1Ha0vbH1wC4bb05s+1ke3/nBy7OlRe8rT+rrWF7aSiRaYt0C5BdKt3277sLrlFhy3ILr1s3311K3/aqUWrZ9rT2FanXTrQ9eqVUvff/+9G8A0rrjtsUQYe1rTxxcY9yXY+PrqFlyP+7SpvbfkGQAIBgTRASALaty4sZssq3vt2rX++S+88ILLIrHa4laD3Gp6Wyc6MZa9YmVVLIPbgs6++ukJA+K+9zYwqWV6+/gC0L6Mcl8H3cf2awMlWdD6oosucvPi1jy3QTttOwtUJ6yznpQLL7wwXgffHkW1GwAL5lsGj2X3JMfaYl8Y2E/7IsLHvjBo27atG1jV2pKwPVYGJi4rdXPHHXekqs0AAACAsbKDFsyOWxrFJ7Es8Lgse9wmC1z7+t29e/d2T6j6WHnF8uXLu35xYmy5rx02uGm/fv3ceyvfYvcMFoxPmIxi9wgJk0esXIwvAccnsUx0AAgmp/7LDgDIsizYa9kklqFiJV6sk54c6zz7AtyW4W3b20BF1jG2zBHrHNtkmSxWviUxlm2SMIBugXyr9dihQweX7Z6cqKgo90irZcRYprtNlv3+4osv+t/bZOVrbPCjuCwQbpn4loVume7WybcSM7NqZP8AAJ6aSURBVL6sfF/7rY6jtdM+nw2eaoF0y9IxVvbGBgm1bS2w37dvX5fRbtO0adPcFxG+9zaVKVMmyRqVAAAAQHKsFKIlhsQti2KB5yJFisSblxxftrc9GWp9d2ODkFrWt/Vd4ybZJGTZ7HPnznVJI75gvvVtLVCe2NOc1s+PG0RftmyZNm3a5MrDWAka32R9eut3x51nkz21aokqSY1tBAA5BZnoAJCNvPHGG67Miz1+WbhwYdWtW9e/zB73tOyQ559/3j/PssivuuoqfxDbgucWULYguD0yaq8XL16sjRs3xnuEMyUWkLdMdXtENCW+APzYsWNdG4yVbbEMdavt7mMlVeI+HpowU94X2LabBqv1aMHuuJnm9lnjDrLkK+Fi7bT1LUBv2ft28+A7ju/GIuFxE2sHAAAAkBILPlug2zK3LehswWULNJcrV84t940ZlBrWt/WVVrHxiSyb3fq2VrowMe+++67uuusuNWrUyA0yOnLkSPekqI2jZOUek8pgt3sBS9TxfQkQGRmZZJtsoNLEWHnHuOVfACCnIRMdALIRC0hb/fOEGSA2WQ3EhBnUY8aMcRnb1hm2GuWW2W0B4htvvFHjx493HXt7bNQyyqtXr55k4N4Cz3Gn0qVLuyxuKzfjm7dgwYJEt/d1/C1z/d5773WTdbCtlrvvvU0WFPcNjJQcuyFp0aJFvHk7duxItCyLvZ8xY4b7wiBhgB0AAABIb6+88op7AtIC6fY0pW88HyvDYoPZWz/WnqL0lVpMivWLLXElIQuQ//TTT4lmflvg3BJVLDPcMtftaU/r5xt7StOe/PQ9yWmTPbGZsNSMZaFbwo1vHSsnaYFzq/Fug5raE5/Wj7e6774vCSyhJWHNdQDIaUi1A4BswOqPW9DaOqe+GueJeeihh/yvV61a5S+DYmVfLDPl1VdfdctssCLr2Fvg3YLrtm5SfNnjlkGTGHu00wLUSZVAKVq0qObMmRNv3m+//ebWjxvYtoB/3Mz6xMyfP19Llixxj6kmDKL7bk4SuuGGG/yvU/P4bGrWAQAAAJKqS37PPfe4DHAb12fgwIGuD29lWZo2beoSR6z/nVwQ3QLTlvRiT2T27NnTP9+C2xdffLELwi9atMglyljA3PeE5qRJk9xTqb7SMXElVqc9sWWWLGOsfUOGDHH3Eu+8844/MeaKK65wgXkrk2j3Fpdeeql76hMAcjqC6ACQhVkH+c8//3S1zatVq+Y64JYxbjXFE7JyLL4BgKzuYqdOnVzn9u6773aPclr2iXV6TaVKlVxmuA2gaRkxyQWvfUF0C4YnJmFplISGDh2q119/PV6Gi6+ci91Y+FhH3bJafvzxx0T3Y+VYLOhvNxRXXnnlKY+g2s1DwtrtCSVXq3HmzJlasWKFIiIiuBEAAABAmtkTk9ZPtxKCvj6vBdWthKHNs6ckreyJDRhqU1L9f+s/W3901KhRWrdunZvftm1bt8xKMVqf3vrx9tPqr1sG+gUXXOAC4Jb48vPPP7v+8YYNG1zCi5VV9AXh7QlWH2tfQtYXfvPNNzVu3DjXt7ZgfePGjd1n87HsdAuwt27d2t2f3HzzzWrfvj1PfgLI0QiiA0AWZZ1k6xBbR9YC3i+//LJ/UM2k+MqhWMf777//dvUSV69e7QYlso6wZX9bsNpKuVhg2wYdfeaZZ9yAmjbgptVaTCh37tzuZ9wOd1zWMU+OZd8MGjTIZcT4MuitxIp1/JcuXRrv8/oyXOLau3evnn76aY0ePdp9KTB79mz/Mnttj6raT8vKSUnCILpvcFVfTXYbrNW+jPANxgoAAACkhgWoH3nkEZfw4Xs60oLglmhy3333uXGKrI9pfXLrHyfsi/oSYaxva8F2K5VoQXJ7CtWC1VdffbXbh+nXr5+uueYanX/++a6sivXvrfSL1V23RBU7ppVysTrnvjrldixLnrEpIV8b7GfLli1dOUjfur6nTe0+w5c9b316e+LVxmqyAL19SVC/fn2C6AByNILoAJBFWWDbOuP2CKc9lmmdYev8Wu3CxLLCbWDRHj16+Dvs1mG2+ulW09w64JbFbVkj1nG3ju9HH33kOuKWVWJBbgum16tXzwW4LfM9YXmTpAYRSrheXOeee66rkWiDoCYMZlvAPGHQ3j6zDWRkbbYbA2PZ6hYkHzx4sLvh8GXG+wL89mWAZabb50qJPUYblwXufV882DmwYwAAAABpZZnflvxhY/2Y999/35UrtHIo1he2TPGHH37YJYdYVrqN2WPLrA/tK79i/W17mnTEiBEuq9zKpdgTpPa0ZlzWh7/uuutc/9WWWZ10ezrU+sVnnXWWe8rUnkKNy/reVhPd1z5jA43a/YIvkcXuEezewPaRcPBTu9dIWILGgvR272CfycZKAoCcLMQT92tPAECWYhklBQsW9L+34LI9OplUORfLFLHa6YmxLJG3337bPTpqWSNxg9H2v4JPPvnEZYZbAD6ut956y2XCJ/W/C+tQ24Clllnuy3RJTxbktprnlStXTvd92wBJlpVvWegAAADAmbAMbl/A28q2fPjhh66/6WPBassct7GJrEyLPQnasWNH9erVK83Hsn3Zvi1zPG4CTFKsv2vB+kKFCqX5WAAAgugAAAAAAAAAACQp6eGZAQAAAAAAAAAIcgTRAQAAAAAAAABIAkF0AAAAAAAAAACSQBAdAAAAAAAAAIAkhCa1AFJsbKx27tzpRq8OCQkJdHMAAAAQBDwejw4dOqRy5copVy5yXgAAAIBAI4ieDAugV6xYMdDNAAAAQBDavn27KlSoEOhmAAAAAEGPIHoyLAPddwNTuHBhBVMGfkREhEqVKkX2UzrivKY/zmnG4LxmDM5rxuC8ZgzOa2DP68GDB10ih68vCgAAACCwCKInw1fCxQLowRZEj4mJcZ+ZG+f0w3lNf5zTjMF5zRic14zBec0YnNescV4pJwgAAABkDdwVAQAAAAAAAACQBILoAAAAAAAAAAAkgSA6AAAAAAAAAABJIIgOAAAAAAAAAEASCKIDAAAAAAAAAJAEgugAAAAAAAAAACSBIDoAAAAAAAAAAEkgiA4AAAAAAAAAQBIIogMAAAAAAAAAEAxB9D179qhq1aratm2bf96aNWvUuHFjFStWTAMGDJDH4wloGwEAAAAAAAAA2UeunBRAb9euXbwA+tGjR9W+fXs1bNhQK1as0Lp16zRr1qyAthMAAAAAAAAAkH2EKofo3LmzunTpoh9++ME/b8GCBYqMjNTEiROVP39+jRw5Uvfdd5/uvPPOgLYVAAAAQNYTHR3tnmQ9ePAgT7BCuXPnVtmyZXXOOecoV64ck38GAACCOYg+ffp0V8qlX79+/nmrV69W06ZNXQDd1K9f32WjJ8Uy123ysc6ziY2NdVOwsM9qNw3B9JkzA+c1/XFOMwbnNWNwXjMG5zVjcF4De14574EJnlvCzeeLPtfho4cJoMMvV0gulS9dXt1u6+aStgAAQHDKMUF0C6AnZEHwuPNDQkJcNsH+/ftdjfSERo0apaFDh54yPyIiQjExMQoWduNmGfx280DGRfrhvKY/zmnG4LxmDM5rxuC8ZgzOa2DP66FDhzK1XcHu2LFj6nN/Hy3fsFyX3H6Jal9cWwWLF1RIrpBANw0BdvL4SUX8EaGfPv1JIyaMcL8r3bp1C3SzAABAAOSYIHpiQkNDlTdv3njzwsPDXaZJYkH0QYMGqX///vGC8BUrVlSpUqVUuHBhBdMNnn3hYJ+bG+f0w3lNf5zTjMF5zRic14zBec0YnNfAnlfrryLzLFmyRN///L1um3ybqjSoEujmIIspULSA+73IWyCvnn/xed18883Kly9foJsFAAAyWY4OohcvXtzVNEyY2RMWFpbo+hZwTxh0N3aTE2w3kHaDF4yfO6NxXtMf5zRjcF4zBuc1Y3BeMwbnNXDnlXOeub788kuVrF6SADqS1eyGZlo5d6WWLVumli1bBro5AAAgk+XoIHrjxo1drXSfrVu3uprnFlxH6r2zOTJTj9epepFMPR4AAACC1x/b/1DZs8sGuhnI4oqXK668hfJq+/btgW4KAAAIgByd5nLZZZe5kiwzZ850722woNatW7u66AAAAABw7PgxhebJ0blFSCehYaGuLjoAAAg+Ob4m+owZM3TLLbdowIAB7tHYxYsXB7pZAAAAAAAAAIBsIscF0T0eT7z3HTp00ObNm7Vy5Uo1bdpUJUqUCFjbAAAAAAAAAADZS44LoiembNmyatu2baCbAQAAAAAAAADIZnJ0TXQAAAAAAAAAAM4EQXQAAAAAAAAAAJJAEB0AAAAAAAAAgCQQRAcAAAAAAAAAIAkE0QEAAAAAAAAASAJBdAAAAAAAAAAAkkAQHQAAAAAAAACAJBBEBwAAAAAAAAAgCQTRAQAAAAAAAABIAkF0AAAAAMhGTp44qZ2/7Ux02d+//63Yk7GZ3iYAAICcjCA6AAAAAGSivTv2avbDs3X86PFEl99R+g5t+mFTktvPnzJfo64bpcjdke79ieMn3L72bN+jR5o9oqVvLfWva/MTBtWndJuiH+f9eMp+9/+9X12KdHHtS8qIDiPUvVJ39a3X1033Vr9Xt5W8zf/eJtvHX+v/0ukYcOEAzZs0L968jd9vdPs8Gn003vwN321w82MOx5zWsQAAAFIrNNVrAgAAAADOWFi+MK1ZvEZT7pyiB155QLlDc8dbHpo31K2TmLVfr9X7495XoZKF1KtmLxUuVdhtf3Gni7X1560qWKyg5jwxR9N6T1Px8sV14tgJDXhrgKpdUM1t/+eaP7X84+W6/uHr3fvY2FjlyuXNrcqTN4/3Z7j3p8fjcdv75ru2h4fp6l5X68ZBN7r3tq/3Rr+nUUtH+dexwHbcbcyOjTvcvJCQEP+8AkULKH+R/PHWK1S80CnbJmyX/zyFhcb7CQAAkFHobQAAAABAAl+//rULRCdU+5La7mflepV1++jb/fMtO/u2UbepziV11K9+PzcvvGC4Kp1bSR0e7KALrr4gXqB44HsD9djlj7mM9Lsm3hXvGHEDzXHt27lPT9/xtG5+4mZdfe/VLuv8sq6XqW2ftlrw/AKt/mK1pqyZosP7D+uBBg9o4PsDVb5W+Xj7sAD8Nb2vUcU6FfXd3O/cuq89+lq8APWD5z3o2mBZ7BaUf27Dc/5luXKn7mHmhOu9MvAVbxA9l/ezrV+6XreOvFWXdr403pcIvuUW3JfHux/fvnzBfp/QPKcG0Vd9vspl6F9+6+WpaicAAEBqEEQHAAAAgAQss7tR20butZUo6Tyksy7qdJEL+I67eVyK2w9ZMMRlWS+atUgTOk9Q/zn91bBNQ//yEhVKqN+sfipUolCq21S8XHFNXDXRBbbfHf2uqjSoooMRBzV31Fy3vF7LevpgwgfudZvebXTy2Ml421uQfeOyjbrn2XtcJvzUnlM1+JPBeiXiFbc86kCUelTuoSm/TnFZ4omx7PRPp36qb974xr0/Fn1MUZFR7hwlXC+uQe8Pivf+nir36NCeQ7qtxG3uywZfoNxKs/y+/HeXTX/HmDvU6q5WSZ+QON81WNB9wXML9OaTb6rqeVV12S2XpTrgDwAAkBKC6AAAAACQgGU3+zKcLSs7LH9YkoHlxLgs9LqV1G1cN1djfN7EeS6IHvFHhI4cPuL2aaVYytUqp8iISFcmJW4G+rGYY/5a38eOHlP+wvld5rUF0M1Hkz/StQ9dqwq1KyR6/Jf6veSy5S0T3hz454CevftZF7y3rHCruX7LU7eo1oW10nRePLGeVJVzSViHfc6QOarRuIYat2+sf7b84wZHtUD/NX2uiZeJPqztMPflhS1LaMemHe5n7In4+96yaoteeeQVN6iqPR1ggXcC6AAAID0RRAcAAACADGTB85cefMkNAPr5i5/rp89+cqVSLIt78urJuu/s+7x1v/+NoR+NOqqRHUb6S5ucPH5SwxYNc0FxHws8L35lsfIWyJvoMS07PG5w2jLeLYBdtnpZ/b7id5WpWkbX3HeNW/Zw04e196+9/uP7ssqPxxzXhJUTVKpSqXjB/WXvLtMfv/7h3h/YdUC7/9itCV0mxDu+1VKP6+yLztZzPZ5zX0TYtudcfI77ksIGEb3w2gtVplqZFM/j45c/7j8fBYsX1MPvPOze2yCrV/W8Sg/Pfdh92ZAYC84nLMEDAACQWgTRAQAAACCNvnjpC39JExMdGZ1sGRYL/B7ed1hdR3R107ol6/R8z+ddxvpr+16Lt77VV3/848dVpX6VpBsQItW9rK5KVfkvwB3X/Gfmxyt3YgF1K+NiwXKr9/7Ep0/4lw1fPNxluSfM3ragvy8Qb5nlFtS//+X73RcAPr98+Ys+ee4Tl3HvY6+LlC4Sb19WE/6eZ+7RxK4T3bL2/dq7+ZY9/ubQN9VvtreOfHJm7pwZ7/22X7a5n0//+nSSwXMfG1yV7HQAAHC6CKIDAAAAQBpdfNPFuuHhG/zvB148MMl1kxoo9EyCuhbUtgB6UuVcLCjuORm/LvnBvQc16dZJqtmkpvsSYOdvO9Xp0U76Zs43+mzaZ6fsw0rBDPlkiHu98KWFeuXhV1zme9x225cDlp1ug5wmbF/T65rqnufu8c+78LoLXekVK0XjG6DVysI8cN4D2rZ6m6vxfjpSCqAb+7ICAADgdBFEBwAAAIA0ylcon0pV/i8L3Fd6JTH7d+1X7jy5XQmSxNiAnmmpt24KlyispW8tdXXTLaht7fGVYLFSLnnz5z2lTTbA6daftyomKsYFucufXV7Fzirmllkd8nun3utf17LVF7+62P++edfmurTzpd5BQHPlcgOTTr59sm545AZ9N/c7XXLzJS4r/ZF3H3HHPXLoiKufHpcd09YpWbGkXnv0NT005yFXo71JhybavHLzaQfRAQAAMhpBdAAAAADIQD99+pMbwNOywxPa8N0GV+JkzHdj/AHtpFgQ2kqsWP10K2FixncerxoNa+i6Adf5B/p8ddCrmvLrFPfeBvC0yQYu7T2ttwvWJwzmJ/UFQNyM8/AC/2VyL317qWb2n6lu47v5M7yvvOdKF0y3TPeez/VM9AuD98a85/Y54psRGnjRQH0580u1urOV7ptxnwvMp9bubbu1cPpCXXzzxaneBgAA4EwQRAcAAACAdGYZ4n+t/0tfvfqVVn6yUo9+8Gi85RbYPrT3kAuC22CXKQXQzcZlG132d57wPP4A954/97iBQhe9ssi9t8FKD+8/7B8cNPZErM656Bz1eamPG7zT4/Eo4o8IbV+/XX+t+0v1W9VP1eex7ayO+wfjP3AlWXpM6aGmNzR1QXtjgf2B7w/U07c/rX71++myrpe58i3nNDvHLV/w/AItnLFQT33xlAoVL+Q+8/S+011ZnLgB+kSP/W9Gu53P+c/Odxn49VvWd6VkEsvqf/buZ9VlWBdVrFPRP9+XsR+WLyxVnxcAACAugugAAAAAkM6eavOUy9Ku0aiGHvvoMX8w2ccCwhbgvm/6fa6USsKAdWKsjvi0LdPc6783/625I+aqaOmibhBSX3D4+/e/d6VSfJnoceuhD24xWAd2HVBsbKxKVy6tsjXK6oI2F7gg9bfvfKsV81f41z9x7ISqnV/N//6nBT9pYpeJLjA+9vuxKlG+hLetJz1uXWPBcWuLBbnnTZrngtb2uT+f/rneHf2uC7JbIN/Yfuy1L4B+4J8D2rdjn/tSwErRxLXph03+uvNNrm2iEYtHuHrtdg7N7yt/11nVz/KXobGSMZYlH9e4m8epcr3KLngPAACQVgTRAQAAACAZM/6cEe/94PmDk13njcg3Utyn1Ri3GuC1L/YOsBmX1TW3wToTY7XGx3Ya6zKum9/aXD2f7+lKtfhYQDuxba2GepenuuisGmep3Nnl4pWWseNd3OniU2qif/7i5/73Fmwf++NYla9VPt5+jx095g+i+1h9dJuOHfG24/LbLledS+qcMghqlfpV4gXpZ/5vpvvS4bwrz4u3nh37169+VecnO6vCOf/to2z1si7L/onWT/iz1a32fIf+HVSmqjdYn9w1AwAASK0QT1JpDtDBgwdVpEgRRUZGqnDhwgoWlpmye/dulS5d2tUmfGdzZKYev1P1IgqG84ozxznNGJzXjMF5zRic14zBeQ3seQ3WPmigdOrcSXnq5FH7B9oru7CgdWhY+uVDWfkX+/20QHugWK13y8zPyuVWJnScoJ439VSPHj0C3RQAAJDJyEQHAAAAgGwkPQPopmCxUwcBzWwuMz5PoFsBAACQOFKLAAAAAAAAAABIAkF0AAAAAAAAAACSQBAdAAAAAAAAAIAkEEQHAAAAAAAAACAJBNEBAAAAAAAAAEgCQXQAAAAAAAAAAJJAEB0AAAAAAAAAgCQQRAcAAAAAAAAAIAkE0QEAAAAAAAAASAJBdAAAAAAAAAAAkkAQHQAAAAAAAACAJBBEBwAAAAAAAAAgCQTRAQAAAAAAAABIAkF0AAAAAMiCog9GnzovMlonjp0ISHsAAACCVWigGwAAAAAAWcnxo8cVkitEoXm8t0snT5zUyeMnNeGWCdqwbIPy5M2j4zHHFRsbq7z587qgdoXaFTT8q+H+ffy14S9VOKfCKfuOPRmrIa2HqNu4bqrRqEay7RjccrAuv/VytX+gvX/ehC4TFJYvTI/MfSRdPzMAAACSRhAdAAAAAOJY8NwCvTn0zXjzWt3ZSqFhoer8RGe16d1Gc0fN1Z4/9+jeqffq69e/1uJXF/vXPXbkmMZ1Gqfal9ZW98nd3XY+K+av0JaftuiHD35INoi+ZdUW/f3b36rSoIrbX2jeUOXK5X2QuMLZ/wXnj8Uccz/DwsMULOwzW0Z+0TJFA90UAAAQJCjnAgAAAABxXN3rar0S8Yq6DOuiy7pcptf2vaZbR96qXLmTvn2Ku8wyxYd+MVQ7NuzQ8PbDdXjfYTffMtc/GPeBbhl6iwuib/15a5L7+/SFT1WxTkUVKFpA91S9Rz0q9VD3St214bsNWvjSQvfapp5Ve2rexHnp9tmtjZNuneS+JEjMrs271LNaT0X8EZHmfduXAU9c8YT70iGuI4eOaPLtk93nuav8XRrTcYx//wf+OaCHmz6sR5o9osiISDdvyZwlOnL4yGl9PgAAgNNBEB0AAAAA4rAgeNzscQuQW9kWhaR+H5YlPeiDQapzSR2FFwp38xY8v0Aej0dt72+rO8beoafveFoH9x5MNFD93TvfqekNTVXt/GqatWuWXvrrJc34c4ZKViipbuO7udc2zfx7pm589MZ0y/B+odcLWv7R8kSX/7X+L426fpQO7T2U5n1HHYhypWh++/G3U5a9MfgN93PMd2M0fPFw5QnPo2fuesbNWz5vucvGr1y/snttQf5/tvyjs6qfleY2AAAAnC6C6AAAAACQGh7p3dHvqk/tPq7ki2WT2+vXHn3Nv4oFeS1j2oLGnliPbhp8k6utblnn7499Xz2f7+mC8g2vaagmHZpoRPsR/gxrn1cffdXVTg8v4A2+/7zwZ91W8jaXkZ23QF598uwn7vWdZ92ppW8tTbePN/Ohma6ttS6sdcqymKgYF0C3LP3TYZnmVr6mZMWSpyyzOvPNuzZXiQolVK5mOTW/tbl2bNrhlh3af0ilKpVy06F9h7Ry/kpdcM0Fp9UGAACA00UQHQAAAADiiDkc47LBre62BY/ttc0zHQd21LPrn1Wb+9rowusudK+t1ItlmJuDEQfVq0Yv9ajcw022/dpv1mr0DaNdcNhKl/jc8tQtqtGwhh5r/piWf+zN/l7y5hKtXrhaZaqV8a9nA5kWO6uYxn4/Nt5U7YJq8TLmfdYtWacuRbq4QH5aXNv/Wt3z7D3KnSf3KcssuD504VA1uqaRTsfdk+5Wp8c6JZrNX6luJX0+/XMd3HNQ+3ft12cvfKbzrzrfLctfOL8rh2OTvbaA+znNzjmtNgAAAJwuBhYFAAAAgDg2/bBJ0+6bpqPRR3XyxElXh7z3tN4uOzwpvmVFShfR7N2zdWDXAfWr388FwK0Oug1I+s/Wf7Ro1iLVvri2W9f2a4OPVqhdQbu37nbztv2yTR0e7KDt67f79x0SEqJ9O/a5rPe4LOh8ZY8rT2nL2c3O1vQ/pit/kfxp+txlq5dNcpkF6y2L/HRqoae079vH3K5BFw/SvdXvde/Ln11e98+8370+/8rz9cH4D9w56Dqiq2qVOTVLHgAAIKMRRAcAAACAOOq3qq/nNjynj6d87OqA3zvVG9z94uUvXDkXm47HHHelW1bMX+GWValfxf20YK8Fzn1yh+bWo/MedfNtsE57b1nV74551w2Q2eK2Fuo8tLObb7o81cWtO+m2SfHaVLx8cU35dUq8ecPaDku0/bYvG5A0u7AyMlXPr6ruk7srNG+o3hv9nkZdO0rDvhrmMvKnrPF+7neGveNKufS/oL/7oqDncz0D3XQAABAkCKIDAAAAQCr0f72//7UFxPf8uccfYE+OBcV91ixeo8GtBuuyLpdp8s+TVbB4wXjr+oLpCVkmet96fePNi/wnMtFM9OzESubYgKGjlo5yJWvMbaNuU/dK3bXp+02qfUlthYWH6e/Nf6t0ldJaOH2hWt3ZSp+/+Lmrm16+VvlAfwQAABAECKIDAAAAQBJOHDuh79//Xof2HNKi2YsU8ae3nEnCTHQr5/LyjpdP2d7WeXXgq24gUVOxTkX1f6N/vGz11EhLJnp2YufH6skf+OeAKp1byc2Liozynt845XOWvLFEHfp30I/zfnRZ/zYI6aG9hwLYcgAAEEwIogMAAABAHFYL3QYDtYDtlp+2uMFAW9/d2g2smSc8j780S9xM9GMxx+LtwzfQ6MgOI12AuGW3lv6a6QkD6NtWb1OVBt5yMElJSya61XG3gVCtJnrcLPiMZJ//5PGTylcoX5q2K1isoM6qcZZeH/y6O0923r58+Ut3nmo0ruHWObj3oDvv4QXCXZkaG+jVBiC1bQEAADJDrkw5CgAAAABkExY4n9pzqs69/Fw9s/YZDXh7gM6/6nyF5QtLMihtJUfiWrdknftpJUqGLx7uMtBt291/7HbZ7cYCxis/WalHL3vUHTMuC0hbMPz40eNuPV8metzJF2S2deLauGyjelTu4UqlZJZ5k+bpqTZPnda2/3v7fypdubTeGPyGXnrgJX/pnLz587rXVu7l8tsud6/ty4x3RryjSnUquQFIAQAAMkOIx5cigVMcPHhQRYoUUWRkpAoXLqxgYY9U7t69W6VLl1auXLn0zubITD1+p+pFFAznFWeOc5oxOK8Zg/OaMTivGYPzGtjzGqx90EDp1LmT8tTJo/YPtI833wLQlsntM/XeqS4z3Vez3FfOxRfotdIjl3a+VHdOuNO7/cFoLf9ouZp3be7fx9qv17oBQ+MGt3Pnya0Wt7fQXRPvinf8ER1GqM6ldVztb8uED8mVePDeE+txwfbp26YrvGB4upwTJG5CxwnqeVNP9ejRI9BNAQAAmYxyLgAAAACQQNwAuun+dHf1fL5nkl+AWBDdgtn+7QvnjxdAN3Wb19WMP2ek6viPzXvM/bx+wPWn0XoAAACkJ4LoAAAAAJCClAYCzZU7l5sAAACQ89DLAwAAAIAM8Nf6v/wDjMYV8WeEG7w0JVYS5pR5kdH+muoAAADIHATRAQAAACABC1S/OuhVF/BOzOgbRuuD8R8kuf2mHzbpscsf0/ql6/3lXo4dOeZ+ju00VrMGzPKva2VgEguMD245WB9N/ijevAldJrgJAAAAmYdyLgAAAACQgA34eeCfAxrZYaSGfDpExcoWO6W8i29Q0YT27dynZ+9+VoVLFtaYG8coNCxU+QrlU9lqZXV2s7O1/+/9WvvNWnUp0kVFyxR1g4a269dObXq18e9jy6ot+vu3v1WlQRUXfA/NG+qvx17h7Ar+9Y7FHHM/w8LDFCzsM1tGvp07AACAzEAmOgAAAAAkEBISol4v9FKxcsU0puOYeIOGeldIfDvLNH/6jqdVtUFVPbP2GbW4vYVqX1xbz657VrcMvUWfTftMw78arim/TlHFOhXV6fFOem7Dc/EC6ObTFz51ywsULaB7qt6jHpV6qHul7trw3QYtfGmhe21Tz6o9NW/ivHT73LGxsZp06yTNHTU30eW7Nu9Sz2o9FfFH4hn6Sfl7898a3m647ix3p/u5Z/se/7KYwzEuM79vvb66p8o9ev6e5xW5O9Itsy8yHm76sB5p9ogiI7zzlsxZoiOHj5zR5wQAAEgLMtEBAAAAIBGWQX7/y/frtx9/U+7Q3KnaxgYXffzjx+WJ9eibN75xwfdSlUr5g9KX33a5lr691L2+oM0FLls9sUD1d+98p46DOqra+dU0a9d/pV/61eun6x+5XpfferkyIsN7Rr8ZWv7RclWsWzHRGu/jbh6nQ3sPpXm/o68frcr1Kmv00tH6/v3vXaB+2FfDXHb9C71fcEH5h9952D0BMLP/TBdoH/3daC2ft9xl4xt73fLOlvpnyz9qdWerdPvcAAAAKSGIDgAAAABxHNx7UAf+PuDKrJjzrjxPh/YdUu7cuV2Q3JdxbnXMLYva2GsruRJeINyVejHL3lvmyrg069gs0eN89sJnrixJw2saxpv/6qOvuv3bvszPC3/WhFsm6KwaZylvgbz65NlP3GSB57sn361Lbr4kXT73zIdmKjRPqGpdWOuUZTFRMRp1/ShXdubVga+mab8/zvvRZZb3eKaHChUvpGsfulZL3lzi6saXP7u8C44P/mSwKpzjLVNz++jbXfb59rXbdWj/IfclhLFrsHL+Sl1wzQXp8nkBAABSiyA6AAAAAMSxfsl6vfXUWy4reseGHRr7w1i90OsFbV+33R9EPx5zXL8u+lXvjX3Pvbegd+cnO8cry2LZ6+u+WecyuJOqnZ4w49uCy6sXrlaZamX88ywoX+ysYhr7/dh46w5rO8xlyye0bsk6l8k9/Y/prhxMal3b/1qVrV7W7TchC64PXTjUZdinNYi+bfU2Vbugmgug+9RsUlO/L//dZeJ7PB5/oNzYeTd2rvMXzu9qw5tytcppw7INum3kbWk6PgAAwJkiiA4AAAAAcVx43YVuMjb4Z56wPK6OeVwTukxQnUvqqE3v+LXME9ZVr1C7guo2r5vocsvAtnXi2vbLNnV4sIO2r98ebz/7duxTn9p94q17cM9BXdnjylP2a4OXWgA9f5H8SgsLoCfFgvUlK5ZMcy10Y9n2ZavG37cF922AVQueFypRyJV4aXt/W7fsi5e+cPMr1KngBm/9YPwH7hx0HdFVtcqcmiUPAACQ0QiiAwAAAEAyfNnnaWXZ6YVLFXaB9MSs/XqtWyeuLk91cQHjSbdNije/ePnibjDSuBLLGPdlwKclAz2jWXsSnsOwfGGK/CfSZdn3frG3pt47Vb8s+sWVx7Ea9N3Gd3P10i0jf8oa7+d+Z9g7rpRL/wv6uy8Kej7XM0CfCAAABJugCKLPmDFDQ4cO1d69e9WkSRO9/PLLqlatWqCbBQAAACCbiDoQlebAtNVD37xys5uORh/1DyJq5UssC9tKldjAoXElNYCpZaL3rdc33jwLQieWiZ7VWKb5jo074s07cvCIqyFvGrRuoOc2POfKtrz++OuuTnrcgUPDwsP09+a/VbpKaS2cvtAt+/zFz7Vj0w6Vr1U+0z8PAAAIPjk+iL5582Y99dRT+vDDD1WyZEkXTO/WrZu++eabQDcNAAAAQDZwYPcBjbxupLoM66LG7RqnuL4FzPOE51Gfl7zlV+Y/O9/VOX/0w0f9AfkelXto1JJRKlW5lGJjY12NdStdkpS0ZKJnNVb/fPFri13WvS8jffNPm+OdS5etHppLaxav0cD3Bp7yZcKSN5aoQ/8ObpDSKvWrqESFEjq091CmfxYAABCccnwQfdWqVWratKkuuMA7gvtdd92lTp06BbpZAAAAALK4kydOup8Tu0xUo3aN1PCahiluY4Hi++ve78qU+AbIjNwd6WqK+zLJbXBOM/TqoS5w7AsuJwySn24murXbyqJYTfSENdczyrGYYzp5/KTLvk+oXst6btnHT3/sAuE/ffqTy85PWI7ljcFvqMm1TU6pIX9w70H3pUR4gXD3NMCuzbu0f9d+FSxWMMM/FwAAQFAE0evUqaNFixbp559/VtWqVfX888/riiuuCHSzAAAAAGRx29d5B/ds3rW5Oj/ZOf5Cbxz8FBYMf3Hri+71oX2HtHDGQn358pd6dN6j/tIjNiDovdXv1eMfP57kYJ4WdLZg+PGjx135l+Qy0W0dC9r7bFy2UcPbDXeDi2ZWbfR5k+Zp5fyVGrV01CnLQvOE6r7p92nKnVP0yfOf6PC+w+o4sKMr2+Kz+ovVWv/teo1fPj7RAVgvv+1y97r13a317N3P6uymZ8fbHgAAICMFRRD9xhtv1Pnnn+/eWyD9hx9+SHTdo0ePusnn4MGD7qc9XmlTsLDPah11/2f2ZO5nz6nn+pTzijPGOc0YnNeMwXnNGJzXjMF5Dex55bxnHVY25KE3H1LDNg0Tzbw+duRYktvOfGimVn22So07NNbwxcNVrGwx/zILesf9mRhbZlPfc/vqyKEjCskVojvL3XnKejYgpwXbp2+brvCC4W5enUvr6I3IN3S6Bs8fnOQyKz+T2L5vHHSjm5JSv1V9Tf55sjZ+v1FlqpY5ZbBVq4v+0vaXEt02bn30cy8/Vy9sfiGVnwQAACB95Pgg+o8//qiPPvpI33//vc455xyNHTtW11xzjZuf8NHGUaNGuZrpCUVERCgmJkbBwm7cIiMj3U1erly5FHIoKlOPv3v3f19k5OTzijPHOc0YnNeMwXnNGJzXjMF5Dex5PXSIOs9ZSWIBdDPo/UHJbnfb6NvUbXy3RMuplChfIsUg92PzHnM/rx9wvXKKgsULpqokDgAAQFaT44Poc+bMUefOnXXhhRe698OHD9fUqVO1evVqnXfeefHWHTRokPr37x8vE71ixYoqVaqUChcurGC6wbPOvn1uu8HzHI7M1OOXLl1EwXBeceY4pxmD85oxOK8Zg/OaMTivgT2v4eHebGJkb1bCBAAAADlDaDDcrOzZsydeZk90dLROnvQOEhRX3rx53ZSQ3eQE2w2k3eD5P3dI5n72nHyu451XpAvOacbgvGYMzmvG4LxmDM5r4M4r5zxrOHH8hKvxfeF13mScMxEdGe0G+Yxr3859Kl6u+BnvGwAAABkvxwfRL730Ut1xxx264IILVKZMGc2YMUNly5ZV/fr1A900AAAAAFnUnj/36KUHX9KOjTt0wyM36J8t/6j/Bf2Vr3C+eOt5Yj0qWKygnv716UT3E/FnhAZePNDVGbca6+afrf/of43+p8GfDFatC2tlyucBAADA6cvxQfSOHTtq/fr1mjx5sv7++2+de+65ev/995Unz3+j1wMAAABAXGWrl9WjHz6qp9o8pZqNa+qsmmepcKnCmvrb1HjrbV+3XZNvm3zK07Anjp5QnvA8KlWplC7qeJF++/E3fxD9s2mf6fyrz3cBdKuRbwOIhoaFBtVTCPv/3u9qpOfJy30ZAADI+kKD4ZHZwYMHuwkAAAAAUsuC3kMWDFHlepW1b8e+pFdMMHbo9rXbNaTVEBcYD8nlXfjd3O8086GZ8cq6dK/U3WWynzxxUmO+G6My1cqccZt/X/m7Zj00Szt/26l6l9dTj2d6uGB1akTujtSMB2Zo7ddrdVaNs9TzuZ6qdG4lf3mb98e+r2XvLlNkRKRqX1xbt4641X3ZYGJPxmrOkDn6+vWvFV4wXF2e6qKmNzR1y9Z+s1bP3PWMzrnoHD3wygNu3qcvfKqbh9x8xp8XAAAgM+T4IDoAAAAApIUFhC1DPHdobn/2uAWRD+45qL71+sZb9+Txky5YHpcF3Wf+PdMF133Z5T99+pNm9J2h5zc9H29d268l/tixzpQFwUdfP1oX3XiR+s7qq/nPztf0+6frwdcfTHFb+7wTu050r4d9NUybV2zW+FvGa+yysS4o/tbQt/TjvB91/8z7VaxMMc15co6evOpJTVgxQQWKFtDbw97WkreW6MHXHlTusNwuO9++FKh6XlV98dIX6jiwoz559hPt2LRDYXnDVLx8ceXKHTyZ9wAAIHsjiA4AAAAAcWz+abOeaP2Ee20Di/ab3U9lqpbR6/tfT/U+vnv3O71434sKyxfm3h87csyffe5jGegWhL915K26sseVZ9zur2Z/pfAC4bp9zO0KzROqrsO76r6z79PeHXtVonyJZLfd9MMmV3Jm7A9jVb5WeTdZ9vyK+St0yc2X6KtXvtIdY+9QjYY1vJ9jcnfdXeFurVuyTudfdb4+e/Ezl31e+5LabvnVPa/WwhkLdc+z9+jwvsMqV7Ocip1VTIf2HtKar9aoXd92Z/x5AQAAMgtBdAAAAACIo2iZomrfr71+W/GbP8vc6pZbaRYLTieWuW4B8bj1vS+56RI3+djgohYob9mtZaraMHfUXK2cv1Kjlo5Kdbu3rt6qus3r+tsYFh7msuJ/X/57ikH0bau3qWTFkqpwTgX/PKvZ/tvy39wXCdGR0a6+u0+u0FzeTPvcuVzpmKNRR9WgdQP/8poX1tTSt5e61/kL59ehfYfc5DnpUe48uV12OwAAQHZBEB0AAAAA4rBg8S1P3eIC2f9s+cfNm9Z7msu6tqDxsZhjLqhswXYrg2JBdKsdPuj9Qf6s89C8/w0UenDvQVcnvVbTWokez0q6nDh2wmWR+3R4sIPa9mmbpnZHH4xW5fqV482zUiv7/t6Xqm0t2z7httt+2ea+HKhYp6K+f/97V9fcfDnzS+XNn9dlnv+59k93XkpXKR3/uDu9x734pov1XI/nvAH9Fb/r8lsvT9PnAgAACDSC6AAAAACQjNjYWPV8vqfLoLbA+OJXF7uBMUd/O9q/PPZErAuGWxb44JaDFfFnhL/md8zhGJfR/uSVTya+/5OxanZDMzcIqI9lkSuNydpWVz1uNryxQPfRw0dT3jZ3buUJj7+tlaKxtpt7p96rSbdN0h+//uE+y7pv1umaPte4LPNEj5svr8tON02ubaJzW5zrzs27o9/Vio9X6KOnP3JfFLS+u3XaPiQAAEAAEEQHAAAAgBRKnQy9aqgLHltJFwssW+DbaoLbeyvlYpnk1/3vOjeA5phlY/zbbvhug569+1k99tFjOqvGWafs28rEJAxAn65CJQq5wUUTZphbVnxqtj3wz4FTtvW1zQYIffqXp7Vz007Nf2a+W//6Add7ty1eSEejj+rIoSPKVyhfose1YLt9+WBZ6SOvHam7Jt6lmQ/NJIgOAACyBYLoAAAAAJAEK9eyeeVmzfpnlkJCQlxd7wcaPKACRQqo9V2t1aF/hyS3Xfv1Wk3tNVVlq5fVw00fdtnllq1uLDhtg4paaZi0DFianJpNamrJnCXx2r71563xarMnt+2sAbMUdSDKlWIxW37aohIV/qulbp+/SKki+nHej7p1xK3KXyS/m1+6amkVKV1EG7/fqPOuOM+/bckKJeO1Zfv67br8tsvd4KIXtLlAU7pNSZfPDQAAkNEIogMAAABAIizb3EqPbF211Q2uacFlyyqv1aSWOj3eSSM6jFDV86uqXot6p2SXfzD+A301+yv1f6O/ajSq4V/20gMvucz1e569J9ljW3Ddguy+zO7UuPDaC/Xao6/pu7nf6aIbL9LCGQtdUNxKqRjLlrd67b7gd1wValdQuVrl9NZTb6nb+G7645c/tPyj5XpozkPx1rNyLOXPLq/mtzb3z7MSN806NtPcEXN1dtOz3RcFnzz/iRq1beRf5+fPf9Z5rb0BdjuPG77doILFCqb6swEAAASSt0gfAAAAACCeX778xWViP7nwSVfCZXi74dr71171mtZL1c6vpm7jumnCLRP0+uOvu/kmJipGj172qAsaWwmXMtXKuG19kwXQLTjve28lUGzgUQuaxzVv0jw91eapNLXXssG7T+muafdNU+9avfXqoFddG33B6m/f+VZ96/VNcvuez/XUyk9WqleNXhrSaojLYG/QuoF/+fZ127Vo1iLdPelul5Ue142DbnQ14/vU7qO+5/Z1WffXPnStf/mWVVtUr6X3y4Z2fdu5+urt+rVL0+cDAAAIlBCPPVeHRB08eFBFihRRZGSkChcurGBhAyPt3r1bpUuXdlkl72yOX1cxo3WqXkTBcF5x5jinGYPzmjE4rxmD85oxOK+BPa/B2gcNlE6dOylPnTxq/0D7U5ZZKZSw/GEuo3zB8wtcPe/bR92ugsX/y6D+bflvmv3wbFe+ZNAHg1xW+p9r/lTZGmW15I0lemXgK6cM2BmX3Y6dOHpC902/zw3AmR7279qv31f8rkp1K6lM1TJp2ta+BFj/7XoVLlFY1RtWT/Pv+MZlG93nqX1pbTeQaE4yoeME9bypp3r0+G8AWAAAEBxyVq8GAAAAANKJDaZpruxxpVrd2SrRgUFrNq6p4V8Nd4HzSudWcvN8P1vd1cpNma1Y2WJq3K7xaW0bXiBc5195/mlta18O1b649mltCwAAkJURRAcAAACAZJSuUjrFdXyBcwAAAOQ8PJ8LAAAAAAAAAEASCKIDAAAAAAAAAJAEgugAAAAAAAAAACSBIDoAAAAAAAAAAEkgiA4AAAAAAAAAQBIIogMAAAAAAAAAkASC6AAAAAAAAAAAJIEgOgAAAAAAAAAASSCIDgAAAAAAAABAEgiiAwAAAAAAAACQBILoAAAAAAAAAAAkgSA6AAAAAAAAAABJIIgOAAAAAAAAAEASCKIDAAAAAAAAAJAEgugAAAAAAAAAACSBIDoAAAAA5FD/bP1HW37aotjY2EA3BQAAINsiiA4AAAAAKRjccrAWTl8Yb97CGQs14MIBia5vgWuPx5Posul9p2vOE3OSPd7R6KN6uOnD+nXRr/HmT7lzipa+tTTV7f72nW/d8TyxibcFAAAAKQtNxToAAAAAEHS2r9uuMTeOUd78ebVvxz59MOEDLXzpv0B6dGS0og5EuWC3Bb3vnHCnzrviPB3cc1BjbxqrEuVL6I6xd6jWhbXi7Tc0T6jy5M2T7LF/+fIX7dm+R9UuqBZv/gVtLtDOjTuT3XbZu8v8gfZtv2xTvkL5NLHLxFPWa3VXK11w9QWpOhcAAADBjCA6AAAAACSiYp2Kenbds+71sLbDdMlNl6jFHS38y79+/Wt988Y3Gjx/cLztCpcsrAkrJujVR1/Vpy98qkIlCumhhg8pV27vg8C+rPAPxn/gstVDQkI0488ZCi8Y7t/HFy9/oYtvulhfzvxS7456V7lCc/m3t/U/e/Eztx+bThw7oVf3vurf1oLvNq/7092T/GzP3PWMDuw6kG7nCgAAICcjiA4AAAAAqTBrwCy9MugV//vYE7Gq0bjGKeudPHFSBYoW0L3P3+teR+6OdPNf2/ea+znzfzNdYP3GQTe6QPqPH/4YL4C+bfU2V8aly7Auate3ndr1a6dcuRKvxOkLwsdlwfbfV/yucTePS/KzRPwR4Q/KAwAAIHkE0QEAAAAgFW4acpMu7nSx//2y95ZpxccrEs3yLnZWMXV+orMrBZM7NHeS+7Rg+8U3/7dP89pjryl3nv+2eWPwG/rm9W+UJzyPd18h3mz2k8dP6uiRoxr3wzgVL1fcv35IrhDVuayOHnrjoSSPa5n1th4AAABSRhAdAAAAAFLhwwkf6rNpn/nfxxyOcSVfErIyLK8OfFVrvlqjR+c9ekqmeFxXdL8i3vtFsxZp56adatimoX/erSNudVNqWYB93TfrXK325DLRL+18aaLLuhTpogdff1CN2zVO9TEBAAByMoLoAAAAAJBImRQbLDQsPMxf9uSWJ29JtCa6iY2N1fGY427AUAs+176othZMXaAipYto7197/cHpuN4b/Z77OfC9garfqr57veevPeo6oqsr5+LzysBXtGTOEoXlC/NnossjnTh+Qkejjmrcj/Ez0WNPxqYqE93WS8z0P6Yrb4G8p3XeAAAAciKC6AAAAACQwOH9h9Wndh/lL5zflVE5cvCIXv7+Zb0//n3/OtGR0W4Az771+rqfts5Dcx7SuZefq4LFC6r13a0VfSDarWflXe6adJfmTZynoQuHuu1/+fIXTblzimpfUtu/z5sev8n9jBtEv3307W5KLauvvuv3XclmolsAPSx/WJIlZgAAAPAfgugAAAAAkECh4oU0+5/Z7vWe7Xs0ov0I3TzkZl1+++X6dOqnrmSLBc6HXj1UXYZ2UdMbmp6yjzlD5riM9sbtG6tEhRJq0KqBXrj3Be3YtEPla5XX59M/dyVVLHs9OUveXKKPJn+U6DLbdsTXI04pEWPT37//rcIlCyt/kfz6acFPOv/q8/0DlFoQff+u/WdwhgAAAIIHw7EDAAAAQCKsnMsnz32ix1s8rit7XqmW3Vq6ILQN9GnZ5RYYt5Ipbw59U8/f87y2rd7m33bVZ6u0/OPluuGRG/TDBz+oZpOaCg0L1WVdL9PcEXO1+ovVWrN4jdr1bZdiO2IOxbiyMGO/Hxtv6v96f+3etjveukvfWqqPp3zsXo/tNNYdJ+pAlKb1nqaFMxb61/v0hU/1VJundHDvwXQ9ZwAAADkRQXQAAAAASGDvjr2unMvab9ZqyIIh+vbtb3V3xbt1T9V73PIhrYaoe8XuGnfzOI3+drQKFCugJ654Qr+v+F0Rf0bohV4vqMtTXVwW+PKPlqvZDc3cdtf/73qt/XqtK+NiAXYLxCfGMth9QnIlPTBpwmWLX13sn2f10y1wX7BYQVdnPV+hfG5+ZESkrrznSpWqVEqTuk5yGfVxWdDd6q0DAADAi3IuAAAAAJBAifIl3ICdVl/cSqYM/2q4f5kNEGqB81KVS/kHFbVBR6976Dq3rgXTK9Wt5GqiT713qmpdWEs1GtVw6+7+Y7cbINRKqRw5dETRB6Nd3fWEjh89Lk+sN5BuPzd8u0HdK3WPt47N9w16aras2qKNyzbqvhn3ufchISH+AHnzrs2923g8evjCh9XnpT5uvXE3jXMDn5apVsa/nx6Ve+jB1x90A6QCAACAIDoAAAAAJKpQiUK6o/QdLjCeO09u/3zLLh906SD/ewtU2zR++XhXduW8K85T6+6tXXmXX774RY9++Kh2/rZT86fMdyVebPBQK+8yo98Mff7i5zr/qvPV5NomatKhiX+fByMOukC62//xEzrn4nP02LzH4rVv1+ZdGnjxQP97O3bvF3urWNli7v05F53jjmHlZnyOxxx3g5xaYD9v/rwa+c3IUz73G5FvpNs5BAAAyAkIogMAAABAIqwcymv7XkvzdlY6xeeZdc+4ci5vPvmmq4c+ceVEFSxe0C0btmiY1i9d7wYYLV25dLx9PP7x4/7XbXq3cVNCZauX1axds+Jlzzfr6C0bY+6efLebAAAAkI2D6EuXLtXbb7+t1atXKyIiQgULFlSlSpV0zTXX6MYbb1ThwoUD2TwAAAAAOCOWxX7RjRe5KTG1L6ntJgAAAGRdAQmib9y4Uffdd58KFSqkrl276uGHH1bp0qV1+PBhbd68WR999JEuvPBC/e9//9Pdd5M5AQAAAAAAAAAIkiD6xx9/rEceeURTp07VZZddFm9Z8eLF3dS4cWM98MADLtC+bNkyzZgxI7ObCQAAAACZ6ljMMeXKlUuhYf/dptlAoFbH3ObFHUQ0Lltn+7rtKn92eVeCBgAAANk8iF61alUtWrRIZcr8N/p7YiyYPmfOHH311VeZ1jYAAAAAMD99+pM+GPeB8oTnSXFdG1S0ZbeWat61eZqOsXfHXt1f535X1zwsX5hef/x1rfh4hQuW79uxzw0AGpIrxO1/wFsDVO2Caonu5+Ceg3rsssfU/43+bpBSAAAAZPMget26dZNctmHDBs2cOVP79u1T0aJFddttt6lFixaZ2j4AAAAAMPe/fL9KVS6V4noH/jmg1V+sTvV+f1v+m/5a/5cuaHOBe+8L1N85/k43nTxxUreXul3jV4xXeIHweNv+8MEPmvPEHLdNSEiIf74F26f1nqbCpf4bV8qC7wWLFdRTXz6V6rYBAAAgi9RE/+uvv1ywvH379jrvvPP88zt27Kju3burTZs2rm66DTBq6wIAAABATrF97Xb98OEPati2oXvfq2YvHY0+qtHfjlaZqmV0YNcBF/xOGEA3lmleqlIpVaxb0Q1aaqVcxt883g1cet3/rtPxo8f9839d9KvKn1M+3dr96GWPatvqbfHmTf9jugoULeBefzT5Iy14foE7docHO6hN7zap3vfPC3/W64+9rn1/71PT65rqjnF3KCw8zC2LPRmrOUPm6OvXv1Z4wXB1eaqLmt7QNFX77Vuvr9r0auNvS9SBKPWo3EOPf/y46lxaJw2fHgAABLOABNErVKigxx57zNVH/+CDD1SzZk116tTJBc+fe+45lS9fXrt27XLvAQAAACAnsXItcWuXv/D7C/7XX83+Sv9s+8cFwr946Qs3z0q9XNblMn/d9Kn3TnXB8e5Pd3dBa3NVz6s0d+RcrZi/Qk98+oS+euUrvTvqXd079V6VKF/ijNscExXjgv8TVk5Q4ZL/ZbvnL5Lf/fzy5S/13pj31Ht6b5WtVlaTbp2k0lVKq+E13i8KkvPn2j81sctEXf/w9e7LgDcGv6G3nnpLt428zS1/e9jbWvLWEj342oPKHZZbk2+brDLVyqjqeVXP+HMBAABk2SC6sQFzLHhutc93796tMWPGqGTJknr33XdVokQJFSlSRIUKFQpU8wAAAAAg3VlWtWVqxy3FYqyEi81/ffDrqtm4pmo1raV1S9e5zGkr/eILoluG+rBFw/T5i59r+bzlen/s+6pcr7KGXTPMZXE/+sGjbr11S9ZpyIIhbll62Lxys0pWKqmzapyV6PKPn/lYV/a4Uo3bNXbvb3z0Rn36wqepCqJ/9sJnrp3XD7jeve82rpv+1/h/unnIze6+8bMXP3PZ57Uvqe2WX93zai2csVD3PHtPunw2AACALBtEt1IuUVFRqlSpkvbs2aMdO3Zo6dKlLoi+efNmNW7cWG3btj2lcwkAAAAA2dXG7ze6jHHLRH/88sddYLrvuX0VGxvrgsiWgd5lWBdXssVKl2z5aYsm3zE53j5svpUzsbroNpjovp37NG/iPD38zsMuq7vSuZV0+W2Xq2jZoom24YVeLygqMkoPvfFQqtu96YdNruRMn9p9dGjfIdVoVMNlildpUEVHDh3RP1v+Uf3W9f3r17qwlmb0m5GqfW9dvVUXXO2tD29sQNWCxQvqr3V/KTRvqI5GHVWD1g38y2teWFNL316q9PDkVU9q0/eb4s27YeANunHQjemyfwAAkDMELIi+adMm3XTTTapWrZr27t2r0aNHuyyD22+/3S1fsWKFnnnmGfXt2zdQTQQAAACAdFX74tp6dv2z2r5uu4qUKqKxnca68itWssVYgN082/1ZV4aledfm8ba3YPviVxa7cicPzXnI1U+3EjAD3h7gSr289OBLijkc44LpFsTu9UKveAFoc+eEO13We1rs3LTTBcYtuGxtfXv42xp38zg9/evTij4Y7daxMi4+Vifd2hEdGe0v+ZKUIwePuPIscdn2lllvwXQrf2OlYeIt27kv1W1/c+ibenf0u+51ws898N2B7ikAs/KTlZo1YJaatG+S6n0DAIDgELAg+ldffeXqoVsGupVtWbZsmfLly+df3qhRIzcBAAAAQE6yZvEaPXv3sxr+1XBt/Xmry7Yedd0oXfvQtf51LHBcvFzxU7adO2KuG5R08PzB+mvDX3pl4CuuxIoN+mkDZdZvUV9znpyjyasnu5rhS+YsUf1W9eM94Zs3f940t/m+6ffFe99jSg/dU/Uerf16rb9kjGXGJzyG1VJPKYieKzSXy8CPy7a3bYuULnLqsnx5XXZ6arXr206X33q5e21Z8wMvHhgvq9/8ueZPzX5ktnq/2Ntl8gMAAAQ0iL5o0SKdddZZql27tnr37p3susePH9crr7yiu+++O9PaBwAAAAAZyWqFX3XvVcpb0Bto3vjdRpc5XqNxDXlivZnSJ46dUJEyRU7ZtkP/DmrXr53yF86vcrXKuZrjFmS+s9yd6jioo85pdo7279qvQsULqcXtLdyUESzIbW2I+CNCdS+r64L0B/454B901DLQjX1BkJJCJQopcndkvHmW3Z4nLI/7HFZGxoLf+Qrl8y9LzX59rI58qcql3GurMZ9QZESkxncer2v7X6tGbUnkAgAAWSCIXrBgQd1www16/PHH1bVr1yTXs7rod955p1q3bp2p7QMAAACAjGLB8aoNqrrBMU+cOOHmvdD7BXV6rJPCwsNcSRZjWeYdHuzg38ZY3fFHLnrElVOxdeM6Fn1Mk2+drNCw/27xrHTJ8Zjjrn76XRPvOu02W7mTQZcMcjXXS1Ys6ebt2rzLBb5LVCzhjmlZ7xuXbVSlut4s7s0/bVbeAnldEDwlNpCqbXtVz6vceysDY+VjbN+lq5Z22ehWS/68K85zy61OfMkK3nacqeNHj2til4k65+Jz/OcbAAAg4EH0Jk2auFIujzzyiCZNmqQuXbrowgsvVJkyZXT48GEXPP/www/1888/a+zYsbr66qszu4kAAAAAkCEs4Hzjo95BK9cvXe9+nn/V+WrZraV7Pfb7sS5IfOzIMVU4p4LLKu//en+3zOqGz9o1K9H9Wib6A6894DLRU2KZ3RZgDy/gLWWSEhsE1doyrfc03TTkJhewf+2x11T+7PKq39I7mOjFN12sj5/+WI3bN3YZ6h9O/NB9Ll8ZGQuMW4kaX+33uC7udLGGtB6i9d+udzXjrX65ZbRXO7+aGzerWcdmrozN2U3P1onjJ/TJ85+kW8b49L7T3bnuOqyrP0vdPq+vzAsAAEDAaqKXLVtWs2fP1pYtW/Tee+9p2rRp2r17twoUKKDKlSu7DPWXX35ZoaEBK9kOAAAAABnCssy/fPlLfTnzSxdQv27AdfGWWWD33qn3au03a/XSAy+p2/huqtGoRvI7tSowqRwrdOZDMxUVGaWH3ngo1W2+e/LdbtDSkdeOdOVjLIjd+cnOLuBsruhxhdYtXacHGjzgvigoUKSA7nvxvzrqNgip1U6/ffTtp+y7SoMq6jiwo0Z2GOkGErVa6A+88oALoBsbzHTMjWPUp3YfN7CqDWAat378mVj65lL3s1fNXv55llU/4usR6bJ/AACQMwQ0Sl2tWjX973//C2QTAAAAAOAUFhx+tvuz8cqjJFfu5IruV6Rqv3/8+ofGdByjinUrasiCIf7g+N4dezW151S33ALoVuvcV8970m2TXI3x1ne3TrYsiZVuSQ3bf1oVKFpAfWf2TXJ5aJ5QF5S3LHqrMV77ktrxMt1tINTkWFDcMs7/WPOHajSsoWJnFfMvs4FJn/jsCVfy5cTRE6p9aW13vNSY8uuUUz7HG5Fv+N/HfQ0AAJAUUr0BAAAAIIEGrRu4Kb1ZNvbEVRNPKaVSonwJN2Co1Uu3GuA+dZvXdQHoEhVKJLvf1/a9pqyg2gXVTnvb0lVKuykxlpVupV4AAP9v7y7Ao7q2No6vCBEkwSVIcNdigUIpTotD8bZIQ7ECxUopdnGKuxSKFi1FC8WKa3FKseJaCJaEuMz37M03Q4ZkIECSifx/zzM3M2fOObNn37nnMm9W1hYAVkCIDgAAAABxyFIvcuPCmRGp3uVZ8mYRe0f7SNuD/ILMeneHh4VLgG+Artw29iIHAADA+yNEBwAAAIBXnNx6UtaPXy/JnJK9cd/Q4FC9MGiVNlWiff7LRy/LpNaTZM7VOfrx2h/Xyu/TfteLb74alquFL0fuHqmr2I0uHbkk09pOkwHrB0j2wtn1tsd3HkvP4j112xWPJh5v8W4BAADwOoToAAAAABCF7gu6Swb3DG/c79mDZ3Jm55m3OreDs4M4JHcwPW7Sv4m+RdeRtUf0OTLkyCChIaFmPcKzFsyqf6pFOIP9g/XrGBfpBAAAwNsjRAcAAACAOHL/6n2Z/fVsCQoIkmf/PZMh1Yfo7WqR0egsYqo8f/pc9q/cL437NZbVI1fLnqV7dAW7aueiDKszzFTFrhbiHHdknGTKnem9x356x2lZNnCZPLn/RDwaeUjb8W3FwenlLwJe5+CvB2XLjC1y/8p9ccvvJi2GtJBiVYvp5wKfB8rKYSt19X+gb6CUrFVS2oxsY+oNrxYrXTVilVw9cVVSpUsldTrXkVpf14rUsmb3kt2yZvQaGX90vG5pAwAAEFMI0QEAAAAgjoSFhMm9f+9J5VaVdfuVMnXLyJoxa8QumZ0Okvf+slcHxUb3/70vMy7OENcMLxcb3Tp7qwT4BEiFphX0gqRfjv1Sb79w4IKMrD9Sfrr+U6S2MO/r1j+3dPuZxt81loqfVZTlg5fLquGr5IvRX7zx2FPbTsncrnOl69yuUqhyITmw6oD82PRHGfT7IClYsaDM6TpHvG56yXe/fqfnYWHvhTKy3kgZe2is+Hj5yKiGo6SmZ03pMqeL3Dh7Q2Z8NUP3fm/Ut5HpNdTxv/zwi3T9qSsBOgAAiHH8TR8AAAAAxBEVbquQV/VPVz8b9G6gg2NVVZ3MMZl80uUTXTluvKVMm1JvN3p0+5Fsnr7ZbIHSae2mSec8nWVh34WStUBW+f7D76VP6T7yedrPY2zc2+Zs0z3ZVfV7plyZpN34drJnyR4JDgx+47G7Fu2SSs0r6T7t6pcBdb+pKzmL55SjG46K7xNfObbxmHwx5gvJVjCbZMmTRf9S4O6lu3L7n9tyeO1hcUnnoivXU2dKrRdfrfplVTmy7ojp/Krifm63ubqCvfSnpWPsPQMAACTJSvT+/fvL+fPnZdOmTdYeCgAAAIAkyMbWvAVJRHb2drJ9/nbZuXDny/1tbHTPc2OP85+++UnSZUsn9y7fM+2jFj+t4VlDPhvwmWmbruz2+C7K15nTZY74eftJn+V9oj3u62euywd1PjA9TpMljQ7475y/I7k/yP3aY729vCVXqVzm7zWZnf6Fgu9jXx2Cq97uEZ9T1POqEj199vRmrVv0sRF6vG+bu00uHb4kbce1lUNrDknRqkV18A4AABBTkkyIfvbsWZk1a5acOfN2C/4AAAAAQEyxEcshumpPErFFyav8vf31IqaeUz1l+CfDX57Txkb+mPmHrgw30v3RLbxU+4ntdXD9NlT7mFf7qqdInUL3R88trw/Rc5fKLcd/Py4Nvm2g+75fO3VN9zlvPqi5Ds9V+xpVWV63e129/86fd+rt2Qpn08dun7ddvG556W1qDg6vOSyVWlbS+z7976msHrFaj+3Zw2fy77F/ZVHfRdJrWS8p9GGht3qPAAAACS5Ev3fvnjg4OEj69Onf+1yqYuPrr7+WXr16Se7cr/8HHgAAAADEFoMYLPYNn9Rmkm53EhocKn7P/PTCmqoKXd1f+mippEyTUkbuGSkhgSGRjv+k2yeRK9ErRF2J7pjc8a3HbWtva9ZWxnieQL/ANx7bdEBTmfrlVOlfob+4FXCTc3vOiXtxdylSpYh+XvUxn915tpzddVYvMvrvX/9KuwntdLV5mfplpPL+ynoB1vzl8+vFRf19/KVWx1r6WLWoqvqFweAtg0194xf3XyyLv1ssYw+Ofev3CQAAEK96ovv5+cknn3wi//zzT5TPDxgwQNzc3GT48JcVFu9qzpw58vfff0vOnDll48aNEhz85r59AAAAABDTDOEGi61cVE/wGRdmSJ+VfSRjroz6/vdrvxf7ZC9rnxycHKI8Xi022qNYD9NtWJ1hMTpuVS3u/dDbbJsKs5M5JHvzsWlT6UVEv1/3vZSsUVKC/IJ0D3SjEjVKyMyLM/Uipcldkuu+7tXbV9fPqSC9/YT28uORH+XTbz7VPdQb9Gqgf8Fg7BGft0xes4VX1eOI7W4AAAASbCV68uTJxdnZWXr37i3btm2L9Py8efOkUaNG8vnnn8uQIUPe+XWeP38uQ4cO1RXoN2/elKVLl8rIkSNl7969+vUjCgoK0jcjHx8fUyW7uiUV6r2qP+80vWdD3L73xDrXkeYV7405jR3Ma+xgXmMH8xo7mFfrzivznrjpNisR7F+5X29TvdLvXLyjA/DQoFDdK1zfDw612JYlojpd6kS7Ev1d5CubT/cdr92ptn6sKsZVUJ0ue7pon0O1Yzmw+oBuxVLAo0CkXyKoandVpa5+caAeR6R6nKtfFKR1S2tq+6LPmT2D3Pz7ptm+Xje8dM92AACABB+iq759CxculKJFi8qOHTukZs2aZs+rVi6NGzd+76rxtWvX6qr33bt369YwoaGhUqxYMR2mqxYvEY0ZM0aGDYtcseHl5SWBgW/+M8XEQn1x8/b21l/yVOWHja9fnL7+w4cvf5GRmOcV7485jR3Ma+xgXmMH8xo7mFfrzquvr2+cjgtxSy2WmSVvFtNjn0c+utJaVairSvQxB8bovt5q8c+JxyfKjbM3olVVrgLmfcv3mR6Hh1r+ZUyQf5D+HDqlcIr2uD9s9qEMqTFELhy8oHuN/zb2N3FJ76J7livBgcESFhImzqnMC5Ui+mvjX3L7/G35dsm3UT6/fPByKdewnKnNS0SP7zzWfd+/XfqtWVuZCp9VkHXj18nm6ZulQtMKOlDfMmuLfNr102i/NwAAgHjdE93V1VXGjRsngwcPjhSiG0Vchf1d3LlzRzw8PEy91e3t7aV48eJy5cqVKFvIqMr4iJXo2bNnlwwZMoiLi0uS+oKn5l29b/UFz/Dc/M82Y1vG///TzMQ+r3h/zGnsYF5jB/MaO5jX2MG8WndenZyiH2wi4XHL5yYD1g3Qvb1V0F33mxdV1Wd2nrFcif5KB5iw0DD9MyQoxHTfUiW6/qva0HC9oKfRwj4Lxc/bT/os7xPtcecskVOaft9URjcYLSnTptS90FUYbvwsb5y8UU5sPqF/CRAVFbIvG7hMWgxuYWrFEpF6/yqgn3BsQpTHLxu0TErWKqlbv0SUKVcmGbB+gKweuVrWjFmj291UaV1FGvRpEO33BgAAEC9D9JkzZ+pKczs7OwkLC9N90VXv82zZspntd+3aNd325X2ocwYEBJhtU21dKlasGGlfR0dHfXuV+odhUvsCqb7gmd63Tdy+98Q812bzihjBnMYO5jV2MK+xg3mNHcyr9eaVOY8fVDuRGZ4zzMJnS1SQXdMz6qIgS0KCQ3SwbKQWEDVWokekKtEHVxtsfmzQi4VFVWitFtHUrU9sRFdjv6qDWwf5oM4H0mNRD9O2zrM7y7to2Kfhi2rvczclb+m8Zi1TVIAfMcR/lQq3p/491eLzKhz/+fbPFp+POP5XFaxYUIZsefcWoAAAAPEyRF+3bp0Oq1WQrtSoUUNOnTqlbxGpkH3GjBnv9Vp169aV7t2768VF69Wrp9u7nDlzRn799df3Oi8AAACAxEuFuq9WPcekghUKyk/XfzI9Lla1mF4Q81U5i+eUpY+Wmm1LlzWdLPderu9X7/BiAc64kjFnRn0DAABISqwSou/cudN0X/U8Hzt2rPTt29dUdf7777/L3bt3pVOnTu/9WunSpZMtW7bo86tWLVmyZJHVq1frNi0AAAAAEB+oSm11AwAAQPxj9b8VVQvaqMU8Q0Je/Emi4ubmpgPve/fuxchrfPjhh3L48GHx9/eXq1evSv369WPkvAAAAAAAAACAxM3qIbpq66KC9Ii9yD/44ANp2rSpjBo1yqpjAwAAAAAAAAAkbVYJ0SNWnRsXWFL9zyMaOnSoLF68WFeOAwAAAAAAAACQZEJ0VXWuQnPjTVWiOzk5mW3Lnz+/br8yYMAAawwRAAAAAAAAAADrLCx68+ZNHZrb2r4+wz927JiUK1cuzsYFAAAAAAAAAIDVQ/Ts2bNHa786derE+lgAAAAAAAAAAIi3C4sqO3fulH379ll7GAAAAAAAAAAAWL8SPSLV99zT01NKlCghH330kbWHAwAAAAAAAABA/KhEDw8Pl7Zt24q9vb306NFDHj58aM3hAAAAAAAAAAAQPyrRVQW6CtCPHDmi27kUKlRILzSaLFkycXNzk2zZsknhwoXlhx9+iHYPdQAAAAAAAAAAEnwl+vr166V48eLi5eWlQ/S8efPq7b6+vnLhwgX55Zdf5JtvvpF79+5Jt27drDFEAAAAAAAAAADivhL90qVL0r9/fxk1apS0aNFCbwsLCxMbGxtxdHSUnDlz6pvi7u4u9erVi+shAgAAAAAAAABgnRC9QIECcvHiRR2av+rVbfny5dP7AgAAAAAAAACQZHqiq7YtLi4uZtsMBoM0btxYt3mpU6eOVKxYUdKkSWON4QEAAAAAAAAAYJ2e6OHh4VKsWDFp06aN3Lhxw+y5qlWryt27d6VBgwZSpUoVefDgQVwPDwAAAAAAAAAA64Xotra2sm3bNnFwcJCiRYvKoEGDxM/PTz/XpUsX+fnnn3W4rvqhV69eXR4/fhzXQwQAAACAKAUHBEdrn9CQ0GidT/1FbnBg9PcHAABAEmnnUrBgQVm4cKF07dpVOnToIH/88YdeaNTOzk4/nzJlSlm8eLE0adJE77Nq1SprDBMAAAAATJ49eCYDKg2Q7gu6S+HKhS3uN775eLl76a7YO774uhXsHyz+Pv6SOnNqEYPIo9uPJH2O9C92NogO0VsMbiFV21bVm26cuSE7ft4hHad11I+X/rBUbG1spc2oNqbH6rgvxnyhH6sA3j6ZvWmMYSFhki5bukjjenr/qXQr2E0WPVgkDk4OMT09AAAAiZZVQnSjsmXLyvHjx+WLL76QOXPmSNu2bcXNzc20yOisWbOkdOnSuho9XbrI/wgEAAAAgLiSOlNqqfplVZnyxRQZtW+UZMiRIcr9Bm4aaPb46PqjsnHyRhm1d5QEPg+UDlk7yORTk8XO/kUR0aucUjrJoTWHpMhHRaTiZxXF0dlRbw8PC9eBuVMKJwkJDJGTW0/KhokbxL2Yu3SY1EHvs2vRLtkwaYPU/aauNOzTUByTvzhWsUv24vWSOSaLsTkBAABICqwaoiuOjo6ycuVKmTBhgmTOnNnsuSxZssiZM2cI0AEAAADEuSD/IAkLDXtR5W3zYpsKph/deSSPbj0S14yupn3DQ8P1Pirgfl+Z82SW1sNb6yBdhejGcPzIuiPilt9NcpbIKfuW75OHNx+KRxMPKVqlqOnYJv2bSO4PcsvC3gulfKPysmnKJjn822Gxsf3/NyAiX6T7whTK1/+2vrQa1uq9xwwAAJCYWT1EN/ZJ/+6776J8LkOGqKs7AAAAACA2rRm1RjbP2KwruNV3FmMP89DgUDmy9oj+61njNhW2l61XVr5d+q3e9nOvn2XvL3vFwdlBt1cJCQoRzxyepnN3yt1J/wzwDZDuP3fXYXhENb6qIdXaVXv52LOGfDbgsxfjGrNGP9d8cHNdma5axRipcZSsWVImnZqkK91VJXrj/o31sT6PfaRz7s7yy5Nf9H6e2T0lf/n8sTqHAAAAiUGch+hhYWGyZs0aadGiRVy/NAAAAABEW6vhraT1yNamsFzxuuklPYv3lInHJ0oGd/OCH1XZbaR6jldpU0W+mvJVlO1c5l6bq0Pu7zy+k2ROL9urLOyz0NR2RVWjK94PvSU8PFw2T9+se52r8fg88tHP/bP3H5nx1QyZ/e9suXf5nkxtO1U8p3lKoQ8L6edt7V+E/696/uS5FK9WXPKWzRujcwYAAJAYWaUSvW/fvoToAAAAAOI1Wzvbd95fLRZ68NeDcubPM6aFRXsU66EXBFV6leplWuxT9Tc3ylMmj4QGhcr8nvPl81Gf622BfoFy6fAl3ZO9ZO2SksI1hSzovUCfz0ZspG73umLvYK8XLs3vkV9G1R+lW7So7cbXe5VqRdNr2YsxvKq1a2v9nKqsBwAAgBVCdDs7O7G3f/myRYsWlQcPHkiyZJEXt1HVFoGBgfLs2bM4HiUAAAAAiDx/+lxXfhsXAQ0KCDL9VFXlimqNYgg3SMq0KU3HfTX5K30z2j5vuzy88dAUjFvyUauPdNsVFaIbQ/mO0zvqvuwqkF/Qa4FeuHTy6cmRjnVJ7yKdZnaSEtVLiEsGF70tJDhE1o5dq28RQ3IlX7l8MmzHsEjnmXdznjimeLkgKQAAQFJnlUr0iH8O6efnJ8ePHzf1EixdurScOnVKB+glSpSQs2fPWmOIAAAAACBzu86Vc3vOmVqsqLBcGVpjqGmxThWiuxd1l/9t/5/+HqPauujFSP+f6qG+edpmXSUeFVWlrtqrZMyZMcrnb/59U3Yv3i13LtyRZw+eyU/dfjI955TSSdqNb2e2v+qvfvv87Rfn9vYXz6mesnPBTmk5tKUUr178xfhrDpXSn5SO8vVSpE7xVnMEAACQ2FklRFdhecRA3d3d3axSPUeOHKb7EZ8DAAAAgLjUZ0Ufs8fGnuhjD46N1BNdOb/vvPz42Y+6J7oxZFeLh6pK9lPbTunFRVUQr7Y5uzjr70MqZFfB9cyLM6Mcw+M7j+XhzYfy3W/fmW2/9fctmdNlTqQQ/e7luzKwykD58fCP8uD6A91HvUKTCrJ32V4dol8/fV2unbomvX6Jup0LAAAA4lklOgAAAAAkFkU/LipLHy01PT6w6oAsG7RMt2DJVjCb3rao3yIdbvdf0/+151IV6n8u+FOH9VdPXJVBHw8ye16F76oXekSqCl5Vz1duWVlXxavFRnN/kFtyFM0h6yeulxtnb8ji/oulWrtqusc6AAAA4mmIfvfuXWnXrp00atRIQkJeLqLzapU6AAAAACRUKijfMHGDBAcEy9IBS6XIR0V0yH1iywkZuWfkG48fXG2wODo7SoNeDSR/+fwycONAs+evnbwmk78w742+YugK8fHykS/WfyGrR66WolWKSqq0qfRzn3b7VEbUHSHOKZ0jVdhH5PfMT/dEj9iSBgAAICl7u+XmY0jq1KmlSJEiMnv2bL2oKAAAAADEJyr4fnDtgfg88tGhsvGm2rAo6mfE7WoBUrWvOs4oU65MMv6v8TL176lStl5Z2TBpg6yfsF5XkK8esVr+2viX6XwRnf3zxbpQuUvllv/t+N9rxxkWEma6r6rWt8zYIl/P+Foe3XmkHzfo3UA/p15TjVEthqr6qN+9dNfiOTu6d9StZwAAAPCCVUoLkidPLv369dO3XLlyWWMIAAAAAGDRvX/vych6UVeLJ3dNLsM/HW62TfU5V0H1V1O+0j3IVe/04MBgeXjjoVw6fEme3Hsi9XrU09Xgj24/kmObjsm6cetkpudMKVatmDQf1Fy3XDFq1LeRNBvUTLfCDAkOkSvHr8h3HuY90UMCQ/RrGhWoWEAa9mkohSsXlpkdZ0rZ+mUlX9l8sm/FPv1aauHSSScmycFfD8rYxmMlS94sUqp2Kandqba4ZnQ1nWe59/IYnEkAAICEz+o90V/tj06/dAAAAADWlrN4Tpl/a/47HXvx8EXZMX+HZMqdSXIUziFtRraR/B75xdb2xR8CZy2QVd9UUH7lxBX5e9ff4lbAzXR8peaVzM6n+pur25uonuvNBzfX97vN6ybPHjyT0Y1Gi2NyR+kwqYMUq1pMP9ekfxOp1bGW7F66Wy9aGjFABwAAQGRWb3KnFruxs7Mz64lufGy8Hxb28k8UAQAAACA+K1ihoIzaOypa++YtnVffYoNaOHTIliFRPpcybUqp37N+rLwuAABAYhPnIXpwcLDZYqJ///232Nvbm6oyIlLhudofAAAAAJIyby9vOb39tHg09tCV5QAAAEjEIbqqLm/e/MWfGCqpUr1YKf51/dMBAAAAIC6d3HpS1o9fL8mckr1xX9WXvFq7alKlTZUYee0j645I+UblzVpdnvzjpKwavkrKNywfI68BAACAeByiOzo6ysSJE+P6ZQEAAADgrXRf0F0yuGd4436q9/iZnWfe6tyBfoFy+/xtSZE6hbjlczM714LeC/RzzQY2M20/tOaQ1OlcR5xSOklCZek9R0UtyKoWQgUAAJCk3hP90qVLUqBAgdfu07NnTxk+fLi4urLYDQAAAICE79S2UzKz40zJ6J5RHtx4IPnL55c+K/qIfTJ73ce856KeMrbJWMn9QW4p/UlpuffvPTm/77yc339e1oxeY3YuG1sbmXp2qqR1S/ve4zr751lZN36d3Dp3S9JlS6cXPq34WcVoHfvf1f9kxZAVelFVB2cHXZWvjrd3sH/je17y/RLZt3yfeE7xFI8mHuL7xFcO/3ZYGvZp+N7vCQAAICZEbkQehwoVKiTZsmWTx48fy4gRI+TcuXNmz1+5ckXmzZtn1kMdAAAAABIqv2d+MuvrWdJraS8ZvX+0TDoxSa4evypH1x817VOkShFp2LehLOq7SEJDQmXt2LVSuVVlWfZ0mfzy5BfTrcn3TSRXyVwxEqDfOHtDxrcYr0PsSacmSYNeDWROlzly8NeDbzw2OCBYRjccLc4uzjLmwBhdwa9C8YV9F77xPavq9L2/7JVOMzvJlplb9P57lu6Rj7/4+L3fEwAAQKKoRHdxcZGVK1dK6tSpZeTIkTJ9+nTJly+frjyvXr26DtBbtGgh6dOnt+YwgUh+ver99gcZwsXG108Mz71VyVC0D2uWh7/CAAAASCz8ffylzcg2OihXXDO66qrv50+fm+3XqE8jqdS8klw6fEn3Zx93ZJwOo+f1mCeN+zUW92LucnzTcd07PSbsXbZXClYoKLW/rq0fV2pRSY5vPi5H1h6RD5t9+NpjVSsbVT3eYVIHXYWuQv16PevpHu4dp3V87Xv2e+onyV2TS46iOcT3sa8EBwaLv7e/3gcAACC+sGoluq2trVSqVEns7Ox0oH779m3x9PSUzz//XIfnCxYskP/973/WHCIAAAAAxJgMOTKYVVmrXucPrj+QMnXLmO2n2qBkzpNZV2qrADp99vRiCDfIXxv+0i1c1GKmecrkEY/GHpFeQ7V9ae3aWofu0eXj5aPHZjaGZPb6O1t0jnXN4KoDdCM7ezvTsa97z86pnCXAN0C8vbx1JfvBVQd1gA8AACBJvRJd/WNKBefh4eHi4OAgBoNB0qRJoxcdbd++vTx48EB++OEHqVmzpri7u1tjiAAAAAAQa1S19XcVvpMnd59I13ldJV3WdGbPB/kH6QVGvxj9haRMm1Jvi1itrkL2ryZ/FeW5C1QoIPNuztMV3tGVu1Ru+WP2Hzp4Vwt/Prr9SE7vOC2fj/r8jcfmKpVLvG56ybWT13QfdxXwqxYtpeqUitZ7LlK5iIz4ZIS0GdVG7l2+J1XbVo32uAEAABJtiB4UFCT29vaSNm1aefr0qd6mWrpMmDBBFi5cqAP13bt3S9++fWXt2rXSpEkTawwTAAAAAGKFCriHbBkiuxbtkp+//VlyFssp2QplMz2/be42+WffP2KXzM607dqpa/rnhQMXJEeRHBbPrarAVRD+Nmp61pR/j/0rAyoNkFwlcsmFgxd0lXjFZhWjFcA3G9hMxjYdK4UqFpJb52/JwxsPxXOaZ7Tec69lvXR4f/HQRV19P67ZOHly74n0XNJTsuTJ8lbvAwAAING0c0mWLJnY2Njom1FAQIB4eXnJokWL5MCBA1KlShWZO3euDBw4UFeqAwAAAEBikjFnRmn5v5aSt3Re3ZPcSAXKv0/7Xb4c86UOso1UdXfpT0vL1tlbJSQoJEbHolqxfLvkWxm2Y5h81OYjXfWuxubg9LJFy+s06tdIppyZIo37N5bA54G6fUtUQb+l96xCf9WGRlE91FX/9D1L9sTgOwQAAEigPdF9fX2lYsWKUqFCBf345s2b8uuvv8rWrVslNDRUPvjgAylQoIBs2fJilXYAAAAASMhUixRVhR2RvaO92Nq9/Gq29IelkqtkLinXsJxp24HVB+TWP7fkm5+/kcx5M8vcbnN1e8yYliZLGjm6/qgU/LDgGxcUfVVyl+Ry/fR1CQsJkxZDW7zVe75y4orkKZ1HLy6qgvbshbLr+wAAAJLUQ/Rt27bJpEmTZOrUqbp9i+qHnjt3bhk3bpzkyJFDZsyYIZ07d5YiRV6s4g4AAAAACZkKhw/+elDWjlsrj+8+1uG4qsA2LhCqqrMPrj4obce1NR2zb/k+Wdh7ofRY2EMckzvK19O/1q1PJracKM+fvOyTbhQWGqar2d/lL3pVX/Mj645I+wntzbarwF6dMzzMcnCvKtDXjF4jLYa0EJd0LtF+z8rRtUelfKPyuiJd9UxX7WBSpnnRCx4AACDJhuiHDh3SVeceHh6SN29eKVeunOTJk0dOnz6t+6D//vvvMn/+fDl//rzkzJnTWsMEAAAAgBiTLls66beqn5zcclL6lukrW6Zvkd7LeuvKc0W1Uan7TV1xy+cm96/clylfTJGlA5ZKj0U9pFClQqZq8cGbB8uD6w+kb7m+un96xHD70uFL0tG9o17I822o0H3Rd4ukTuc6kr1wdrPnHt9+rM+pquEtWTdunV4stGq7qm/1nn0e++jqc9XLvXDlwnph0gOrDshHrT96q/EDAAAkqoVFVauWjh07SsuWLfXjfv366SD9yy+/1C1eihYtKj/99JPuja4WIQUAAACAuKQC3RmeM8Te4c1fmVTlt1qYM7pUGD5yz8gon1MButGmKZskwDdARu0dpUPmiDLlyqTPsXzwcrn37z2z1igqiF7uvVzellqzavjO4VE+l8E9wxvP2Wp4q3d6z6pqvWbHF/PnlMJJ/rf9f281bgAAgEQZol++fFnSp08vP/zwg1y6dEl27twp06ZNkxQpUsiyZctkw4YN8sUXX0idOnVkypQp1hgiAAAAgCSsRI0S+mZNntM8xdbW8h8Pq8C5w6QOcTomAACApMgq7VwKFy4se/fuFTs7O0mZMqXMnTtXB+hGDRs2lL/++kvCwsLEwSF6q8EDAAAAQGLyugAdAAAAccfq/yrLmjWrrjh/leqPvnLlSnF1dbXKuAAAAAAgvggNCZXt87brnukAAABIAu1cLKlataps2bJFAgICpEmTJrJp0yZJlSqVtYcFAAAAIIk5ufWkrB+/XpI5JXvjvmohzGrtqkmVNlVi5LWPbz4uxasXFwenl3+Ve/HgRVk2cJmU/qS0pEyTMkZeBwAAAPE4RFervnt4eMjRo0fNtl+/fl2cnZ1l9uzZ4ujoqAP0jz76SPbt22eNYQIAAABIwrov6K4X1HyTZw+eyZmdZ2LkNYMDgnVY/tfGv6Tr3K6m7YfWHJJKLStJumzpJKEK9AuU2+dvS4rUKcQtn9tr931442GkxVQBAACSVIiuVn1Xt1e5uLjIxYsXZfLkybJ//3697cKFC1YYIQAAAADEjl8G/iJbZmwx29ZrWS8pW6+sODg7SJ+VfWRwtcGyvcx2qdWxlm7hcvi3wxIcGCx7f9kb6XxDtg6R/OXyx+gYLxy8ICPrjZRlT5dFa///rv4nK4askIuHL+r3oKryG/VtJPYOL75yntp2SmZ2nCkZ3TPKgxsPJH/5/NJnRR+xT2YvS75fIvuW7xPPKZ7i0cRDfJ/46vfbsE/DGH1PAAAACSpEb9Omja4679DBfCX5u3fvSr169WTOnDkydOhQWbx4sdmCowAAAACQ0F0+elm+nvG1lK1f1rTNMYWj6X62gtmk3fh2sqjfIqnQtIJsnr5Zcn+QWwZvHmx2nv0r98uKoSskX9l8MTq+AN8AmdN5jhjCDdGunh/dcLQUrlxYxhwYI49uP5IZX82Qp/89lY7TOorfMz+Z9fUs6bW0lxSpUkS8H3pLv3L95Oj6o1L609L6FwOdZ3eWTVM26RB9z9I98vEXH8foewIAAEhwC4uWLVtWt2opX768bNy4Ufz9/SV58uTi5OQkv/32m3zyySfy559/6n2jqlgHAAAAgIQoJChEbp69KcWqFdNtTYw3VZEdkarkHrFrhPh4+cj2n7ZL6+Gt9fZp7afJP3v/0fePbzouH9T5IMa/My39fqk4pXSK9v6qlY2qHu8wqYOkdUurq8zr9awnR9Yd0c/7+/hLm5FtdICuuGZ01W1pVIW931M/Se6aXHIUzSG+j311tb2/t7/eBwAAIEmH6N9++61kzJhROnXqpMP0ihUr6uA8Xbp0ki1bNrl06ZLY2trKrVu3dP90AAAAAEgMrp68qn+ObTJW2mZsK/3K99P9zqOiKtJV0NyoTyPJUzqP3nZmxxkJ9A/U97PkyyJVPo+8mKnXTS9p7dpabpy98dbjO/HHCTm+5bh8NeWraB+jgn7XDK66jYuRnb2d/k6nZMiRwayyXL3fB9cfSJm6ZcQ5lbOufPf28hZnF2c5uOqgVGpR6a3HDQAAkOhC9NfZvn27VKlSRe7fv68r1Z8/f27tIQEAAABAjLhz/o5kLZBVV21POTtFV5yrXuG3/rlltp8qJvqp20+6srtB7wZ6W2hwqF6c06jl/1pG2colXfZ0Mu/mPMlRJMdbjc3nsY/M7zFfPKd6SprMaaJ9XK5SuXRwf+3kNdM4VYuWUnVKme2nKsy/KfyNbvXSYXIHSZc1na5CL1K5iIz4ZIR81PojuXf5np4fAACA+CRehejqH4qtWrWShw8fSvbs2XWQrirVAQAAACAxqPFVDRm9f7QU+rCQDqrr9agnBTwKyKFfzavRj6w9IkfWHzFVcyvXTl/TfcovHLjw2tdQx6gWMbZ2b/d1b8G3C6RUrVJSrkG5tzoud6nc0mxgMxnbdKxMbjNZV9erinv13iJSgfmQLUOkQa8G8vO3P8udC3dMi6rOuTpHV6wX+biIjGs2Tr7/8Hu5f/X+W40DAAAgUYXoTZs2lStXrkjr1q3Fy8tLlixZIuPGjdOLjTZo0EC8vb0lODjYGkMDAAAAgDiVOnNqeXjzoelxaEio/Db2N2n6fVPdO9xIV3fXLiX7V+wXn0c+MToGtUjpzb9vypc/fvlOxzfq10imnJkijfs3lsDngbp9S1SV8BlzZtQV9HlL55W9y/aatqvQ//z+86Ye6qp/+p4le97jHQEAACTwEL169eri6uoqtWvXlpkzZ0r37t2lQ4cOkiFDBilatKg0a9ZMBg4cqPelJzoAAACAxEK1aDmw+oDpcVhomFw9flXSZ0tv2rZh4gaxsbWROl3qmLap6nPVS1z1Ki/fqLxMazdNL8IZU1RA/+TeE91uxTOHp64EV9T9g78ejNY5krskl+unr0tYSJi0GNrCtP30jtO68jwie0d7s0r5Kyeu6L7vanFRFbRnL5Rd3wcAAIgPzJeAjyNdu3aVn376Sdq2bWu2XVWjjx49Wj799FPTCvM+PjFbYQEAAAAA1qL6h6/830pJlTaVpEiTQrbM2KKryqu2q6qfP7fnnKwfv16+X/e9XpxTObPzjEzvMF06Tuuoe6S3Ht5ahtUZJiM+HSE9F/eU9NlfBvBKeHi4BPgE6EU7o9vS5Zufv5GQwBDT48f3HsvwOsNlzP4xkipdqmidU1Wgrxm9RloMaSEu6VxM21UgrkL/NG5pdA/4Cwcv6Krz5oOam/Y5uvaotBzWUr/XJ3efyMMbDyVlmpRvObsAAACJqBJdVZc3atQo0nbVxiUsLEx+/PFHGTZsmAQGBsqIESOsMUQAAAAAiHE1PWvqIHnW17NkVL1R4vfMT4btGCZZ8mTRz6tAvWKzilLkoyK6MlxVcE/+fLJ8OfZLqfhZRb2PU0onGbBhgA7Z+1foL2t/XCtB/kGm13h8+7F0dO8YabHS10mdKbVkcM9guqVze9FGRt1Xrxedc64bt04vFmr8hYCRaknTb1U/ObnlpPQt01e2TN8ivZf1llwlc714z499dPW5ej+FKxfWC5MeWHVALzQKAACQZCvRVZX5//73v0jbVRW6CtiLFSsmo0aNEjs7O+nWrZs1hggAAAAgCVOB7gzPGWLv8OavTKoliwrHo0stwqluUVFBuTEsVz3DVXsUFbK7F3M3209Veg/eMlgH17fO3TIbpwq+l3svl/fx6jmic85Ww1tZfK5QpUIycs/IKJ9T76Vmxxfz55TCSf63PfJ3RQAAgCQXohupwPzIkSNSoUIF/fjLL18uYqN6pAMAAACANZSoUULfrKlxv8bSsE9DsbW1tRj0f/bDZ3E+LgAAgKTGKu1cjFTrlqZNm1pzCAAAAAAQb1kK0AEAAJDIK9HHjBkjjo6O+r7qez5p0iSL/2BUrV2qV68exyMEAAAAAAAAAMBKIbpq4ZI6dWodkoeEhMjff/8d5X7quR9++EE/nydPnjgfJwAAAAAAAAAgabNKiL5hwwbT/T179sjChQst7luxYkUduhOiAwAAAAAAAADimtUb7NnY2JjuX7x4Udq0aSMrVqwwbVMBu9oGAAAAAAAAAECSCtENBoMEBwfr+/3795cPP/xQsmfPLh4eHqZ9ChQoEKOvWadOHVm0aFGMnhMAAAAAAAAAkDhZNUQPDQ2V4sWL6/vNmjWTK1euyNixYyVXrlyyb98+2bx5c4y+3rJly2Tbtm0xek4AAAAAAAAAQOJltRD96tWr4ubmpivRBw0aJD4+PpIqVSoJCgqS7t2761D92bNnMfZ6T548kT59+sR4ZTsAAAAAAAAAIPGyysKiRuXKlZN+/frJsWPHZNiwYXL58mVJkSKFlCxZUi5cuCBp06aNsddSAXrjxo0lICAgxs4JAAAAAAAAAEjcrBqiq8D8448/1jcVqDdv3lxKlCghR48elcOHD0vdunVj5HV2794tf/75p/zzzz+6yt0SVQWvbkaqOl4JDw/Xt6RCvVfVr970ng1x+94TxFy/y5wYDC9vEp645iO+fFYRI5jX2MG8xg7mNXYwr9adV+YdAAAAiF+sGqKrLxHKb7/9Jj169NA/K1WqpEP01q1by9mzZ2XAgAHv9RqBgYHSqVMnmT17tm4X8zpjxozRFfGv8vLy0udJKtQXN29vb/3fj62trdj4+sXp6z98+PIXGfHVu82JQWwCfEVs9BkS1XzEl88qYgbzGjuY19jBvMYO5tW68+rr6xun4wIAAAAQT0P0kJAQU4iuKsW3bNmiq9CV8uXLy4EDB3SgrrxPkD5ixAgpW7ZstKra1ev07t3brBI9e/bskiFDBnFxcZGk9AXPxsZGv2/1Bc/w3DtOXz9jRleJ795pTnQVuoghZVoRG5tENR/x5bOKmMG8xg7mNXYwr7GDebXuvDo5OcXpuAAAAADE0xC9YMGCsmbNGn1/xowZkZ7PkiWLbN++3dRS5V0tX75cV5KnTp1aP/b395fVq1fLX3/9JbNmzTLb19HRUd9epb7kJLUvkOoLnul928Tte08Qc/1OcxL+IjzXN9vENR/x5bOKGMO8xg7mNXYwr7GDebXevDLnAAAAQPxi1XYub5InT573Psf+/fslNDTU9Lhv377i4eEh7dq1e+9zAwAAAAAAAAASN6uE6Kq9ioODg76pahxLf+6qWr4EBwfLsWPH3vm1smXLZvY4ZcqUkj59en0DAAAAAAAAACDehegXL140tXJRfdFVVfjixYv1/ZYtW+p2KypEb9asmV5sNCYtWrQoRs8HAAAAAPHFo9uPJEXqFOKcyll/v7JUtKSEhYaJIdwg9g7x+g+UAQAArM4q/1pKliyZ1K5d2/TY2dnZ9Fg9V6tWrUj3AQAAACCunNx6UtaPXy/JnJK9cd/Q4FCp1q6aVGlTJVrn3jBxg7gVcJOy9cpKoF+gBAcGmz3vlNxJHJwd9P1ze87JskHLZMyBMfqx7xNfOfnHSYuvtXHyRnn+9LlUb1ddfhn4i/yw8QdJlTaVaZwzO86Uuj3qSt7SeWXH/B2yatgqsUtmZ3aO4IBgHbB3nN5RPv78Y9P2IP8gOX/gvJSqVcrie/ux6Y+SLls68ZzqGa25AAAASAjiXcnB6yolAAAAACCudF/QXTK4Z3jjfs8ePJMzO89E+7wuGVxkpudMqdejng7R9/6yV1KmSamf83nkIw16NZD6veqb9vd97Gu6//T+U1k2cJkOtGt1jFxwlMwxmTg4OUihSoUkdabUMuOrGTJg3QD9nKo4T5UulUxrN01G7xstdTrX0Td/b39ZPXK1NOzTULxuecnElhOl7bi2UvGzimbn3rV4l6wculJGHxgtWfNnjfK9Obs46zFYcmjNIT2mTrM6RfuXDgAAANYWL0J0gnMAAAAASUXVL6tK7lK5dWX48c3HpXbn2vLZgM/0c3O6zNGV4RsnbZTb529L9fbVzY7NUSSH9F3VV+b3nK+rxI0V60bGynlbO1vpMreL+Hj5mD3/+ejP5fHdxzqsNwb3jikc9VgGfjRQxEak27xuUrx68UjjVqH9gZUHZGHvhTLo90G6gl4F5hG/z+nWMDYvqt5VNbtjckezcyR3Tf7ip8uLnwAAAAmBVUJ0Pz8/6dChg+mxl5eXfqx69vn6+pqeM+43efJkcXV1tcZQAQAAACBGPX/yXNyLuev7J7ac0D9XDV8ldvZ2ZmG0pYru/OXz6/YuuxfvlkX9FukgXYXmSkhQiISHhetwXoXYQX5BMufaHHFJ56KfV1Xq/Vb10/f/mPWHbJ6+Wd9X38VURb0KvX/65ie9TfVVH//XeNPrqvF1mNRBt4lR1es9ivXQP6Oybc42yVYom4w7Ms5suzE8J0QHAAAJiVVC9LFjx4q9vb08f/5cQkJCZMKECabQPFu2bPqmlClTRoKDg3VvdAAAAABI6O79e08GVxssHad2FI8mHqbtqqpbVW+/juqHfmTtEbFPZi/pc6SXau2r6Zut7YsAXflz4Z9yZN0RGbhxoH6sAnVjwK5eQx1rfKwC9tJ1S0v7Ce314+88vpN249tJ4cqFxeumlwyqOsh03tCQUF1xnqd0Hhm6dajeNufqHHlw/YG45XMzVaOrSnpVba4q3sNCwiK9B2Mlumr7AgAAkFBYJUTv1auX/jl8+HDZu3ev/Pnnn3Lnzh359NNP9a1Tp07WGBYAAAAAxCoVOHea0Ukv8KmCcCMVQjulcNLBtiX+z/xl77K9ukd6ng/ySLGqxV5UntuEi629rYQEhkiaLGl0RbmRMTA3Lvp54cAFyVUyl4zaO0of8zq6Ncv/27NkjyzovUDfV33V516bqxcgHVx1sHw15Sv5sNmHZseqYN/WMfL5jRXohOgAACAhsUqIfvnyZXF0dNSV52FhYXLr1i0ZPXq01KlTR7p27aofK+Hh4RIUFCR58+YVOzvzFeMBAAAAICEq17CcZM6bWfc3P/nHSb1NheFvanGSKXcmGbl7pPw+7Xe5de7Fd6YVQ1fI1tlbTfv0WdFHHt16FOXxaiHTvGXyyqUjl0ztWVRLmIOrD+rHAb4BMr7FeL3dEG7Q7VyMPv7yY307tfWULPl+id6mxqt6tq8du1YqNK1gVhFvCe1cAABAQmSVEL1IkSLi5OSkW7moW65cufR21bZlzpw5uh+fMURX7Vxu3rwpbm5u1hgqAAAAAMQ8g8g/e//RLVwc7B3k2f1n4l7UXR7eeKjboKjFRV/HWGHealgrqfhZRZnWfppMODZBh9/qnPev3JcsebOYHVOqdildXX7l+BX92MbWRqq2rWqxncuwOsNMx6o2MC8OenGcUbV21eSvjX/J03tPJV22dG98204pnXRIT4gOAAASEquE6Co4V8aMGSObNm2Sjh07yubNm2XXrl3Srl07+f777yVjxozWGBoAAAAAxLrfp/8u3g+8JV+5fLri+8bZG1K1XVVJ65ZWB8yBfoF6YdA3UYuPGivA1aKhilrQ8/LRy5FC9FepsD66leiWqNeYdHKS2aKob7Lg3gKLi6YCAADER2/+e7tYpKrMVUV6+/btZc2aNXL+/HldfV6yZEm5ceOGNYcGAAAAALHC28tbjq47Kk0HNJXPfvhMSlQvIU//eyqp0qaSWl/Xkpoda+qAW/U4f507F+/I2T/PRtqeu1RuuXToRcuW11EhvapEn39rvr5lLZBV+q3qJ7P/nS3f/PyNrjqPDhWgq/7o/t7+upLeMbnja/cnQAcAAAmNVUP0b7/9VlasWGF6nDlzZpkyZYocPnxYcubMac2hAQAAAECsWD9hvRSsWFDyl8+vH2+aukmKVSsmW+dslfHNx+tAulG/RvLt0m8tnkP1RB9We5iuYFdUC5c/F/4pp3ecljL1ysjxzcd1n/XN0zfLf1f/Mx0X+DxQHlx7oNu9mLVp+f+FRFWrFnXOSa0nScmaJc1eMzwsXM7tOafD8tvnb0eqrPfM4alD9PINy1sc97FNx6RPmT5y4o8Tbz1vAAAASTJEd3V1lUyZMkXa7u7ubpXxAAAAAEBsuvzXZdk5f6c06N1AP754+KIc+vWQNP2+qXw1+SsdoC8bvMy0f1BAUKRzqAD81j+3pPWI1vL4zmOZ1GaS2NjYyMVDFyVF6hRSokYJcXZx1qH8wV8PyqE1h/RxFw5ckPk950vWgll1v3MVlmfOk1nO7z+v73eb101Xw6vK8r6r+krRKkVl+7zt+tin95/KiLojdCDfbFAzmdBigoxtMlY2z9gsZ3aekYIVCsqAdQP0caEhobrf+5F1R8TnsY/Z2P19/OX+v/clwCcglmcaAAAggfdEBwAAAID4TAXJMzxn6Ors6LRFqelZM1rndU7pLBWaVpAiHxURv2d+Mq3tNKnVqZZuwaJ8PfNrWTNqjQ6i149fL79P+11XrUekeqZ3X9BdyjcqrwNsdb4CFQroIN3oyx+/lEmtJonBYJAOkzrotpnLhyyX2p1rS5Pvmuiw/sSWE7qyXPVEVyF5kH+Qfl1Vca6OU+f74JMPpFbHWnLpyCX9Pvus6KN7r3/8+cey8+ed+hyq2l1Vp6vKd9VL3cg1o6vMvDTTbOxV2lTRNwAAgITExqD+dYQo+fj46Gp5b29vcXFxkaRC/QP74cOHenFXtUjRr1e94/T1m+VxlfjunebEEC42vk/EkCqtiI1topqP+PJZRcxgXmMH8xo7mNfYwbxad16T6r9BraVZy2aSrHAyqf9tfau8/t1Ld3U1eFQLc3rd9JJHtx9Jfo/8b7Vwp9Gpbafkyb0nUr19dVMVuIOzg1kLl7ehWsZE55cKidXEphOlU/NO0rFjR2sPBQAAxLGk+y8gAAAAALAytZinJRncM+jbuypVu1SkCvb3kZQDdAAAkLRRWgQAAAAAAAAAgAWE6AAAAAAAAAAAWECIDgAAAAAAAACABYToAAAAAAAAAABYQIgOAAAAAAAAAIAFhOgAAAAAAAAAAFhAiA4AAAAAAAAAgAWE6AAAAAAAAAAAWECIDgAAAAAAAACABYToAAAAAAAAAABYQIgOAAAAAAAAAIAFhOgAAAAAAAAAAFhAiA4AAAAAAAAAgAWE6AAAAACSLBsbGzGEG6w9DCQA6nNia8tXaAAAkiL+BQAAAAAgyUqbOq34PPKx9jAQzwUHBkuAT4C4urpaeygAAMAKCNEBAAAAJFkVK1SUmyduSpB/kLWHgnjs0uFLYhduJx4eHtYeCgAAsAJCdAAAAABJVo0aNSS5TXJZOWSl+D3zs/ZwEA/duXBHtk7bKuVKlZNs2bJZezgAAMAK7K3xogAAAAAQH7i5ucmsabOka4+uMqnJJHEr6iYp0qYQG1sbaw8NVhYWEiaPbz2WZ7eeSbH8xWTihInWHhIAALASQnQAAAAASVqZMmVk6+9bZdeuXXLs2DHx9vEWg4HFRpM6ezt7KVuqrHzc62PdxsXBwcHaQwIAAFZCiA4AAAAgyUubNq189tln+gYAAABERE90AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAAAgKYfoGzZskNy5c4u9vb2ULFlSLly4YO0hAQAAAAAAAAASgEQfol+9elXat28vY8eOlbt370r+/PnF09PT2sMCAAAAAAAAACQAiT5EV1XnKkBv3ry5ZMqUSbp06SKnTp2y9rAAAAAAAAAAAAmAvSRy9erVM3t86dIlyZcvn9XGAwAAAAAAAABIOBJ9iB5RcHCwTJw4UXr37h3l80FBQfpm5OPjo3+Gh4frW1Kh3qvBYHj5ng1x+94TxFy/y5wYDC9vEp645iO+fFYRI5jX2MG8xg7mNXYwr9adV+YdAAAAiF+SVIg+dOhQSZEihcWe6GPGjJFhw4ZF2u7l5SWBgYGSVKgvbt7e3vpLnq2trdj4+sXp6z98+PIXGfHVu82JQWwCfEVs9BkS1XzEl88qYgbzGjuY19jBvMYO5tW68+rr6xun4wIAAADwekkmRN+1a5fMnDlTjhw5IsmSJYtynwEDBphVqatK9OzZs0uGDBnExcVFktIXPBsbG/2+1Rc8w3PvOH39jBldJb57pznRVegihpRpRWxsEtV8xJfPKmIG8xo7mNfYwbzGDubVuvPq5OQUp+MCAAAA8HpJIkS/fv26tGrVSofohQsXtrifo6Ojvr1KfclJal8g1Rc80/u2idv3niDm+p3mJPxFeK5vtolrPuLLZxUxhnmNHcxr7GBeYwfzar15Zc4BAACA+CXRh+gBAQF6cdGGDRtK48aN5fnz53q7auuivsQAAAAAAAAAAGBJoi9z2b59u5w/f17mzZsnqVKlMt1u3rxp7aEBAAAAAAAAAOK5RF+JrirQ1eJNAAAAAAAAAAC8rURfiQ4AAAAAAAAAwLsiRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAntLT8D6fr3qHWev1SyPa5y9FgAAAAAAAAAkFEmiEv3cuXNStmxZSZMmjfTr108MBoO1hwQAAAAAAAAASAASfYgeFBQk9evXl9KlS8vx48fl/PnzsmjRImsPCwAAAAAAAACQACT6di5//PGHeHt7y6RJkyR58uQyevRo6datm7Rv397aQ8M7oMWN9eYjocwJAAAAAAAAEJMSfYh+5swZ8fDw0AG6Urx4cV2NDiBx/oIjKY4jxsdiCBcbXz8xPPcWsXm7P1jiFy0AAAAAACCxSfQhuo+Pj+TKlcv02MbGRuzs7OTp06e6R/qrrV/UzUhVsCvPnj2T8PBwiWv+PnEXwj179rJPvHqvat4cHBzE1tY2Tsfx6ljiy5zEzDgMYuPrIwaxU59EK47j3cWXsRjH8epn1VrjSHz/3bzbZ/VN49hwPe7mpGEu13gxjohjierzmhTnJObHoT6vT8VwP/CtP6+Jd07efxyvfl6t9b+bV8WXcbyrqK4DUVH7KKzjAwAAAMQPiT5Et7e3F0dHR7NtTk5O4u/vHylEHzNmjAwbNizSOdzd3WN9nAAAAEBEvr6+4urKX/gAAAAA1pboQ/S0adPKuXPnIn0hURVArxowYID07t3brFroyZMnki5dOl3BnlSo6qfs2bPL7du3xcXFxdrDSTSY15jHnMYO5jV2MK+xg3mNHcyrdedVVaCrf6+6ubnF6fgAAAAAJNEQvWzZsjJv3jzT4+vXr+uWLSpcf5WqWH+1aj116tSSVKkvd3xxjnnMa8xjTmMH8xo7mNfYwbzGDubVevNKBToAAAAQf7zdinEJ0EcffaSrfhYuXKgfjx49WmrUqKH7ogMAAAAAAAAAIEm9J/r8+fOlVatW0q9fP72I0549e6w9LAAAAAAAAABAApDoQ3SlQYMGcvXqVTlx4oR4eHjoHuewTLW0GTp0aKTWNng/zGvMY05jB/MaO5jX2MG8xg7mNXYwrwAAAEDCZGNQKxcBAAAAAAAAAICk1xMdAAAAAAAAAIB3RYgOAAAAAAAAAIAFhOhJ0IYNGyR37tx60dWSJUvKhQsXotVX3sbGxnSrUaNGnIwVSduiRYvMPnfGm9r+OsWLFzfb39PTM87GjKTl0aNHkitXLrlx48Z7XWMVrrOI68/ru15jFa6zAAAAAJISQvQkRi2w2r59exk7dqzcvXtX8ufPH60vvsePH5e///5bnj59qm8qJMJLPXr0MAsT8ubN+8Zj9u7dK4UKFZL06dPLpEmT4mScCU3r1q1Nnzl1u337tp6vypUrWzzG399ff84fPnxoOm769OlxOu6EFPieO3dOypYtK2nSpJF+/fpJdJfJWLNmjbi7u4ubm5usWLFCkup81qtXz2w+3/Uaq3Cdff1n9V2uswrXWsuf13e5xipcZ+WNvzTj2goAAAAkLoToSYz6cqfCnebNm0umTJmkS5cucurUqdceo4Ig9eWvaNGikjp1an1LkSJFnI05IVDh1+bNm01hwpvm1MvLS1edtmrVSg4fPizLli2T3bt3x9l4EwoHBwfTZ07dlixZIo0bN5Y8efJYPEbNvaqQzJAhg+k4Z2dnSeqiCtCCgoKkfv36Urp0af0ZPn/+fLQqUFU41KZNGxk8eLBs27ZNhgwZIpcuXZKkpmXLljqEfN9rrMJ19vWf1Xe5zipca1//eX2Xa6zCdfb1vzTj2goAAAAkQgYkabNnzzYUL178tfusXbvWkCFDBkPWrFkNyZMnN7Ro0cLw5MmTOBtjfBcSEmJwcXEx+Pr6RvuYyZMnGwoWLGgIDw/Xj9evX29o06ZNLI4y4QsICDBkzJjRcP369dfuN2nSJEO2bNkM6dOnN7i6uho6d+5sCAwMNCR11atXN0ydOlWVQprmcN26dYY0adIY/Pz89OPTp08bPvzwwzeeq2fPnobatWubHk+ZMsUwcOBAQ1Jz7do1/TPinL7LNVbhOvv6z+q7XGcVrrXR/7xG9xqrcJ19YdOmTYa5c+eaHu/atcvg7OzMtRUAAABIhKhET8KCg4Nl4sSJ0rlz59fud/HiRSlRooSuADxy5Ihcv35dBgwYEGfjjO9U+4Xw8HD9Z9yqEq9OnTpy69at1x5z5swZqVq1qm5JoJQrV05OnDgRRyNOmJYvXy7ly5eXnDlzvnY/VbVXqVIlOXDggK7k27Fjh0yePFmSunnz5ul2GK9+Dj08PCR58uT6saosVRWTb6KOq1atmulxUv38qnYjMXGNVbjOvv6z+i7XWYVrbfQ/r9G9xipcZ19QfzHx9ddfm81Lvnz5uLYCAAAAiRAhehI2dOhQ3S7gTf16VZCjviCrgKdYsWIyfvx43bMTL6gvxgUKFJClS5fK2bNndV/UiF+qo+Lj42MWaLi4uMi9e/fiYLQJ15w5c6IVRqr9VB9Z9d+JCoTUn8PzeY06QHv1c6iCRjs7O90q43X4/MbsNVbhOvv6z+q7XGcVPqsxf4017st11vIvzbi2AgAAAImPvbUHAOvYtWuXzJw5U1c8JkuW7K2OzZgxozx+/Fj3/HR0dJSkTvUvVTejWbNm6S/B6suw+gIcFRUARZw7JycnvVAbonblyhV9q1mz5lsfqz6vqlct3vw5jPhZVIvhRfc4Pr8xe41VuM6+/3VW4bMa+9dYheus+S/NBg0axLUVAAAASGSoRE+CVJsAtciaCngKFy78xv1btGih/2TbSC3OphbMI9ixHCaotgP379+3uE/atGn1gndGvr6+eoE3RG316tX6z+ajE0ZWqFBBbt++bfZ5dXd3j+URJkyvfg6j+1nk8xuz11iF62zMX2cVPqsxf41VuM5G/Usz1RJHzSHXVgAAACDxIURPYgICAvQX5YYNG0rjxo3l+fPn+qbWGlMVfSEhIZGOUa0FevXqpQOe9evX67YDXbp0scr446N+/frpL84RwwRbW1vJnj27xWPKli2r9zM6deqUZM2aNdbHmlBt3bpVPv74Y7Ntz549k7CwsEj7FilSRDp16iRHjx6VxYsX6z+v5/Mavc+hCn9V5bMKct7mOD6/0bvGKlxn4+46q/BZffdrrMJ19t1+aca1FQAAAEiErL2yKeLW+vXrVZIT6Xb9+nWDu7u7Yd26dZGOCQ4ONnTo0MGQIkUKQ+bMmQ3Dhg0zhISEWGX88dHSpUsNuXLlMuzcudOwbds2Q/78+Q3t2rXTz3l7e+v5e5WXl5fBycnJsGPHDv18nTp1DN98840VRh//+fv7GxwcHAwXLlww264+t6dOnYq0/9OnTw2NGjUyODs768/0rFmz4nC08Z/xf++K+t9xhgwZDAsWLNCPPT09DfXq1TOby9DQ0EjnOH36tL4enD171uDr62soWbKkYcKECYakKuKcvu4aq3Cdjb6I8/a666zCtfbd5vV111jjvlxnLVNzV7hwYUPHjh31tdB4U581rq0AAABA4mKj/sPaQT6Q0Kmq0dmzZ+uFwz7//HMZPXq07o2aM2dOmTJlijRq1CjKhdl69OghKVOmlNSpU5vaNwCxSS1wp6oi1WdT2bhxo66idHZ21pW9e/bsMVVTqn1VJWTJkiUjnWfgwIEyYcIE3bM3X758sn//fn0OILY+q5auswrXWljDhg0bovzMqc+tWgCXaysAAACQeBCiA1akvmhfvHhRKleurAMewBr+++8/OXHihHh4eEi6dOmifdz58+f1YoJVqlShby/iNa61sAaurQAAAEDiQYgOAAAAAAAAAIAFLCwKAAAAAAAAAIAFhOgAAAAAAAAAAFhAiA4AAAAAAAAAgAWE6AAAAAAAAAAAWECIDgCIU5cuXZLAwEBrDwMAAAAAACBaCNEBIIkJCgqSatWqyYULF/Tj7t27y+zZs03PFyhQQC5fviy3b9+WTZs26W137twRLy+vKM9XsWJF2bVrV5TPnT9/PtK2bt26yahRoyyOLywsTIYPH67H2aFDB5kyZYqcOHFC5s+fr5+vUKGCnDp16i3fNQAAAAAAwLshRAeAJMbR0VE+//xzadGihYSHh+vHP/74o5QoUUKWLFkiqVKlkmbNmomnp6cO2lWY/dNPP+lw3RhkR5QsWTJ9e5U6rnbt2jJr1izTtocPH8qxY8ekZ8+eFsdnZ2en9xswYIDY29uLg4ODzJgxQ/9UQf7Jkyclb968MTgjAAAAAAAAltkYDAbDa54HACRSd+/elaxZs0rfvn2lSJEi0rJlS1H/l/DRRx/J6tWrJXfu3BIaGqqDbOW3336TqVOnypYtW3TQ7urqqrc/f/5cnJ2ddfjt6+urA+8uXbro5w4ePCi1atWS3bt3S7ly5XRYP3DgQEmZMqXZWGxsbOTvv/+WbNmy6Qp4tf/Tp09l+/btkjNnTn1eFcir15g5c6bs379fH6fGp8YcVYgPAAAAAAAQE6hEB4Ak5K+//pJ58+aZVZSrinEfHx9Zv369Dq6N4bTi4eEhx48fl71790rTpk1l3759OgBXVeH//fefPHv2TCpVqiR//PGHfuzk5CTVq1c3nfvDDz+U7777Tvr37y/+/v4yefJkOXTokD7OeFPjyZEjhw7QFXUe1R5m+fLlOrBX4bo6rwrUN2/erKvjVbCeJk0aSZ8+vSxdujTO5xEAAAAAACQdhOgAkISoim5V6T1y5Ei5du2a3qZCaVUdvmPHDh1Wq1YpVatWlfz58+uQ28XFRfr06SMff/yxnD59Wh9jaxv5/z7UsVevXtXHRfT999/L2rVrZciQIVK5cmVdkT569Gh9UzZu3Cj169c37V+2bFndC/3Jkyd6HKqNjAr5p0+froP+lStXyo0bN+Trr7+WQYMG6X0BAAAAAABiy4u/0QcAJAmqSlzdVEW5sQWKao+igvKdO3eaQupXqT7mar/UqVObKtXTpUunW6kkT55cGjVqJAEBATogV61YIlI919VrqRC+WLFietu9e/d0CxilQYMGUrp0adP+ixYtkvHjx8uGDRtkzpw5UrBgQfn333+lU6dOuppdBfU1atTQ51B93AEAAAAAAGITlegAkMSpSu8sWbLItm3b5MGDB1Huo3qWd+3aVW7evCmBgYG6lYrqm66C8UePHumbuq/C9FddvnxZypQpo3uau7u762137twRb29vfV8tYqr6rxupx4cPH9aV6Cq8V+G5agOjwnrVv131WVdUJf2rVe8AAAAAAAAxjRAdAJI4tRBoxowZ5eLFi7rtigq81U0tHqp6kxv9/PPPOtBWleCqh3nNmjV1j3W1QOmVK1d0dfuXX34Z6fwjRozQFerGyndVxX7gwAHd4zwsLCzS/qrivVChQvr8KijPkyePDtBV2K/azvz555+6zcylS5eoRAcAAAAAALGOdi4AkMSpRUKbNGmiW6dEpCrLjcH3rVu39OKgmzZtkjVr1ki1atX0caovuaoOf/z4sXTv3l1XqEd08uRJXeGu+q5HbNeiQnrVDkZVmKvjI1ILkXp6esoHH3yge7Crti+qT3vLli112xnVV10tcqoWNDWODwAAAAAAILYQogNAEqUqylXVd5UqVXRrFlUdHtH169f1T9W+RbVYad++vZQsWVKaN28uW7ZsMS0ami1bNh2eq37qEake6eoYtaCoCsyVEydO6GryrVu36m2qF3ratGkjLQ6qwndVHV+3bl1dEa8qzlX1u9K5c2e94OirrwcAAAAAABAbaOcCAEmM6l/+zz//6Mpx1WZFtVRRwfi5c+fMbmXLltWtVxYuXCheXl66Lcs333wj5cuX16H2f//9pwPtXLlyia2trbRt21b3K1eLjSrTp0/Xx6t9lCVLlugWLap9TKlSpXQovnHjRunRo4duE6N6oBuphUNVX/SKFSvqPu0qkFfnURXx/fr108H/Dz/8oMcAAAAAAAAQmwjRASCJ8fPzk+rVq+uqcBWkqxA9Yi904009bwzB//jjD906RW1Xj9Uio6qKvGjRonrxz7Nnz+o+5mphURXMq+r13r1762pxe3t7adCggQwYMEDWrVunq9qNKlSooHup7969W29XAbwKyseMGSPFixfX59m/f79s3rxZV6arAF8F+Xv27NFtXVRlvLoPAAAAAAAQW2wMxpJBAACiwd/fX4fhDRs2lJQpU5o9p0JvVTnu7u5utv3y5cuSOXNm3dM8KqrS/OnTp+Lm5iY+Pj4ybdo0vUipsYWL+r+qqVOn6t7txm2KakOjAnonJ6dYea8AAAAAAACE6AAAAAAAAAAAWEA7FwAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAsIEQHAAAAAAAAAMACQnQAAAAAAAAAACwgRAcAAAAAAAAAwAJCdAAAAAAAAAAALCBEBwAAAAAAAADAAkJ0AAAAAAAAAAAkav8Hv/SGZyA9c9sAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 测试THD计算函数\n", "def test_matlab_thd():\n", "    \"\"\"测试MATLAB标准THD计算\"\"\"\n", "    print(\"🔍 测试MATLAB标准THD计算...\")\n", "    \n", "    # 生成测试信号：基波 + 3次谐波 + 5次谐波\n", "    fs = 10000  # 采样频率\n", "    t = np.linspace(0, 0.1, int(fs * 0.1))  # 0.1秒\n", "    \n", "    # 基波50Hz，幅值1\n", "    fundamental = 1.0 * np.sin(2 * np.pi * 50 * t)\n", "    # 3次谐波，幅值0.1\n", "    harmonic_3 = 0.1 * np.sin(2 * np.pi * 150 * t)\n", "    # 5次谐波，幅值0.05\n", "    harmonic_5 = 0.05 * np.sin(2 * np.pi * 250 * t)\n", "    \n", "    # 合成信号\n", "    test_signal = fundamental + harmonic_3 + harmonic_5\n", "    \n", "    # 计算THD\n", "    result = extract_matlab_standard_thd(test_signal, fs)\n", "    \n", "    if result:\n", "        theoretical_thd = np.sqrt(0.1**2 + 0.05**2) / 1.0 * 100\n", "        print(f\"\\n✅ 测试完成！\")\n", "        print(f\"理论THD: {theoretical_thd:.2f}%\")\n", "        print(f\"计算THD: {result['thd_percent']:.2f}%\")\n", "        print(f\"误差: {abs(theoretical_thd - result['thd_percent']):.3f}%\")\n", "        \n", "        # 绘制测试结果\n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "        fig.suptitle('MATLAB标准THD计算测试', fontsize=16, fontweight='bold')\n", "        \n", "        # 时域信号\n", "        axes[0, 0].plot(t[:500], test_signal[:500], 'b-', linewidth=1)\n", "        axes[0, 0].set_title('测试信号时域波形')\n", "        axes[0, 0].set_xlabel('时间 (s)')\n", "        axes[0, 0].set_ylabel('幅值')\n", "        axes[0, 0].grid(True, alpha=0.3)\n", "        \n", "        # 频谱\n", "        freqs, yf_mag = result['frequency_spectrum']\n", "        axes[0, 1].plot(freqs[:500], yf_mag[:500], 'r-', linewidth=1)\n", "        axes[0, 1].axvline(50, color='blue', linestyle='--', label='基波 50Hz')\n", "        axes[0, 1].axvline(150, color='green', linestyle='--', label='3次谐波 150Hz')\n", "        axes[0, 1].axvline(250, color='orange', linestyle='--', label='5次谐波 250Hz')\n", "        axes[0, 1].set_title('频谱分析')\n", "        axes[0, 1].set_xlabel('频率 (Hz)')\n", "        axes[0, 1].set_ylabel('幅值')\n", "        axes[0, 1].legend()\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "        \n", "        # 谐波柱状图\n", "        harmonics = list(result['harmonic_details'].keys())\n", "        harmonic_ratios = [result['harmonic_details'][h]['ratio_percent'] for h in harmonics]\n", "        \n", "        bars = axes[1, 0].bar(harmonics, harmonic_ratios, color='skyblue', alpha=0.7)\n", "        axes[1, 0].set_title('谐波含量分布')\n", "        axes[1, 0].set_xlabel('谐波次数')\n", "        axes[1, 0].set_ylabel('相对基波百分比 (%)')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        \n", "        # 结果对比\n", "        comparison_text = f\"\"\"\n", "THD计算结果对比:\n", "\n", "理论值: {theoretical_thd:.2f}%\n", "计算值: {result['thd_percent']:.2f}%\n", "误差: {abs(theoretical_thd - result['thd_percent']):.3f}%\n", "\n", "基波信息:\n", "• 频率: {result['fundamental_freq']:.2f} Hz\n", "• 幅值: {result['fundamental_mag']:.4f}\n", "\n", "主要谐波:\n", "• 3次: {result['harmonic_details'][3]['ratio_percent']:.2f}%\n", "• 5次: {result['harmonic_details'][5]['ratio_percent']:.2f}%\n", "\n", "✅ 测试通过！\n", "\"\"\"\n", "        \n", "        axes[1, 1].text(0.05, 0.95, comparison_text, transform=axes[1, 1].transAxes,\n", "                        verticalalignment='top', fontsize=11,\n", "                        bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))\n", "        axes[1, 1].set_xlim(0, 1)\n", "        axes[1, 1].set_ylim(0, 1)\n", "        axes[1, 1].set_title('测试结果')\n", "        axes[1, 1].axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    return result\n", "\n", "# 运行测试\n", "test_result = test_matlab_thd()"]}, {"cell_type": "code", "execution_count": 4, "id": "load_real_data_cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📂 加载光伏故障数据...\n", "✅ 数据加载成功！\n", "   - 数据长度: 60001 点\n", "   - 采样频率: 20000 Hz\n", "   - 时间范围: 2.950 - 3.000 秒\n"]}], "source": ["# 加载实际光伏故障数据进行THD分析\n", "def load_and_analyze_pv_data():\n", "    \"\"\"加载光伏故障数据并进行THD分析\"\"\"\n", "    print(\"📂 加载光伏故障数据...\")\n", "    \n", "    # 尝试加载数据文件\n", "    data_file = 'pv_fault_data.mat'\n", "    if not Path(data_file).exists():\n", "        print(f\"❌ 数据文件 {data_file} 不存在\")\n", "        return None\n", "    \n", "    try:\n", "        # 加载MATLAB数据\n", "        mat_data = scipy.io.loadmat(data_file)\n", "        \n", "        # 提取关键数据\n", "        time_vector = mat_data['tout'].flatten()\n", "        ia_data = mat_data['Ia'].flatten()\n", "        \n", "        # 计算采样频率\n", "        dt = time_vector[1] - time_vector[0]\n", "        sampling_rate = 1 / dt\n", "        \n", "        print(f\"✅ 数据加载成功！\")\n", "        print(f\"   - 数据长度: {len(ia_data)} 点\")\n", "        print(f\"   - 采样频率: {sampling_rate:.0f} Hz\")\n", "        print(f\"   - 时间范围: {time_vector[0]:.3f} - {time_vector[-1]:.3f} 秒\")\n", "        \n", "        return {\n", "            'time': time_vector,\n", "            'ia': ia_data,\n", "            'sampling_rate': sampling_rate\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 数据加载失败: {e}\")\n", "        return None\n", "\n", "# 加载数据\n", "pv_data = load_and_analyze_pv_data()"]}, {"cell_type": "code", "execution_count": 5, "id": "analyze_pv_thd_cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 分析光伏故障THD变化 (故障时间: 1.0s)...\n", "分析窗口设置:\n"]}, {"ename": "IndexError", "evalue": "index -4000 is out of bounds for axis 0 with size 1000", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 174\u001b[0m\n\u001b[0;32m    172\u001b[0m \u001b[38;5;66;03m# 执行光伏故障THD分析\u001b[39;00m\n\u001b[0;32m    173\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m pv_data \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m--> 174\u001b[0m     fault_analysis_result \u001b[38;5;241m=\u001b[39m \u001b[43manalyze_pv_fault_thd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpv_data\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfault_time\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1.0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m    175\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    176\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m⚠️ 跳过实际数据分析，请确保 pv_fault_data.mat 文件存在\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[1;32mIn[5], line 32\u001b[0m, in \u001b[0;36manalyze_pv_fault_thd\u001b[1;34m(pv_data, fault_time)\u001b[0m\n\u001b[0;32m     29\u001b[0m post_fault_data \u001b[38;5;241m=\u001b[39m ia_data[post_fault_start:post_fault_end]\n\u001b[0;32m     31\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m分析窗口设置:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m---> 32\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m  - 故障前: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mtime_vector\u001b[49m\u001b[43m[\u001b[49m\u001b[43mpre_fault_start\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.3f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtime_vector[pre_fault_end]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.3f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 秒\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     33\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m  - 故障后: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtime_vector[post_fault_start]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.3f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtime_vector[post_fault_end]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.3f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 秒\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     34\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m  - 窗口长度: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(pre_fault_data)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 点 (\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mwindow_duration\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m秒)\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mIndexError\u001b[0m: index -4000 is out of bounds for axis 0 with size 1000"]}], "source": ["# 分析光伏故障前后的THD变化\n", "def analyze_pv_fault_thd(pv_data, fault_time=1.0):\n", "    \"\"\"分析光伏故障前后的THD变化\"\"\"\n", "    if pv_data is None:\n", "        print(\"❌ 无数据可分析\")\n", "        return None\n", "    \n", "    print(f\"\\n🔍 分析光伏故障THD变化 (故障时间: {fault_time}s)...\")\n", "    \n", "    time_vector = pv_data['time']\n", "    ia_data = pv_data['ia']\n", "    fs = pv_data['sampling_rate']\n", "    \n", "    # 找到故障时间点\n", "    fault_idx = np.argmin(np.abs(time_vector - fault_time))\n", "    \n", "    # 定义分析窗口（每个窗口0.1秒，包含5个工频周期）\n", "    window_duration = 0.1  # 秒\n", "    window_samples = int(window_duration * fs)\n", "    \n", "    # 故障前稳态窗口（故障前0.2-0.1秒）\n", "    pre_fault_start = fault_idx - int(0.2 * fs)\n", "    pre_fault_end = fault_idx - int(0.1 * fs)\n", "    pre_fault_data = ia_data[pre_fault_start:pre_fault_end]\n", "    \n", "    # 故障后稳态窗口（故障后0.5-0.6秒）\n", "    post_fault_start = fault_idx + int(0.5 * fs)\n", "    post_fault_end = fault_idx + int(0.6 * fs)\n", "    post_fault_data = ia_data[post_fault_start:post_fault_end]\n", "    \n", "    print(f\"分析窗口设置:\")\n", "    print(f\"  - 故障前: {time_vector[pre_fault_start]:.3f} - {time_vector[pre_fault_end]:.3f} 秒\")\n", "    print(f\"  - 故障后: {time_vector[post_fault_start]:.3f} - {time_vector[post_fault_end]:.3f} 秒\")\n", "    print(f\"  - 窗口长度: {len(pre_fault_data)} 点 ({window_duration}秒)\")\n", "    \n", "    # 计算故障前THD\n", "    print(f\"\\n📊 故障前THD分析:\")\n", "    pre_fault_thd = extract_matlab_standard_thd(pre_fault_data, fs, verbose=True)\n", "    \n", "    # 计算故障后THD\n", "    print(f\"\\n📊 故障后THD分析:\")\n", "    post_fault_thd = extract_matlab_standard_thd(post_fault_data, fs, verbose=True)\n", "    \n", "    if pre_fault_thd and post_fault_thd:\n", "        # 计算变化量\n", "        thd_change = post_fault_thd['thd_percent'] - pre_fault_thd['thd_percent']\n", "        thd_change_ratio = (thd_change / pre_fault_thd['thd_percent']) * 100\n", "        \n", "        print(f\"\\n📈 THD变化分析:\")\n", "        print(f\"  - 故障前THD: {pre_fault_thd['thd_percent']:.3f}%\")\n", "        print(f\"  - 故障后THD: {post_fault_thd['thd_percent']:.3f}%\")\n", "        print(f\"  - 绝对变化: {thd_change:+.3f}%\")\n", "        print(f\"  - 相对变化: {thd_change_ratio:+.1f}%\")\n", "        \n", "        # 绘制对比分析图\n", "        plot_thd_comparison(pre_fault_thd, post_fault_thd, \n", "                           pre_fault_data, post_fault_data, \n", "                           time_vector[pre_fault_start:pre_fault_end],\n", "                           time_vector[post_fault_start:post_fault_end])\n", "        \n", "        return {\n", "            'pre_fault': pre_fault_thd,\n", "            'post_fault': post_fault_thd,\n", "            'thd_change': thd_change,\n", "            'thd_change_ratio': thd_change_ratio\n", "        }\n", "    \n", "    return None\n", "\n", "def plot_thd_comparison(pre_thd, post_thd, pre_data, post_data, pre_time, post_time):\n", "    \"\"\"绘制故障前后THD对比分析图\"\"\"\n", "    fig, axes = plt.subplots(3, 2, figsize=(16, 18))\n", "    fig.suptitle('光伏故障前后THD对比分析 - MATLAB标准计算', fontsize=16, fontweight='bold')\n", "    \n", "    # 第一行：时域波形对比\n", "    axes[0, 0].plot(pre_time, pre_data, 'b-', linewidth=1, label='故障前')\n", "    axes[0, 0].set_title('故障前电流波形')\n", "    axes[0, 0].set_xlabel('时间 (s)')\n", "    axes[0, 0].set_ylabel('电流 (A)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    axes[0, 0].legend()\n", "    \n", "    axes[0, 1].plot(post_time, post_data, 'r-', linewidth=1, label='故障后')\n", "    axes[0, 1].set_title('故障后电流波形')\n", "    axes[0, 1].set_xlabel('时间 (s)')\n", "    axes[0, 1].set_ylabel('电流 (A)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].legend()\n", "    \n", "    # 第二行：频谱对比\n", "    pre_freqs, pre_mag = pre_thd['frequency_spectrum']\n", "    post_freqs, post_mag = post_thd['frequency_spectrum']\n", "    \n", "    axes[1, 0].plot(pre_freqs[:500], pre_mag[:500], 'b-', linewidth=1)\n", "    axes[1, 0].axvline(pre_thd['fundamental_freq'], color='red', linestyle='--', \n", "                      linewidth=2, label=f\"基波 {pre_thd['fundamental_freq']:.1f}Hz\")\n", "    axes[1, 0].set_title('故障前频谱')\n", "    axes[1, 0].set_xlabel('频率 (Hz)')\n", "    axes[1, 0].set_ylabel('幅值')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    axes[1, 1].plot(post_freqs[:500], post_mag[:500], 'r-', linewidth=1)\n", "    axes[1, 1].axvline(post_thd['fundamental_freq'], color='red', linestyle='--', \n", "                      linewidth=2, label=f\"基波 {post_thd['fundamental_freq']:.1f}Hz\")\n", "    axes[1, 1].set_title('故障后频谱')\n", "    axes[1, 1].set_xlabel('频率 (Hz)')\n", "    axes[1, 1].set_ylabel('幅值')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # 第三行：谐波对比和结果总结\n", "    # 谐波对比\n", "    harmonics = sorted(set(list(pre_thd['harmonic_details'].keys()) + \n", "                          list(post_thd['harmonic_details'].keys())))\n", "    \n", "    pre_ratios = [pre_thd['harmonic_details'].get(h, {'ratio_percent': 0})['ratio_percent'] for h in harmonics]\n", "    post_ratios = [post_thd['harmonic_details'].get(h, {'ratio_percent': 0})['ratio_percent'] for h in harmonics]\n", "    \n", "    x = np.arange(len(harmonics))\n", "    width = 0.35\n", "    \n", "    bars1 = axes[2, 0].bar(x - width/2, pre_ratios, width, label='故障前', color='blue', alpha=0.7)\n", "    bars2 = axes[2, 0].bar(x + width/2, post_ratios, width, label='故障后', color='red', alpha=0.7)\n", "    \n", "    axes[2, 0].set_title('谐波含量对比')\n", "    axes[2, 0].set_xlabel('谐波次数')\n", "    axes[2, 0].set_ylabel('相对基波百分比 (%)')\n", "    axes[2, 0].set_xticks(x)\n", "    axes[2, 0].set_xticklabels(harmonics)\n", "    axes[2, 0].legend()\n", "    axes[2, 0].grid(True, alpha=0.3)\n", "    \n", "    # 结果总结\n", "    thd_change = post_thd['thd_percent'] - pre_thd['thd_percent']\n", "    thd_change_ratio = (thd_change / pre_thd['thd_percent']) * 100\n", "    \n", "    summary_text = f\"\"\"\n", "THD分析结果总结 (MATLAB标准):\n", "\n", "故障前:\n", "• THD: {pre_thd['thd_percent']:.3f}%\n", "• 基波: {pre_thd['fundamental_freq']:.2f}Hz, {pre_thd['fundamental_mag']:.4f}\n", "• 主要谐波: {len(pre_thd['harmonic_details'])}次\n", "\n", "故障后:\n", "• THD: {post_thd['thd_percent']:.3f}%\n", "• 基波: {post_thd['fundamental_freq']:.2f}Hz, {post_thd['fundamental_mag']:.4f}\n", "• 主要谐波: {len(post_thd['harmonic_details'])}次\n", "\n", "变化分析:\n", "• 绝对变化: {thd_change:+.3f}%\n", "• 相对变化: {thd_change_ratio:+.1f}%\n", "• 变化趋势: {'增加' if thd_change > 0 else '减少'}\n", "\n", "结论:\n", "{'✅ THD计算符合MATLAB标准' if abs(thd_change) > 0.1 else '⚠️ THD变化较小'}\n", "{'故障导致谐波增加，电能质量恶化' if thd_change > 0 else '故障后谐波减少'}\n", "\"\"\"\n", "    \n", "    axes[2, 1].text(0.05, 0.95, summary_text, transform=axes[2, 1].transAxes,\n", "                   verticalalignment='top', fontsize=10,\n", "                   bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))\n", "    axes[2, 1].set_xlim(0, 1)\n", "    axes[2, 1].set_ylim(0, 1)\n", "    axes[2, 1].set_title('分析结果总结')\n", "    axes[2, 1].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 执行光伏故障THD分析\n", "if pv_data is not None:\n", "    fault_analysis_result = analyze_pv_fault_thd(pv_data, fault_time=1.0)\n", "else:\n", "    print(\"⚠️ 跳过实际数据分析，请确保 pv_fault_data.mat 文件存在\")"]}], "metadata": {"kernelspec": {"display_name": "matlab", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}