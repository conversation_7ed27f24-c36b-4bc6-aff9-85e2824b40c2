# 导入基础库
import numpy as np
import pandas as pd
import scipy.io
import scipy.stats
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

# 尝试导入可选库
try:
    import pywt
    PYWT_AVAILABLE = True
    print("✅ PyWavelets 可用")
except ImportError:
    PYWT_AVAILABLE = False
    print("⚠️ PyWavelets 不可用，将跳过小波分析")

try:
    import seaborn as sns
    sns.set_style("whitegrid")
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False
    print("⚠️ Seaborn 不可用，使用基础matplotlib")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("✅ 库导入完成！")

# 改进的配置参数类
class ImprovedConfig:
    """改进的分析配置参数"""
    
    # 基础参数
    MAT_FILE_PATH = 'pv_fault_circult_data.mat'
    SIM_TIME = 3.0                    # 仿真时间(秒)
    FAULT_TIME = 1.0                  # 故障时间(秒)
    GRID_FREQ = 60.0                  # 电网频率(Hz)
    RATED_POWER = 400000              # 额定功率(W)
    RATED_VOLTAGE = 380               # 额定电压(V)
    RATED_CURRENT = 608               # 额定电流(A)
    
    # 分窗参数 (基于60Hz周期)
    CYCLES_PER_WINDOW = 2             # 每窗口周期数 (2×16.67ms = 33.33ms)
    WINDOW_OVERLAP = 0.5              # 窗口重叠率
    
    # 电能质量标准 (60Hz系统)
    THD_LIMIT = 0.05                  # THD限值(5%)
    VOLTAGE_DEVIATION_LIMIT = 0.07    # 电压偏差限值(±7%)
    FREQUENCY_DEVIATION_LIMIT = 0.24  # 频率偏差限值(±0.24Hz, 60Hz系统标准)
    
    # 谐波分析
    HARMONICS = [3, 5, 7, 11, 13, 15, 17, 19]
    
    # 小波分析
    WAVELET_TYPE = 'db4'
    WAVELET_LEVEL = 4
    
    # 故障分析
    PRE_FAULT_WINDOWS = 10
    POST_FAULT_OFFSET = 8
    
    # 图表设置
    FIGURE_SIZE = (15, 10)
    SAVE_PLOTS = True
    DPI = 300
    
    # 文件组织 - 改进1
    OUTPUT_DIR = 'results'
    PLOTS_DIR = 'results/plots'
    DATA_DIR = 'results/data'
    REPORTS_DIR = 'results/reports'
    
    # 故障影响量化参数 - 改进4
    IMPACT_WEIGHTS = {
        'thd_weight': 0.3,
        'power_weight': 0.25,
        'voltage_weight': 0.2,
        'frequency_weight': 0.15,
        'harmonic_weight': 0.1
    }
    
    SEVERITY_THRESHOLDS = {
        'mild': 0.3,      # 轻微影响
        'moderate': 0.6,  # 中等影响
        'severe': 1.0     # 严重影响
    }

config = ImprovedConfig()

# 创建输出文件夹结构
os.makedirs(config.OUTPUT_DIR, exist_ok=True)
os.makedirs(config.PLOTS_DIR, exist_ok=True)
os.makedirs(config.DATA_DIR, exist_ok=True)
os.makedirs(config.REPORTS_DIR, exist_ok=True)

print("✅ 改进配置加载完成！")
print(f"📁 输出文件夹结构已创建:")
print(f"   - 主目录: {config.OUTPUT_DIR}")
print(f"   - 图表: {config.PLOTS_DIR}")
print(f"   - 数据: {config.DATA_DIR}")
print(f"   - 报告: {config.REPORTS_DIR}")

# 数据加载函数
def load_data(config):
    """加载和验证数据"""
    print("📊 加载数据...")
    
    try:
        mat_data = scipy.io.loadmat(config.MAT_FILE_PATH)
        print(f"✅ 文件加载成功: {config.MAT_FILE_PATH}")
        print(f"📋 包含变量: {list(mat_data.keys())}")
    except FileNotFoundError:
        print(f"❌ 文件未找到: {config.MAT_FILE_PATH}")
        return None, None, None
    
    # 提取必要变量
    required_vars = ['Ia', 'Ib', 'Ic', 'Va', 'Vb', 'Vc', 'Vdc']
    data_dict = {}
    
    for var in required_vars:
        if var in mat_data:
            data_dict[var] = mat_data[var].flatten()
            print(f"✅ {var}: {len(data_dict[var])} 个采样点")
        else:
            print(f"❌ 缺少变量: {var}")
            return None, None, None
    
    # 计算采样参数
    num_samples = len(data_dict['Ia'])
    sampling_rate = num_samples / config.SIM_TIME
    time_vector = np.linspace(0, config.SIM_TIME, num_samples)
    
    print(f"📊 采样信息:")
    print(f"   - 采样点数: {num_samples}")
    print(f"   - 采样频率: {sampling_rate:.2f} Hz")
    print(f"   - 时间间隔: {1/sampling_rate*1000:.3f} ms")
    
    return data_dict, sampling_rate, time_vector

# 数据探索和加载
def explore_mat_file(file_path):
    """探索.mat文件中的所有变量，寻找THD相关数据"""
    print("🔍 探索.mat文件内容...")
    
    try:
        import scipy.io
        mat_data = scipy.io.loadmat(file_path)
        
        print(f"\n📂 文件: {file_path}")
        print(f"📊 变量总数: {len(mat_data)}")
        
        # 过滤掉MATLAB内部变量
        user_vars = {k: v for k, v in mat_data.items() if not k.startswith('__')}
        
        print(f"\n📋 用户变量列表:")
        thd_related_vars = []
        
        for var_name, var_data in user_vars.items():
            var_shape = var_data.shape if hasattr(var_data, 'shape') else 'scalar'
            var_type = type(var_data).__name__
            
            # 检查是否为THD相关变量
            is_thd_related = any(thd_keyword in var_name.lower() for thd_keyword in 
                               ['thd', 'harmonic', 'distortion', 'quality'])
            
            if is_thd_related:
                thd_related_vars.append(var_name)
                print(f"  🎯 {var_name}: {var_shape} ({var_type}) [THD相关]")
            else:
                print(f"  📄 {var_name}: {var_shape} ({var_type})")
        
        if thd_related_vars:
            print(f"\n✅ 发现 {len(thd_related_vars)} 个THD相关变量: {thd_related_vars}")
        else:
            print(f"\n⚠️ 未发现明显的THD相关变量")
            print(f"   将检查可能的隐含THD数据...")
        
        return mat_data, thd_related_vars
        
    except Exception as e:
        print(f"❌ 文件探索失败: {e}")
        return None, []

# 探索数据文件
mat_data, thd_vars = explore_mat_file(config.MAT_FILE_PATH)

# 加载数据
data_dict, sampling_rate, time_vector = load_data(config)

if data_dict is not None:
    print("\n✅ 数据加载完成！")
    
    # 检查是否有预计算的THD数据
    if thd_vars:
        print(f"\n🎯 检测到预计算THD数据，将优先使用:")
        for var in thd_vars:
            if var in data_dict:
                thd_data = data_dict[var]
                print(f"  - {var}: 长度 {len(thd_data)}, 范围 [{np.min(thd_data):.4f}, {np.max(thd_data):.4f}]")
            else:
                print(f"  - {var}: 未在data_dict中找到")
else:
    print("\n❌ 数据加载失败！")

# 改进3: 数据可视化改进 - 拆分图表，解决重合问题
def plot_improved_overview(data_dict, time_vector, config):
    """绘制改进的数据概览 - 拆分为多个独立图表"""
    if data_dict is None:
        print("❌ 无数据可绘制")
        return
    
    # 图1: 三相电流分离显示
    fig1, axes1 = plt.subplots(3, 1, figsize=(15, 12))
    fig1.suptitle('三相电流分离显示', fontsize=16, fontweight='bold')
    
    phases = ['A', 'B', 'C']
    currents = ['Ia', 'Ib', 'Ic']
    colors = ['blue', 'red', 'green']
    
    for i, (phase, current, color) in enumerate(zip(phases, currents, colors)):
        axes1[i].plot(time_vector, data_dict[current], color=color, linewidth=1.5, label=f'{phase}相电流')
        axes1[i].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')
        axes1[i].set_title(f'{phase}相电流')
        axes1[i].set_ylabel('电流 (A)')
        axes1[i].legend()
        axes1[i].grid(True, alpha=0.3)
        
        # 添加故障前后的统计信息
        fault_idx = int(config.FAULT_TIME * len(time_vector) / config.SIM_TIME)
        pre_rms = np.sqrt(np.mean(data_dict[current][:fault_idx]**2))
        post_rms = np.sqrt(np.mean(data_dict[current][fault_idx:]**2))
        
        axes1[i].text(0.02, 0.95, f'故障前RMS: {pre_rms:.2f}A\n故障后RMS: {post_rms:.2f}A\n变化: {(post_rms-pre_rms)/pre_rms*100:.1f}%',
                     transform=axes1[i].transAxes, verticalalignment='top',
                     bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    axes1[2].set_xlabel('时间 (s)')
    plt.tight_layout()
    if config.SAVE_PLOTS:
        plt.savefig(f'{config.PLOTS_DIR}/three_phase_currents_separated.png', dpi=config.DPI, bbox_inches='tight')
    plt.show()
    
    # 图2: 三相电压改进显示
    fig2, axes2 = plt.subplots(2, 2, figsize=(15, 10))
    fig2.suptitle('三相电压改进显示', fontsize=16, fontweight='bold')
    
    # 子图1: 三相电压叠加（使用不同线型）
    voltages = ['Va', 'Vb', 'Vc']
    linestyles = ['-', '--', '-.']
    alphas = [0.8, 0.7, 0.6]
    
    for voltage, color, linestyle, alpha in zip(voltages, colors, linestyles, alphas):
        axes2[0, 0].plot(time_vector, data_dict[voltage], color=color, linestyle=linestyle, 
                        alpha=alpha, linewidth=2, label=voltage)
    
    axes2[0, 0].axvline(config.FAULT_TIME, color='red', linestyle=':', linewidth=3, label='故障时刻')
    axes2[0, 0].set_title('三相电压叠加显示')
    axes2[0, 0].set_ylabel('电压 (V)')
    axes2[0, 0].legend()
    axes2[0, 0].grid(True, alpha=0.3)
    
    # 子图2: A相电压放大（故障前后）
    start_idx = int((config.FAULT_TIME - 0.1) * len(time_vector) / config.SIM_TIME)
    end_idx = int((config.FAULT_TIME + 0.1) * len(time_vector) / config.SIM_TIME)
    axes2[0, 1].plot(time_vector[start_idx:end_idx], data_dict['Va'][start_idx:end_idx], 
                    color='blue', linewidth=2)
    axes2[0, 1].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')
    axes2[0, 1].set_title('A相电压 (故障前后0.1秒)')
    axes2[0, 1].set_ylabel('电压 (V)')
    axes2[0, 1].legend()
    axes2[0, 1].grid(True, alpha=0.3)
    
    # 子图3: 直流母线电压
    axes2[1, 0].plot(time_vector, data_dict['Vdc'], color='purple', linewidth=2)
    axes2[1, 0].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')
    axes2[1, 0].set_title('直流母线电压')
    axes2[1, 0].set_ylabel('电压 (V)')
    axes2[1, 0].set_xlabel('时间 (s)')
    axes2[1, 0].legend()
    axes2[1, 0].grid(True, alpha=0.3)
    
    # 子图4: 瞬时功率
    p_inst = (data_dict['Va'] * data_dict['Ia'] + 
              data_dict['Vb'] * data_dict['Ib'] + 
              data_dict['Vc'] * data_dict['Ic'])
    axes2[1, 1].plot(time_vector, p_inst/1000, color='green', linewidth=2)
    axes2[1, 1].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')
    axes2[1, 1].set_title('瞬时功率')
    axes2[1, 1].set_ylabel('功率 (kW)')
    axes2[1, 1].set_xlabel('时间 (s)')
    axes2[1, 1].legend()
    axes2[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    if config.SAVE_PLOTS:
        plt.savefig(f'{config.PLOTS_DIR}/voltage_and_power_analysis.png', dpi=config.DPI, bbox_inches='tight')
    plt.show()
    
    print("✅ 改进的数据概览图绘制完成")

# 绘制改进的概览图
plot_improved_overview(data_dict, time_vector, config)

# 改进2: THD计算逻辑修正
def extract_basic_features(signal_data):
    """提取基础时域特征"""
    features = {}
    
    # 基础统计特征
    features['rms'] = np.sqrt(np.mean(signal_data**2))
    features['peak'] = np.max(np.abs(signal_data))
    features['mean'] = np.mean(signal_data)
    features['std'] = np.std(signal_data)
    features['skewness'] = scipy.stats.skew(signal_data)
    features['kurtosis'] = scipy.stats.kurtosis(signal_data)
    
    # 波形因子
    if features['rms'] != 0:
        features['crest_factor'] = features['peak'] / features['rms']
    else:
        features['crest_factor'] = 0
    
    return features

def extract_improved_frequency_features(signal_data, fs, config, window_center_time=None, use_precomputed_thd=True):
    """改进的频域特征提取 - 支持预计算THD数据"""
    features = {}
    N = len(signal_data)
    
    # 检查是否使用预计算THD
    thd_from_precomputed = False
    if (use_precomputed_thd and precomputed_thd is not None and 
        window_center_time is not None and len(thd_vars) > 0):
        
        # 从预计算数据中获取THD值
        thd_value = extract_thd_at_time(precomputed_thd, time_vector, window_center_time)
        
        if thd_value is not None:
            # 确保THD值的格式正确
            if thd_value > 1:  # 如果是百分比形式
                features['thd_percent'] = thd_value
                features['thd'] = thd_value / 100
            else:  # 如果是小数形式
                features['thd'] = thd_value
                features['thd_percent'] = thd_value * 100
            
            features['thd_source'] = 'precomputed'
            thd_from_precomputed = True
            print(f"  📊 使用预计算THD: {features['thd_percent']:.3f}% (时间: {window_center_time:.3f}s)")
    
    # 如果没有使用预计算THD，则进行FFT计算
    if not thd_from_precomputed:
        # 确保信号长度为偶数，提高FFT精度
        if N % 2 != 0:
            signal_data = signal_data[:-1]
            N = len(signal_data)
        
        # 应用汉宁窗减少频谱泄漏
        windowed_signal = signal_data * np.hanning(N)
        
        # FFT分析
        yf = np.fft.fft(windowed_signal)
        yf_mag = 2.0/N * np.abs(yf[:N//2])
        freqs = np.fft.fftfreq(N, 1/fs)[:N//2]
    
    # 基波分析 - 更精确的峰值检测
    fundamental_freq = config.GRID_FREQ
    freq_resolution = fs / N
    search_range = max(1, int(2 / freq_resolution))  # ±2Hz搜索范围
    
    fundamental_idx = np.argmin(np.abs(freqs - fundamental_freq))
    start_idx = max(0, fundamental_idx - search_range)
    end_idx = min(len(yf_mag), fundamental_idx + search_range)
    
    # 在搜索范围内找到真正的峰值
    if end_idx > start_idx:
        local_peak_idx = np.argmax(yf_mag[start_idx:end_idx])
        fundamental_idx = start_idx + local_peak_idx
    
    features['fundamental_mag'] = yf_mag[fundamental_idx]
    features['fundamental_freq'] = freqs[fundamental_idx]
    
    # 谐波分析 - 修正计算逻辑
    harmonics_power = 0
    harmonic_details = {}
    
    for h in config.HARMONICS:
        harmonic_freq = h * features['fundamental_freq']  # 使用实际检测到的基波频率
        
        # 确保谐波频率在有效范围内
        if harmonic_freq >= fs/2:
            continue
            
        harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))
        
        # 谐波峰值搜索
        h_search_range = max(1, int(1 / freq_resolution))  # ±1Hz搜索范围
        h_start_idx = max(0, harmonic_idx - h_search_range)
        h_end_idx = min(len(yf_mag), harmonic_idx + h_search_range)
        
        if h_end_idx > h_start_idx:
            local_h_peak_idx = np.argmax(yf_mag[h_start_idx:h_end_idx])
            harmonic_idx = h_start_idx + local_h_peak_idx
            harmonic_mag = yf_mag[harmonic_idx]
            
            features[f'h{h}_mag'] = harmonic_mag
            features[f'h{h}_freq'] = freqs[harmonic_idx]
            harmonics_power += harmonic_mag**2
            harmonic_details[h] = harmonic_mag
    
        # THD计算 - 如果没有预计算THD，则重新计算
        if not thd_from_precomputed:
            if features['fundamental_mag'] > 1e-10:  # 避免除零错误
                # THD = sqrt(sum(harmonics^2)) / fundamental
                # 注意：故障后谐波增加，THD应该增大
                features['thd'] = np.sqrt(harmonics_power) / features['fundamental_mag']
                features['thd_percent'] = features['thd'] * 100  # 百分比形式
                features['thd_source'] = 'calculated'
                print(f"  🔄 重新计算THD: {features['thd_percent']:.3f}%")
            else:
                features['thd'] = 0
                features['thd_percent'] = 0
                features['thd_source'] = 'calculated'
        
        # 验证THD计算的物理意义（无论是否预计算都需要）
        if 'fundamental_mag' in features and features['fundamental_mag'] > 0:
            features['fundamental_power'] = features['fundamental_mag']**2
            features['harmonics_total_power'] = harmonics_power
            features['signal_quality'] = features['fundamental_power'] / (features['fundamental_power'] + harmonics_power)
            
            # 计算各次谐波的相对含量
            for h, mag in harmonic_details.items():
                features[f'h{h}_ratio'] = mag / features['fundamental_mag']
        else:
            features['fundamental_power'] = 0
            features['harmonics_total_power'] = 0
            features['signal_quality'] = 0
    
    # 如果完全没有进行FFT计算（只使用预计算THD），设置默认值
    if thd_from_precomputed and 'fundamental_mag' not in features:
        features['fundamental_mag'] = 0
        features['fundamental_freq'] = config.GRID_FREQ
        features['fundamental_power'] = 0
        features['harmonics_total_power'] = 0
        features['signal_quality'] = 0
    
    return features

def extract_wavelet_features(signal_data, config):
    """提取小波特征(如果可用)"""
    features = {}
    
    if not PYWT_AVAILABLE:
        return features
    
    try:
        coeffs = pywt.wavedec(signal_data, config.WAVELET_TYPE, level=config.WAVELET_LEVEL)
        
        # 各层能量
        for i in range(config.WAVELET_LEVEL):
            features[f'wavelet_d{i+1}'] = np.sum(coeffs[i+1]**2)
        
        features['wavelet_a'] = np.sum(coeffs[0]**2)
    except:
        pass
    
    return features

print("✅ 改进的特征提取函数定义完成")

# 预计算THD数据处理函数
def extract_thd_at_time(thd_data, time_vector, target_time):
    """从预计算THD数据中提取指定时间的THD值"""
    try:
        # 找到最接近目标时间的索引
        time_idx = np.argmin(np.abs(time_vector - target_time))
        
        # 确保索引在有效范围内
        if 0 <= time_idx < len(thd_data):
            thd_value = thd_data[time_idx]
            return float(thd_value)
        else:
            return None
    except Exception as e:
        print(f"⚠️ THD提取错误: {e}")
        return None

def get_precomputed_thd_data():
    """获取预计算的THD数据"""
    if not thd_vars or not data_dict:
        return None
    
    # 优先选择最可能的THD变量
    priority_names = ['THD', 'thd', 'THD_A', 'ia_thd', 'THD_Ia', 'current_thd']
    
    for name in priority_names:
        if name in thd_vars and name in data_dict:
            thd_data = data_dict[name].flatten()
            print(f"📊 使用预计算THD数据: {name}")
            print(f"   - 数据长度: {len(thd_data)}")
            print(f"   - 数值范围: [{np.min(thd_data):.4f}, {np.max(thd_data):.4f}]")
            return thd_data
    
    # 如果没有找到优先名称，使用第一个THD相关变量
    if thd_vars:
        first_thd_var = thd_vars[0]
        if first_thd_var in data_dict:
            thd_data = data_dict[first_thd_var].flatten()
            print(f"📊 使用预计算THD数据: {first_thd_var} (备选)")
            print(f"   - 数据长度: {len(thd_data)}")
            print(f"   - 数值范围: [{np.min(thd_data):.4f}, {np.max(thd_data):.4f}]")
            return thd_data
    
    return None

# 获取预计算THD数据
precomputed_thd = get_precomputed_thd_data()

print("✅ 预计算THD处理函数定义完成")
if precomputed_thd is not None:
    print("🎯 将在特征提取中优先使用预计算THD数据")
    print(f"   THD数据统计: 均值={np.mean(precomputed_thd):.4f}, 标准差={np.std(precomputed_thd):.4f}")
else:
    print("🔄 将使用FFT重新计算THD数据")

# 改进4: 故障影响量化指标设计
class FaultImpactQuantifier:
    """故障影响量化器 - 设计综合影响因子"""
    
    def __init__(self, config):
        self.config = config
        self.weights = config.IMPACT_WEIGHTS
        self.thresholds = config.SEVERITY_THRESHOLDS
    
    def calculate_thd_impact(self, pre_thd, post_thd):
        """计算THD影响分量"""
        if pre_thd == 0:
            return 0
        
        # THD相对变化率
        thd_change_ratio = (post_thd - pre_thd) / pre_thd
        
        # 超标程度（相对于国标限值）
        pre_exceed = max(0, (pre_thd - self.config.THD_LIMIT) / self.config.THD_LIMIT)
        post_exceed = max(0, (post_thd - self.config.THD_LIMIT) / self.config.THD_LIMIT)
        exceed_change = post_exceed - pre_exceed
        
        # 综合THD影响 = 变化率 + 超标恶化程度
        thd_impact = abs(thd_change_ratio) + exceed_change
        
        return min(thd_impact, 2.0)  # 限制最大值
    
    def calculate_power_impact(self, pre_power, post_power):
        """计算功率影响分量"""
        if pre_power == 0:
            return 0
        
        # 功率损失率
        power_loss_ratio = (pre_power - post_power) / pre_power
        
        # 相对于额定功率的损失程度
        rated_loss_ratio = (pre_power - post_power) / self.config.RATED_POWER
        
        # 综合功率影响
        power_impact = abs(power_loss_ratio) + abs(rated_loss_ratio)
        
        return min(power_impact, 2.0)
    
    def calculate_voltage_impact(self, pre_voltage, post_voltage):
        """计算电压影响分量"""
        if pre_voltage == 0:
            return 0
        
        # 电压偏差变化
        pre_deviation = abs(pre_voltage - self.config.RATED_VOLTAGE) / self.config.RATED_VOLTAGE
        post_deviation = abs(post_voltage - self.config.RATED_VOLTAGE) / self.config.RATED_VOLTAGE
        
        # 超标程度
        pre_exceed = max(0, (pre_deviation - self.config.VOLTAGE_DEVIATION_LIMIT) / self.config.VOLTAGE_DEVIATION_LIMIT)
        post_exceed = max(0, (post_deviation - self.config.VOLTAGE_DEVIATION_LIMIT) / self.config.VOLTAGE_DEVIATION_LIMIT)
        
        voltage_impact = abs(post_deviation - pre_deviation) + (post_exceed - pre_exceed)
        
        return min(voltage_impact, 2.0)
    
    def calculate_frequency_impact(self, pre_freq, post_freq):
        """计算频率影响分量"""
        # 频率偏差变化
        pre_deviation = abs(pre_freq - self.config.GRID_FREQ)
        post_deviation = abs(post_freq - self.config.GRID_FREQ)
        
        # 超标程度
        pre_exceed = max(0, (pre_deviation - self.config.FREQUENCY_DEVIATION_LIMIT) / self.config.FREQUENCY_DEVIATION_LIMIT)
        post_exceed = max(0, (post_deviation - self.config.FREQUENCY_DEVIATION_LIMIT) / self.config.FREQUENCY_DEVIATION_LIMIT)
        
        frequency_impact = abs(post_deviation - pre_deviation) / self.config.GRID_FREQ + (post_exceed - pre_exceed)
        
        return min(frequency_impact, 2.0)
    
    def calculate_harmonic_impact(self, pre_harmonics, post_harmonics):
        """计算谐波影响分量"""
        harmonic_changes = []
        
        for h in self.config.HARMONICS:
            pre_h = pre_harmonics.get(f'h{h}_mag', 0)
            post_h = post_harmonics.get(f'h{h}_mag', 0)
            
            if pre_h > 0:
                change_ratio = abs(post_h - pre_h) / pre_h
                harmonic_changes.append(change_ratio)
        
        if harmonic_changes:
            harmonic_impact = np.mean(harmonic_changes)
        else:
            harmonic_impact = 0
        
        return min(harmonic_impact, 2.0)
    
    def calculate_comprehensive_impact_factor(self, pre_features, post_features):
        """计算综合故障影响因子"""
        
        # 计算各分量影响
        thd_impact = self.calculate_thd_impact(
            pre_features.get('ia_thd', 0),
            post_features.get('ia_thd', 0)
        )
        
        power_impact = self.calculate_power_impact(
            pre_features.get('active_power', 0),
            post_features.get('active_power', 0)
        )
        
        voltage_impact = self.calculate_voltage_impact(
            pre_features.get('vdc_rms', self.config.RATED_VOLTAGE),
            post_features.get('vdc_rms', self.config.RATED_VOLTAGE)
        )
        
        frequency_impact = self.calculate_frequency_impact(
            pre_features.get('ia_fundamental_freq', self.config.GRID_FREQ),
            post_features.get('ia_fundamental_freq', self.config.GRID_FREQ)
        )
        
        harmonic_impact = self.calculate_harmonic_impact(pre_features, post_features)
        
        # 加权综合影响因子
        comprehensive_impact = (
            self.weights['thd_weight'] * thd_impact +
            self.weights['power_weight'] * power_impact +
            self.weights['voltage_weight'] * voltage_impact +
            self.weights['frequency_weight'] * frequency_impact +
            self.weights['harmonic_weight'] * harmonic_impact
        )
        
        # 影响严重程度分级
        if comprehensive_impact <= self.thresholds['mild']:
            severity = 'mild'  # 轻微
            severity_cn = '轻微'
        elif comprehensive_impact <= self.thresholds['moderate']:
            severity = 'moderate'  # 中等
            severity_cn = '中等'
        else:
            severity = 'severe'  # 严重
            severity_cn = '严重'
        
        return {
            'comprehensive_impact_factor': comprehensive_impact,
            'severity_level': severity,
            'severity_level_cn': severity_cn,
            'component_impacts': {
                'thd_impact': thd_impact,
                'power_impact': power_impact,
                'voltage_impact': voltage_impact,
                'frequency_impact': frequency_impact,
                'harmonic_impact': harmonic_impact
            }
        }

print("✅ 故障影响量化器定义完成")

# THD验证和对比功能
def validate_thd_comparison(data_dict, time_vector, sampling_rate, config, num_test_windows=10):
    """验证预计算THD与重新计算THD的差异"""
    print("🔍 THD计算方法验证和对比...")
    
    if precomputed_thd is None:
        print("⚠️ 没有预计算THD数据，跳过对比验证")
        return None
    
    # 随机选择测试窗口
    window_size = int(0.1 * sampling_rate)  # 0.1秒窗口
    max_start = len(data_dict['Ia']) - window_size
    
    test_indices = np.random.choice(max_start, min(num_test_windows, max_start//window_size), replace=False)
    
    comparison_results = []
    
    print(f"\n📊 对比 {len(test_indices)} 个测试窗口:")
    
    for i, start_idx in enumerate(test_indices):
        end_idx = start_idx + window_size
        window_center_time = (start_idx + end_idx) / 2 / sampling_rate
        
        # 提取窗口数据
        window_signal = data_dict['Ia'][start_idx:end_idx]
        
        # 使用预计算THD
        features_precomputed = extract_improved_frequency_features(
            window_signal, sampling_rate, config, 
            window_center_time=window_center_time, 
            use_precomputed_thd=True
        )
        
        # 重新计算THD
        features_calculated = extract_improved_frequency_features(
            window_signal, sampling_rate, config, 
            window_center_time=window_center_time, 
            use_precomputed_thd=False
        )
        
        # 对比结果
        thd_precomputed = features_precomputed.get('ia_thd_percent', 0)
        thd_calculated = features_calculated.get('ia_thd_percent', 0)
        
        difference = abs(thd_precomputed - thd_calculated)
        relative_error = (difference / max(thd_calculated, 0.001)) * 100
        
        comparison_results.append({
            'time': window_center_time,
            'precomputed': thd_precomputed,
            'calculated': thd_calculated,
            'difference': difference,
            'relative_error': relative_error
        })
        
        print(f"  窗口 {i+1}: 时间={window_center_time:.3f}s, "
              f"预计算={thd_precomputed:.3f}%, 重计算={thd_calculated:.3f}%, "
              f"差异={difference:.3f}%, 相对误差={relative_error:.1f}%")
    
    # 统计分析
    differences = [r['difference'] for r in comparison_results]
    relative_errors = [r['relative_error'] for r in comparison_results]
    
    print(f"\n📈 统计分析:")
    print(f"  - 平均绝对差异: {np.mean(differences):.4f}%")
    print(f"  - 最大绝对差异: {np.max(differences):.4f}%")
    print(f"  - 平均相对误差: {np.mean(relative_errors):.2f}%")
    print(f"  - 最大相对误差: {np.max(relative_errors):.2f}%")
    
    # 判断一致性
    if np.mean(differences) < 0.1:  # 平均差异小于0.1%
        print(f"✅ THD计算结果高度一致，建议使用预计算THD数据")
    elif np.mean(differences) < 0.5:  # 平均差异小于0.5%
        print(f"⚠️ THD计算结果基本一致，可以使用预计算THD数据")
    else:
        print(f"❌ THD计算结果差异较大，建议检查预计算THD数据的准确性")
    
    return comparison_results

# 执行THD验证（如果有预计算数据）
if precomputed_thd is not None and data_dict is not None:
    thd_comparison = validate_thd_comparison(data_dict, time_vector, sampling_rate, config)
else:
    print("⚠️ 跳过THD验证，缺少必要数据")
    thd_comparison = None

# 改进的分窗分析和特征提取
def perform_improved_windowed_analysis(data_dict, sampling_rate, config):
    """执行改进的分窗分析"""
    if data_dict is None:
        print("❌ 无数据进行分析")
        return None
    
    print("\n🔍 开始改进的分窗分析...")
    
    # 计算窗口参数
    window_size = int(sampling_rate * config.CYCLES_PER_WINDOW / config.GRID_FREQ)
    step_size = int(window_size * (1 - config.WINDOW_OVERLAP))
    num_windows = (len(data_dict['Ia']) - window_size) // step_size + 1
    
    print(f"📊 分窗参数:")
    print(f"   - 窗口大小: {window_size} 点 ({window_size/sampling_rate*1000:.1f} ms)")
    print(f"   - 步长: {step_size} 点")
    print(f"   - 总窗口数: {num_windows}")
    
    # 提取特征
    all_features = []
    
    for i in range(num_windows):
        start_idx = i * step_size
        end_idx = start_idx + window_size
        window_center_time = (start_idx + end_idx) / 2 / sampling_rate
        
        # 当前窗口数据
        window_data = {}
        for key, data in data_dict.items():
            window_data[key] = data[start_idx:end_idx]
        
        # 提取A相电流特征（使用改进的函数）
        features = {'window_time': window_center_time}
        
        # 时域特征
        time_features = extract_basic_features(window_data['Ia'])
        for key, value in time_features.items():
            features[f'ia_{key}'] = value
        
        # 改进的频域特征（包含修正的THD计算或预计算THD）
                # 使用已计算的window_center_time
        freq_features = extract_improved_frequency_features(
            window_data['Ia'], 
            sampling_rate, 
            config, 
            window_center_time=window_center_time,
            use_precomputed_thd=True
        )
        for key, value in freq_features.items():
            features[f'ia_{key}'] = value
        
        # 小波特征
        wavelet_features = extract_wavelet_features(window_data['Ia'], config)
        for key, value in wavelet_features.items():
            features[f'ia_{key}'] = value
        
        # 直流电压特征
        dc_features = extract_basic_features(window_data['Vdc'])
        for key, value in dc_features.items():
            features[f'vdc_{key}'] = value
        
        # 功率特征
        p_inst = (window_data['Va'] * window_data['Ia'] + 
                  window_data['Vb'] * window_data['Ib'] + 
                  window_data['Vc'] * window_data['Ic'])
        features['active_power'] = np.mean(p_inst)
        features['power_std'] = np.std(p_inst)
        
        # 三相不平衡度
        ia_rms = np.sqrt(np.mean(window_data['Ia']**2))
        ib_rms = np.sqrt(np.mean(window_data['Ib']**2))
        ic_rms = np.sqrt(np.mean(window_data['Ic']**2))
        avg_rms = (ia_rms + ib_rms + ic_rms) / 3
        if avg_rms > 0:
            features['current_unbalance'] = np.std([ia_rms, ib_rms, ic_rms]) / avg_rms
        else:
            features['current_unbalance'] = 0
        
        all_features.append(features)
        
        if (i + 1) % 20 == 0:
            print(f"   已处理 {i+1}/{num_windows} 个窗口")
    
    # 转换为DataFrame
    feature_df = pd.DataFrame(all_features)
    
    # 添加故障标签
    feature_df['fault_label'] = (feature_df['window_time'] >= config.FAULT_TIME).astype(int)
    
    print(f"✅ 改进的特征提取完成!")
    print(f"   - 特征数: {len(feature_df.columns)}")
    print(f"   - 数据维度: {feature_df.shape}")
    
    return feature_df

# 执行改进的分窗分析
feature_df = perform_improved_windowed_analysis(data_dict, sampling_rate, config)

if feature_df is not None:
    print(f"\n📊 改进特征数据预览:")
    print(feature_df.head())
    print(f"\n故障前后数据分布:")
    print(feature_df['fault_label'].value_counts())
    
    # 保存特征数据到指定文件夹
    feature_df.to_csv(f'{config.DATA_DIR}/improved_features.csv', index=False)
    print(f"✅ 特征数据已保存到: {config.DATA_DIR}/improved_features.csv")

# 改进的故障影响分析
def calculate_improved_fault_impact(feature_df, config):
    """计算改进的故障影响向量和量化指标"""
    if feature_df is None:
        print("❌ 无特征数据进行分析")
        return None, None
    
    print("\n📈 计算改进的故障影响向量...")
    
    # 分离故障前后数据
    pre_fault_data = feature_df[feature_df['fault_label'] == 0]
    post_fault_data = feature_df[feature_df['fault_label'] == 1]
    
    if len(pre_fault_data) == 0 or len(post_fault_data) == 0:
        print("❌ 故障前后数据不足")
        return None, None
    
    # 选择稳态窗口
    pre_fault_stable = pre_fault_data.tail(config.PRE_FAULT_WINDOWS)
    post_fault_stable = post_fault_data.iloc[config.POST_FAULT_OFFSET:config.POST_FAULT_OFFSET+10]
    
    print(f"📊 分析窗口:")
    print(f"   - 故障前稳态: {len(pre_fault_stable)} 个窗口")
    print(f"   - 故障后稳态: {len(post_fault_stable)} 个窗口")
    
    # 选择数值特征
    numeric_cols = feature_df.select_dtypes(include=[np.number]).columns
    exclude_cols = ['window_time', 'fault_label']
    feature_cols = [col for col in numeric_cols if col not in exclude_cols]
    
    # 计算平均值
    pre_fault_mean = pre_fault_stable[feature_cols].mean()
    post_fault_mean = post_fault_stable[feature_cols].mean()
    
    # 计算影响向量
    absolute_impact = post_fault_mean - pre_fault_mean
    relative_impact = (post_fault_mean - pre_fault_mean) / (pre_fault_mean + 1e-10) * 100
    
    # 创建影响分析结果
    impact_analysis = pd.DataFrame({
        'Pre_Fault': pre_fault_mean,
        'Post_Fault': post_fault_mean,
        'Absolute_Change': absolute_impact,
        'Relative_Change_Percent': relative_impact,
        'Impact_Magnitude': np.abs(relative_impact)
    })
    
    # 按影响程度排序
    impact_analysis = impact_analysis.sort_values('Impact_Magnitude', ascending=False)
    
    # 计算综合故障影响因子
    quantifier = FaultImpactQuantifier(config)
    
    # 准备特征字典
    pre_features = pre_fault_mean.to_dict()
    post_features = post_fault_mean.to_dict()
    
    # 计算综合影响因子
    impact_factor_result = quantifier.calculate_comprehensive_impact_factor(pre_features, post_features)
    
    print(f"\n🎯 故障影响最显著的前10个特征:")
    print(impact_analysis.head(10)[['Relative_Change_Percent', 'Absolute_Change']].round(4))
    
    print(f"\n📊 综合故障影响评估:")
    print(f"   - 综合影响因子: {impact_factor_result['comprehensive_impact_factor']:.4f}")
    print(f"   - 影响严重程度: {impact_factor_result['severity_level_cn']} ({impact_factor_result['severity_level']})")
    print(f"   - THD影响分量: {impact_factor_result['component_impacts']['thd_impact']:.4f}")
    print(f"   - 功率影响分量: {impact_factor_result['component_impacts']['power_impact']:.4f}")
    print(f"   - 电压影响分量: {impact_factor_result['component_impacts']['voltage_impact']:.4f}")
    
    return impact_analysis, impact_factor_result

# 计算改进的故障影响
impact_analysis, impact_factor_result = calculate_improved_fault_impact(feature_df, config)

if impact_analysis is not None:
    print("\n✅ 改进的故障影响分析完成！")
    
    # 保存影响分析结果
    impact_analysis.to_csv(f'{config.DATA_DIR}/improved_impact_analysis.csv')
    
    # 保存综合影响因子结果
    impact_factor_df = pd.DataFrame([impact_factor_result])
    impact_factor_df.to_csv(f'{config.DATA_DIR}/comprehensive_impact_factor.csv', index=False)
    
    print(f"✅ 影响分析结果已保存到: {config.DATA_DIR}/")

# 改进的故障影响可视化
def plot_improved_fault_impact(feature_df, impact_analysis, impact_factor_result, config):
    """绘制改进的故障影响分析图"""
    if feature_df is None or impact_analysis is None:
        print("❌ 无数据可绘制")
        return
    
    # 图1: 关键特征时间序列分析
    fig1, axes1 = plt.subplots(2, 2, figsize=(16, 12))
    fig1.suptitle('关键电能质量特征时间序列分析', fontsize=16, fontweight='bold')
    
    # THD变化
    if 'ia_thd_percent' in feature_df.columns:
        axes1[0, 0].plot(feature_df['window_time'], feature_df['ia_thd_percent'], 
                        color='red', linewidth=2, label='THD (%)')
        axes1[0, 0].axhline(config.THD_LIMIT*100, color='orange', linestyle='--', linewidth=2, label='国标限值(5%)')
    else:
        axes1[0, 0].plot(feature_df['window_time'], feature_df['ia_thd']*100, 
                        color='red', linewidth=2, label='THD (%)')
        axes1[0, 0].axhline(config.THD_LIMIT*100, color='orange', linestyle='--', linewidth=2, label='国标限值(5%)')
    
    axes1[0, 0].axvline(config.FAULT_TIME, color='black', linestyle=':', linewidth=3, label='故障时刻')
    axes1[0, 0].set_title('总谐波畸变率(THD)变化')
    axes1[0, 0].set_ylabel('THD (%)')
    axes1[0, 0].legend()
    axes1[0, 0].grid(True, alpha=0.3)
    
    # 有功功率变化
    power_data = feature_df['active_power'] / 1000  # 转换为kW
    axes1[0, 1].plot(feature_df['window_time'], power_data, 
                    color='green', linewidth=2, label='有功功率')
    axes1[0, 1].axvline(config.FAULT_TIME, color='black', linestyle=':', linewidth=3, label='故障时刻')
    
    # 功率损失标注
    pre_power = feature_df[feature_df['fault_label'] == 0]['active_power'].mean() / 1000
    post_power = feature_df[feature_df['fault_label'] == 1]['active_power'].mean() / 1000
    power_loss = pre_power - post_power
    
    axes1[0, 1].text(0.02, 0.98, f'功率损失: {power_loss:.1f} kW\n损失率: {power_loss/pre_power*100:.1f}%', 
                    transform=axes1[0, 1].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    axes1[0, 1].set_title('有功功率变化')
    axes1[0, 1].set_ylabel('功率 (kW)')
    axes1[0, 1].legend()
    axes1[0, 1].grid(True, alpha=0.3)
    
    # 电流RMS变化
    axes1[1, 0].plot(feature_df['window_time'], feature_df['ia_rms'], 
                    color='blue', linewidth=2, label='A相电流RMS')
    axes1[1, 0].axvline(config.FAULT_TIME, color='black', linestyle=':', linewidth=3, label='故障时刻')
    axes1[1, 0].set_title('A相电流RMS变化')
    axes1[1, 0].set_ylabel('电流 (A)')
    axes1[1, 0].set_xlabel('时间 (s)')
    axes1[1, 0].legend()
    axes1[1, 0].grid(True, alpha=0.3)
    
    # 直流电压变化
    axes1[1, 1].plot(feature_df['window_time'], feature_df['vdc_rms'], 
                    color='purple', linewidth=2, label='直流母线电压')
    axes1[1, 1].axvline(config.FAULT_TIME, color='black', linestyle=':', linewidth=3, label='故障时刻')
    axes1[1, 1].set_title('直流母线电压变化')
    axes1[1, 1].set_ylabel('电压 (V)')
    axes1[1, 1].set_xlabel('时间 (s)')
    axes1[1, 1].legend()
    axes1[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    if config.SAVE_PLOTS:
        plt.savefig(f'{config.PLOTS_DIR}/improved_fault_impact_timeseries.png', dpi=config.DPI, bbox_inches='tight')
    plt.show()
    
    # 图2: 综合影响因子可视化
    fig2, axes2 = plt.subplots(2, 2, figsize=(16, 12))
    fig2.suptitle('综合故障影响因子分析', fontsize=16, fontweight='bold')
    
    # 影响程度排序
    top_impacts = impact_analysis.head(12)
    y_pos = np.arange(len(top_impacts))
    
    bars = axes2[0, 0].barh(y_pos, top_impacts['Relative_Change_Percent'], 
                          color=['red' if x > 0 else 'blue' for x in top_impacts['Relative_Change_Percent']])
    axes2[0, 0].set_yticks(y_pos)
    axes2[0, 0].set_yticklabels(top_impacts.index, fontsize=8)
    axes2[0, 0].set_title('故障影响程度排序 (前12项)')
    axes2[0, 0].set_xlabel('相对变化 (%)')
    axes2[0, 0].grid(True, alpha=0.3)
    
    # 综合影响因子分量
    components = impact_factor_result['component_impacts']
    comp_names = ['THD影响', '功率影响', '电压影响', '频率影响', '谐波影响']
    comp_values = [components['thd_impact'], components['power_impact'], 
                  components['voltage_impact'], components['frequency_impact'], 
                  components['harmonic_impact']]
    
    colors_pie = ['red', 'green', 'blue', 'orange', 'purple']
    axes2[0, 1].pie(comp_values, labels=comp_names, colors=colors_pie, autopct='%1.1f%%', startangle=90)
    axes2[0, 1].set_title('综合影响因子分量分布')
    
    # 严重程度指示器
    severity_levels = ['轻微', '中等', '严重']
    severity_values = [config.SEVERITY_THRESHOLDS['mild'], 
                      config.SEVERITY_THRESHOLDS['moderate'], 
                      config.SEVERITY_THRESHOLDS['severe']]
    current_impact = impact_factor_result['comprehensive_impact_factor']
    
    bars = axes2[1, 0].bar(severity_levels, severity_values, 
                          color=['green', 'yellow', 'red'], alpha=0.7)
    axes2[1, 0].axhline(current_impact, color='black', linestyle='--', linewidth=3, 
                       label=f'当前影响: {current_impact:.3f}')
    axes2[1, 0].set_title('故障严重程度评估')
    axes2[1, 0].set_ylabel('影响因子')
    axes2[1, 0].legend()
    axes2[1, 0].grid(True, alpha=0.3)
    
    # 影响因子数学公式展示
    formula_text = f"""
综合影响因子计算公式:
CIF = w₁×THD + w₂×Power + w₃×Voltage + w₄×Freq + w₅×Harmonic

权重设置:
w₁(THD) = {config.IMPACT_WEIGHTS['thd_weight']}
w₂(功率) = {config.IMPACT_WEIGHTS['power_weight']}
w₃(电压) = {config.IMPACT_WEIGHTS['voltage_weight']}
w₄(频率) = {config.IMPACT_WEIGHTS['frequency_weight']}
w₅(谐波) = {config.IMPACT_WEIGHTS['harmonic_weight']}

当前计算结果:
CIF = {current_impact:.4f}
严重程度: {impact_factor_result['severity_level_cn']}
"""
    
    axes2[1, 1].text(0.05, 0.95, formula_text, transform=axes2[1, 1].transAxes, 
                     verticalalignment='top', fontsize=10, 
                     bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    axes2[1, 1].set_xlim(0, 1)
    axes2[1, 1].set_ylim(0, 1)
    axes2[1, 1].set_title('影响因子计算公式')
    axes2[1, 1].axis('off')
    
    plt.tight_layout()
    if config.SAVE_PLOTS:
        plt.savefig(f'{config.PLOTS_DIR}/comprehensive_impact_factor_analysis.png', dpi=config.DPI, bbox_inches='tight')
    plt.show()
    
    print("✅ 改进的故障影响分析图绘制完成")

# 绘制改进的故障影响图
plot_improved_fault_impact(feature_df, impact_analysis, impact_factor_result, config)

# 改进的报告生成
def generate_improved_analysis_report(feature_df, impact_analysis, impact_factor_result, config):
    """生成改进的分析报告"""
    print("\n📋 生成改进的分析报告...")
    
    if feature_df is None:
        print("❌ 无数据生成报告")
        return
    
    # 计算关键指标
    pre_fault_data = feature_df[feature_df['fault_label'] == 0]
    post_fault_data = feature_df[feature_df['fault_label'] == 1]
    
    # THD变化分析
    thd_column = 'ia_thd_percent' if 'ia_thd_percent' in feature_df.columns else 'ia_thd'
    thd_multiplier = 1 if 'ia_thd_percent' in feature_df.columns else 100
    
    # 检查THD数据来源
    thd_source_column = 'ia_thd_source'
    thd_data_source = 'unknown'
    if thd_source_column in feature_df.columns:
        source_counts = feature_df[thd_source_column].value_counts()
        thd_data_source = source_counts.index[0] if len(source_counts) > 0 else 'unknown'
    
    pre_thd = pre_fault_data[thd_column].mean() * thd_multiplier
    post_thd = post_fault_data[thd_column].mean() * thd_multiplier
    thd_change = post_thd - pre_thd
    thd_change_percent = (thd_change / pre_thd) * 100 if pre_thd > 0 else 0
    
    # 功率变化分析
    pre_power = pre_fault_data['active_power'].mean() / 1000  # kW
    post_power = post_fault_data['active_power'].mean() / 1000  # kW
    power_loss = pre_power - post_power
    power_loss_percent = (power_loss / pre_power) * 100 if pre_power > 0 else 0
    
    # 电流变化分析
    pre_current = pre_fault_data['ia_rms'].mean()
    post_current = post_fault_data['ia_rms'].mean()
    current_change = post_current - pre_current
    current_change_percent = (current_change / pre_current) * 100 if pre_current > 0 else 0
    
    # 电压变化分析
    pre_voltage = pre_fault_data['vdc_rms'].mean()
    post_voltage = post_fault_data['vdc_rms'].mean()
    voltage_change = post_voltage - pre_voltage
    voltage_change_percent = (voltage_change / pre_voltage) * 100 if pre_voltage > 0 else 0
    
    # 获取综合影响因子信息
    cif = impact_factor_result['comprehensive_impact_factor']
    severity = impact_factor_result['severity_level_cn']
    component_impacts = impact_factor_result['component_impacts']
    
    # 生成详细报告
    report_template = """
# 光伏断路故障电能质量影响分析报告 - 改进版

## 执行摘要
本报告基于改进的分析方法，对400KW并网光伏发电系统中PV Array4断路故障进行了全面的电能质量影响评估。
通过多维特征提取、THD计算修正、可视化改进和综合影响因子量化，得出了准确可靠的分析结果。

## 系统配置
- **系统容量**: {:.0f} kW
- **故障时间**: {:.1f} 秒
- **分析时长**: {:.1f} 秒
- **分析窗口数**: {}
- **提取特征数**: {}
- **采样频率**: {:.2f} Hz

## 关键发现

### 1. 电能质量影响分析

#### 总谐波畸变率(THD)变化
- **故障前THD**: {:.3f}%
- **故障后THD**: {:.3f}%
- **THD变化量**: {:.3f}% (相对变化: {:.1f}%)
- **国标限值**: 5.0%
- **是否超标**: {}
- **数据来源**: {}

#### 有功功率变化
- **故障前功率**: {:.1f} kW
- **故障后功率**: {:.1f} kW
- **功率损失**: {:.1f} kW (损失率: {:.1f}%)
- **相对额定功率损失**: {:.1f}%

#### 电流变化
- **故障前电流**: {:.2f} A
- **故障后电流**: {:.2f} A
- **电流变化**: {:.2f} A (相对变化: {:.1f}%)

#### 直流母线电压变化
- **故障前电压**: {:.1f} V
- **故障后电压**: {:.1f} V
- **电压变化**: {:.1f} V (相对变化: {:.1f}%)

### 2. 综合故障影响因子(CIF)评估

#### 综合影响因子计算
**数学公式**:
```
CIF = w₁×THD_impact + w₂×Power_impact + w₃×Voltage_impact + w₄×Freq_impact + w₅×Harmonic_impact
```

**权重配置**:
- THD权重(w₁): {:.2f}
- 功率权重(w₂): {:.2f}
- 电压权重(w₃): {:.2f}
- 频率权重(w₄): {:.2f}
- 谐波权重(w₅): {:.2f}

**分量影响值**:
- THD影响分量: {:.4f}
- 功率影响分量: {:.4f}
- 电压影响分量: {:.4f}
- 频率影响分量: {:.4f}
- 谐波影响分量: {:.4f}

**最终结果**:
- **综合影响因子**: {:.4f}
- **影响严重程度**: {} ({})

#### 严重程度分级标准
- **轻微影响**: CIF ≤ {:.2f}
- **中等影响**: {:.2f} < CIF ≤ {:.2f}
- **严重影响**: CIF > {:.2f}

### 3. 最显著影响特征(前5项)
{}

## 技术改进说明

### 1. 文件组织优化
- 建立了规范的输出文件夹结构
- 分类存储图表、数据和报告文件
- 便于结果管理和后续分析

### 2. THD计算逻辑修正
- 应用汉宁窗减少频谱泄漏
- 精确的基波和谐波峰值检测
- 修正THD计算公式，确保物理意义正确
- 验证故障后THD增加的合理性

### 3. 数据可视化改进
- 拆分原有概览图为多个独立图表
- 解决三相电压曲线重合问题
- 使用不同线型、透明度和颜色区分
- 添加详细的统计信息标注

### 4. 故障影响量化指标
- 设计综合故障影响因子(CIF)
- 多维度影响评估(THD、功率、电压、频率、谐波)
- 建立影响严重程度分级标准
- 提供量化的故障影响评估

## MPC补偿建议

### 控制策略优先级
1. **THD补偿** - 权重最高({:.1f}%)，重点关注谐波抑制
2. **功率调节** - 权重{:.1f}%，补偿功率损失
3. **电压支撑** - 权重{:.1f}%，维持电压稳定
4. **频率调节** - 权重{:.1f}%，保持频率稳定
5. **谐波滤波** - 权重{:.1f}%，抑制特定次谐波

### 具体控制参数建议
- **预测时域**: 20步
- **控制时域**: 5步
- **采样时间**: 1ms
- **补偿延迟**: 16.67ms (60Hz系统一个周期)

## 结论与建议

### 主要结论
1. PV Array4断路故障对电网电能质量产生{}影响
2. THD变化是最主要的影响因素，需要重点关注
3. 功率损失达到{:.1f}kW，占总容量的{:.1f}%
4. 综合影响因子为{:.4f}，属于{}影响级别

### 技术建议
1. **实施主动谐波补偿**，重点抑制3次、5次谐波(180Hz、300Hz)
2. **调整剩余阵列功率输出**，补偿功率损失
3. **优化直流侧电压控制**，减少电压波动
4. **采用MPC预测控制**，实现故障的主动补偿
5. **建立实时监测系统**，及时发现和处理故障

### 后续工作
1. 基于提取特征设计MPC控制器
2. 验证补偿效果和控制性能
3. 扩展到其他类型光伏故障分析
4. 开发实时故障检测和补偿系统

---
**报告生成时间**: {}
**分析软件**: 光伏故障电能质量影响分析系统 v2.0
**技术支持**: 改进版特征提取与MPC补偿算法
"""
    
    # 准备报告数据
    thd_exceed_status = "是" if post_thd > 5.0 else "否"
    
    # 最显著影响特征
    top_features_text = ""
    for i, (feature, row) in enumerate(impact_analysis.head(5).iterrows(), 1):
        top_features_text += f"{i}. {feature}: 相对变化 {row['Relative_Change_Percent']:.2f}%\n"
    
    # 当前时间
    from datetime import datetime
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 格式化报告
    report = report_template.format(
        config.RATED_POWER/1000,  # 系统容量
        config.FAULT_TIME,        # 故障时间
        config.SIM_TIME,          # 分析时长
        len(feature_df),          # 分析窗口数
        len(feature_df.columns)-2, # 提取特征数
        len(feature_df) / config.SIM_TIME,  # 采样频率
        pre_thd, post_thd, thd_change, thd_change_percent,  # THD相关
        thd_exceed_status,
        '预计算数据' if thd_data_source == 'precomputed' else 'FFT重新计算',  # THD数据来源
        pre_power, post_power, power_loss, power_loss_percent,  # 功率相关
        power_loss / (config.RATED_POWER/1000) * 100,  # 相对额定功率损失
        pre_current, post_current, current_change, current_change_percent,  # 电流相关
        pre_voltage, post_voltage, voltage_change, voltage_change_percent,  # 电压相关
        config.IMPACT_WEIGHTS['thd_weight'],     # 权重
        config.IMPACT_WEIGHTS['power_weight'],
        config.IMPACT_WEIGHTS['voltage_weight'],
        config.IMPACT_WEIGHTS['frequency_weight'],
        config.IMPACT_WEIGHTS['harmonic_weight'],
        component_impacts['thd_impact'],         # 分量影响
        component_impacts['power_impact'],
        component_impacts['voltage_impact'],
        component_impacts['frequency_impact'],
        component_impacts['harmonic_impact'],
        cif, severity, impact_factor_result['severity_level'],  # 综合结果
        config.SEVERITY_THRESHOLDS['mild'],      # 分级标准
        config.SEVERITY_THRESHOLDS['mild'],
        config.SEVERITY_THRESHOLDS['moderate'],
        config.SEVERITY_THRESHOLDS['moderate'],
        top_features_text,                       # 最显著特征
        config.IMPACT_WEIGHTS['thd_weight']*100,     # MPC建议权重
        config.IMPACT_WEIGHTS['power_weight']*100,
        config.IMPACT_WEIGHTS['voltage_weight']*100,
        config.IMPACT_WEIGHTS['frequency_weight']*100,
        config.IMPACT_WEIGHTS['harmonic_weight']*100,
        severity,                                # 结论
        power_loss,
        power_loss_percent,
        cif,
        severity,
        current_time                             # 生成时间
    )
    
    # 保存报告
    report_path = f'{config.REPORTS_DIR}/improved_analysis_report.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"   ✅ 改进分析报告已生成: {report_path}")
    
    # 打印关键摘要
    print(f"\n📊 关键结果摘要:")
    print(f"   - THD变化: {pre_thd:.3f}% → {post_thd:.3f}% (变化{thd_change:+.3f}%)")
    print(f"   - 功率损失: {power_loss:.1f} kW ({power_loss_percent:.1f}%)")
    print(f"   - 综合影响因子: {cif:.4f} ({severity})")
    print(f"   - 分析窗口: {len(feature_df)} 个")
    print(f"   - 提取特征: {len(feature_df.columns)-2} 个")

# 生成改进的分析报告
generate_improved_analysis_report(feature_df, impact_analysis, impact_factor_result, config)

print("\n🎉 改进版光伏故障分析完成！")
print(f"\n📁 所有结果文件已保存到:")
print(f"   - 图表文件: {config.PLOTS_DIR}/")
print(f"   - 数据文件: {config.DATA_DIR}/")
print(f"   - 报告文件: {config.REPORTS_DIR}/")
print("\n💡 四项改进已全部实现:")
print("   ✅ 1. 文件组织优化 - 统一输出文件夹管理")
print("   ✅ 2. THD计算修正 - 修正计算逻辑，确保物理意义正确")
print("   ✅ 3. 可视化改进 - 拆分图表，解决重合问题")
print("   ✅ 4. 故障影响量化 - 设计综合影响因子和分级标准")

