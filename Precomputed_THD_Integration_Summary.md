# 预计算THD数据集成修改总结

## 🎯 修改目标

根据用户要求，检查光伏故障数据文件 `pv_fault_data.mat` 中是否包含预计算的THD数据，并修改代码以优先使用预计算THD数据，同时保持其他特征提取功能不变。

## 📋 主要修改内容

### 1. 数据探索功能 (`explore_mat_file`)

**位置**: 第240-281行
**功能**: 
- 自动探索.mat文件中的所有变量
- 识别THD相关变量（包含'thd', 'harmonic', 'distortion', 'quality'关键词）
- 显示变量的形状、类型和是否为THD相关

**代码示例**:
```python
def explore_mat_file(file_path):
    """探索.mat文件中的所有变量，寻找THD相关数据"""
    # 检查是否为THD相关变量
    is_thd_related = any(thd_keyword in var_name.lower() for thd_keyword in 
                       ['thd', 'harmonic', 'distortion', 'quality'])
```

### 2. 预计算THD数据处理函数

**位置**: 第587-649行
**包含函数**:
- `extract_thd_at_time()`: 从预计算THD数据中提取指定时间的THD值
- `get_precomputed_thd_data()`: 获取预计算的THD数据，按优先级选择变量

**优先级顺序**:
1. `THD`
2. `thd` 
3. `THD_A`
4. `ia_thd`
5. `THD_Ia`
6. `current_thd`

### 3. 改进的频域特征提取函数

**位置**: 第476-596行
**主要修改**:
- 添加 `window_center_time` 和 `use_precomputed_thd` 参数
- 支持预计算THD数据的优先使用
- 在使用预计算THD时跳过FFT计算
- 添加THD数据来源标识 (`thd_source`)

**关键逻辑**:
```python
def extract_improved_frequency_features(signal_data, fs, config, window_center_time=None, use_precomputed_thd=True):
    # 检查是否使用预计算THD
    thd_from_precomputed = False
    if (use_precomputed_thd and precomputed_thd is not None and 
        window_center_time is not None and len(thd_vars) > 0):
        # 从预计算数据中获取THD值
        thd_value = extract_thd_at_time(precomputed_thd, time_vector, window_center_time)
        if thd_value is not None:
            features['thd_source'] = 'precomputed'
            thd_from_precomputed = True
    
    # 如果没有使用预计算THD，则进行FFT计算
    if not thd_from_precomputed:
        # 进行FFT分析和THD计算
        features['thd_source'] = 'calculated'
```

### 4. THD验证和对比功能

**位置**: 第667-751行
**功能**:
- 随机选择测试窗口对比预计算THD与重新计算THD
- 计算绝对差异和相对误差
- 提供一致性评估建议

**评估标准**:
- 平均差异 < 0.1%: 高度一致，建议使用预计算THD
- 平均差异 < 0.5%: 基本一致，可以使用预计算THD  
- 平均差异 ≥ 0.5%: 差异较大，建议检查预计算THD准确性

### 5. 特征提取主函数修改

**位置**: 第971-981行
**修改内容**:
- 在调用 `extract_improved_frequency_features` 时传递窗口中心时间
- 启用预计算THD使用选项

```python
freq_features = extract_improved_frequency_features(
    window_data['Ia'], 
    sampling_rate, 
    config, 
    window_center_time=window_center_time,
    use_precomputed_thd=True
)
```

### 6. 报告生成增强

**位置**: 第1501-1513行, 第1565-1566行, 第1706-1707行
**修改内容**:
- 检测THD数据来源并在报告中标注
- 在THD分析部分添加"数据来源"字段
- 在报告格式化时包含数据来源信息

## 🔍 使用流程

### 1. 自动检测阶段
```python
# 探索数据文件
mat_data, thd_vars = explore_mat_file(config.DATA_FILE)

# 获取预计算THD数据
precomputed_thd = get_precomputed_thd_data()
```

### 2. 特征提取阶段
```python
# 优先使用预计算THD
freq_features = extract_improved_frequency_features(
    signal_data, sampling_rate, config,
    window_center_time=window_center_time,
    use_precomputed_thd=True
)
```

### 3. 验证阶段（可选）
```python
# 对比预计算THD与重新计算THD
thd_comparison = validate_thd_comparison(data_dict, time_vector, sampling_rate, config)
```

## 📊 预期输出

### 控制台输出示例
```
🔍 探索.mat文件内容...
📂 文件: pv_fault_data.mat
📊 变量总数: 8
📋 用户变量列表:
  🎯 THD: (10001,) (ndarray) [THD相关]
  📄 Ia: (10001,) (ndarray)
  📄 tout: (10001,) (ndarray)
✅ 发现 1 个THD相关变量: ['THD']

📊 使用预计算THD数据: THD
   - 数据长度: 10001
   - 数值范围: [2.1234, 4.5678]
🎯 将在特征提取中优先使用预计算THD数据
```

### 特征提取输出示例
```
📊 使用预计算THD: 3.456% (时间: 0.850s)
```

### 验证输出示例
```
🔍 THD计算方法验证和对比...
📊 对比 10 个测试窗口:
  窗口 1: 时间=0.123s, 预计算=3.456%, 重计算=3.445%, 差异=0.011%, 相对误差=0.3%
  ...
📈 统计分析:
  - 平均绝对差异: 0.0234%
  - 最大绝对差异: 0.0567%
  - 平均相对误差: 0.68%
  - 最大相对误差: 1.64%
✅ THD计算结果高度一致，建议使用预计算THD数据
```

## 🎯 关键优势

### 1. 智能检测
- 自动识别.mat文件中的THD相关变量
- 按优先级选择最合适的THD数据源

### 2. 灵活切换
- 支持预计算THD和重新计算THD的无缝切换
- 保持其他特征提取功能完全不变

### 3. 质量保证
- 提供验证功能对比两种THD计算方法
- 在报告中明确标注THD数据来源

### 4. 性能优化
- 使用预计算THD时跳过FFT计算，提高处理速度
- 保持分析结果的准确性和可靠性

## 🔧 配置选项

### 启用/禁用预计算THD
```python
# 在特征提取时控制
freq_features = extract_improved_frequency_features(
    signal_data, sampling_rate, config,
    window_center_time=window_center_time,
    use_precomputed_thd=True  # 设为False强制重新计算
)
```

### THD变量优先级自定义
可以在 `get_precomputed_thd_data()` 函数中修改优先级列表：
```python
priority_names = ['THD', 'thd', 'THD_A', 'ia_thd', 'THD_Ia', 'current_thd']
```

## 📝 注意事项

1. **数据格式**: 预计算THD数据应与时间向量长度一致
2. **单位处理**: 自动识别THD数据是百分比形式还是小数形式
3. **时间对齐**: 通过最近邻插值确保时间对齐的准确性
4. **向后兼容**: 如果没有预计算THD数据，自动回退到FFT重新计算

## 🚀 下一步建议

1. **运行测试**: 使用实际的.mat文件测试修改后的代码
2. **验证结果**: 运行THD对比验证功能，确认数据一致性
3. **性能评估**: 对比使用预计算THD前后的处理速度
4. **报告审查**: 检查生成的分析报告中THD数据来源标注是否正确

这些修改确保了代码能够智能地检测和使用预计算THD数据，同时保持了原有功能的完整性和分析结果的准确性。
