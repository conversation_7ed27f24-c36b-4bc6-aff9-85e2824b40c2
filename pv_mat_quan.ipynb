{"cells": [{"cell_type": "markdown", "id": "title_cell", "metadata": {}, "source": ["# 光伏断路故障对电网电能质量影响特征提取\n", "\n", "## 项目概述\n", "本项目旨在分析400KW并网光伏发电系统中PV Array4断路故障对电网电能质量的影响，\n", "提取关键特征用于后续的主动电网支撑和MPC补偿算法开发。\n", "\n", "## 系统配置\n", "- 总容量：400KW (4×100KW光伏阵列)\n", "- 故障类型：PV Array4断路故障\n", "- 故障时间：1秒后断开\n", "- 分析目标：电网电流谐波畸变率及电能质量影响\n", "\n", "## 技术路线\n", "1. 数据预处理与分窗分析\n", "2. 多维特征提取（时域、频域、小波域）\n", "3. 故障影响向量计算\n", "4. 电能质量指标量化\n", "5. 为MPC补偿算法提供特征基础"]}, {"cell_type": "code", "execution_count": 3, "id": "f204d38d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 所有库导入成功！\n"]}], "source": ["# 导入必要的库\n", "import numpy as np\n", "import pandas as pd\n", "import scipy.io\n", "import pywt\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import signal\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.decomposition import PCA\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体和图表样式\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ 所有库导入成功！\")"]}, {"cell_type": "code", "execution_count": 4, "id": "54ceb37a", "metadata": {}, "outputs": [], "source": ["# -------------------------------------------------------------------\n", "# 1. 配置参数 (所有可调参数都在这里，方便修改)\n", "# -------------------------------------------------------------------\n", "class Config:\n", "    \"\"\"存放所有配置参数的类\"\"\"\n", "    # -- 文件与仿真设置 --\n", "    MAT_FILE_PATH = 'pv_fault_data.mat' # .mat文件路径\n", "    SIM_TIME = 3.0                      # 总仿真时间 (秒)\n", "    FAULT_TIME = 1.0                    # 故障发生时间 (秒)\n", "    GRID_FREQ = 50.0                    # 电网基波频率 (Hz)\n", "\n", "    # -- 窗口化设置 --\n", "    # 窗口大小建议为基波周期的整数倍，以保证FFT分析的准确性\n", "    # 1个周期 = 1/GRID_FREQ 秒\n", "    CYCLES_PER_WINDOW = 2                             # 每个窗口包含的工频周期数\n", "    WINDOW_OVERLAP = 0.5                              # 窗口重叠率 (0.5 = 50%)\n", "\n", "    # -- 特征提取设置 --\n", "    WAVELET_TYPE = 'db4' # 小波基函数类型\n", "    WAVELET_LEVEL = 4    # 小波分解层数\n", "    FFT_HARMONICS = [3, 5, 7, 11, 13, 15, 17, 19] # 需要分析的谐波次数\n", "\n", "    # -- 影响向量分析设置 --\n", "    POST_FAULT_STABLE_OFFSET = 5 # 为避开暂态，从故障点向后偏移的窗口数"]}, {"cell_type": "code", "execution_count": 5, "id": "81b10c7c", "metadata": {}, "outputs": [], "source": ["# -------------------------------------------------------------------\n", "# 2. 数据加载与准备\n", "# -------------------------------------------------------------------\n", "def load_and_prepare_data(config):\n", "    \"\"\"\n", "    从.mat文件加载数据并进行初步处理。\n", "    返回一个包含所有时间序列数据的字典和计算出的采样率。\n", "    \"\"\"\n", "    print(\"--- 步骤1: 加载和准备数据 ---\")\n", "    try:\n", "        mat_data = scipy.io.loadmat(config.MAT_FILE_PATH)\n", "        print(f\"✅ 文件 '{config.MAT_FILE_PATH}' 加载成功!\")\n", "        print(f\"   文件中包含的变量: {list(mat_data.keys())}\")\n", "    except FileNotFoundError:\n", "        print(f\"❌ 错误: 未找到文件 '{config.MAT_FILE_PATH}'。\")\n", "        return None, None\n", "\n", "    # 定义需要从.mat文件中提取的变量名\n", "    required_vars = ['Ia', 'Ib', 'Ic', 'Va', 'Vb', 'Vc', 'Vdc']\n", "    \n", "    # 将数据存入字典，并使用.flatten()确保其为一维数组\n", "    data_dict = {}\n", "    for var in required_vars:\n", "        if var in mat_data:\n", "            data_dict[var] = mat_data[var].flatten()\n", "        else:\n", "            print(f\"⚠️ 警告: 在.mat文件中未找到变量 '{var}'，将跳过。\")\n", "            \n", "    # 检查是否加载了最关键的数据\n", "    if 'Ia' not in data_dict:\n", "        print(\"❌ 错误: 缺少关键数据 'Ia'，程序无法继续。\")\n", "        return None, None\n", "\n", "    # 计算采样率\n", "    num_samples = len(data_dict['Ia'])\n", "    sampling_rate = num_samples / config.SIM_TIME\n", "    print(f\"   - 仿真参数: 总时长={config.SIM_TIME}s, 采样点={num_samples}, 采样率={sampling_rate:.2f}Hz\")\n", "    \n", "    return data_dict, sampling_rate"]}, {"cell_type": "code", "execution_count": 6, "id": "92c65d74", "metadata": {}, "outputs": [], "source": ["# -------------------------------------------------------------------\n", "# 3. 特征提取函数\n", "# -------------------------------------------------------------------\n", "def extract_all_features_for_window(window_data, fs, config):\n", "    \"\"\"\n", "    为一个窗口的数据提取所有指定的特征。\n", "    window_data: 包含该窗口所有信号切片的字典。\n", "    fs: 采样率。\n", "    config: 配置对象。\n", "    返回一个包含所有特征的字典。\n", "    \"\"\"\n", "    features = {}\n", "\n", "    # --- A. 三相电流时域特征 (以a相为例，可扩展) ---\n", "    ia_win = window_data['Ia']\n", "    features['ia_rms'] = np.sqrt(np.mean(ia_win**2))\n", "    features['ia_peak'] = np.max(np.abs(ia_win))\n", "    features['ia_std'] = np.std(ia_win)\n", "    if features['ia_rms'] != 0:\n", "        features['ia_crest_factor'] = features['ia_peak'] / features['ia_rms']\n", "    else:\n", "        features['ia_crest_factor'] = 0\n", "\n", "    # --- B. 三相电流频域特征 (FFT, THD) ---\n", "    N = len(ia_win)\n", "    yf = np.fft.fft(ia_win)\n", "    yf_mag = 2.0/N * np.abs(yf[:N//2]) # 归一化处理\n", "    \n", "    fundamental_idx = int(config.GRID_FREQ * N / fs)\n", "    fundamental_mag = yf_mag[fundamental_idx]\n", "    \n", "    harmonics_sq_sum = 0\n", "    for h in config.FFT_HARMONICS:\n", "        harmonic_idx = int(h * config.GRID_FREQ * N / fs)\n", "        if harmonic_idx < len(yf_mag):\n", "            harmonic_mag = yf_mag[harmonic_idx]\n", "            harmonics_sq_sum += harmonic_mag**2\n", "            features[f'ia_h{h}_mag'] = harmonic_mag # 单次谐波幅值\n", "            \n", "    thd = np.sqrt(harmonics_sq_sum) / fundamental_mag if fundamental_mag != 0 else 0\n", "    features['ia_thd'] = thd\n", "    features['ia_fundamental_mag'] = fundamental_mag\n", "\n", "    # --- <PERSON><PERSON> 小波能量特征 ---\n", "    coeffs = pywt.wavedec(ia_win, config.WAVELET_TYPE, level=config.WAVELET_LEVEL)\n", "    for i in range(config.WAVELET_LEVEL):\n", "        features[f'ia_wavelet_energy_d{i+1}'] = np.sum(coeffs[i+1]**2)\n", "\n", "    # --- D. 直流侧特征 ---\n", "    vdc_win = window_data['Vdc']\n", "    features['dc_volt_mean'] = np.mean(vdc_win)\n", "    features['dc_volt_std'] = np.std(vdc_win) # 标准差反映了直流母线电压的波动/纹波情况\n", "\n", "    # --- E. 功率特征 (有功P, 无功Q) ---\n", "    # 使用瞬时功率理论计算\n", "    p_inst = (window_data['Va'] * window_data['Ia'] +\n", "              window_data['Vb'] * window_data['Ib'] +\n", "              window_data['Vc'] * window_data['Ic'])\n", "    features['active_power_P'] = np.mean(p_inst)\n", "    \n", "    # 无功功率计算需要相移，这里用一个简化估算（更精确的计算需要alpha-beta变换）\n", "    # p(t-T/4) 估算 q(t)\n", "    shift_samples = int(fs / (4 * config.GRID_FREQ)) # 90度相移对应的采样点\n", "    q_inst = (window_data['Va'] * np.roll(window_data['Ia'], -shift_samples) +\n", "              window_data['Vb'] * np.roll(window_data['Ib'], -shift_samples) +\n", "              window_data['Vc'] * np.roll(window_data['Ic'], -shift_samples))\n", "    features['reactive_power_Q'] = np.mean(q_inst)\n", "\n", "    return features"]}, {"cell_type": "code", "execution_count": 7, "id": "73e751e9", "metadata": {}, "outputs": [], "source": ["# -------------------------------------------------------------------\n", "# 4. 主处理流程\n", "# -------------------------------------------------------------------\n", "def main():\n", "    \"\"\"主函数，执行整个分析流程\"\"\"\n", "    # 加载配置\n", "    config = Config()\n", "    \n", "    # 加载数据\n", "    data_dict, sampling_rate = load_and_prepare_data(config)\n", "    if data_dict is None:\n", "        return\n", "\n", "    print(\"\\n--- 步骤2: 数据分窗 ---\")\n", "    # 计算窗口参数\n", "    window_size = int(sampling_rate * config.CYCLES_PER_WINDOW / config.GRID_FREQ)\n", "    step_size = int(window_size * (1 - config.WINDOW_OVERLAP))\n", "    print(f\"   - 窗口参数: 大小={window_size}点, 步长={step_size}点\")\n", "\n", "    # 对所有信号进行同步分窗\n", "    num_windows = (len(data_dict['Ia']) - window_size) // step_size + 1\n", "    print(f\"   - 将生成 {num_windows} 个分析窗口。\")\n", "\n", "    print(\"\\n--- 步骤3: 循环提取特征 ---\")\n", "    all_features_list = []\n", "    for i in range(num_windows):\n", "        start_idx = i * step_size\n", "        end_idx = start_idx + window_size\n", "        \n", "        # 为当前循环创建一个包含所有信号切片的字典\n", "        current_window_data = {key: data[start_idx:end_idx] for key, data in data_dict.items()}\n", "        \n", "        # 提取该窗口的特征\n", "        features = extract_all_features_for_window(current_window_data, sampling_rate, config)\n", "        all_features_list.append(features)\n", "    \n", "    # 转换为Pandas DataFrame\n", "    feature_df = pd.DataFrame(all_features_list)\n", "    print(\"✅ 特征提取完成，已生成DataFrame。\")\n", "\n", "    print(\"\\n--- 步骤4: 特征标注 ---\")\n", "    # 计算故障发生时对应的采样点和窗口\n", "    fault_sample_index = int(config.FAULT_TIME * sampling_rate)\n", "    fault_window_index = fault_sample_index // step_size\n", "    \n", "    # 创建标签列\n", "    feature_df['label'] = 0\n", "    feature_df.loc[fault_window_index:, 'label'] = 1\n", "    print(\"✅ 数据标注完成。\")\n", "\n", "    print(\"\\n--- 步骤5: 计算故障影响向量 ---\")\n", "    # 定位故障前后的稳定窗口\n", "    pre_fault_idx = fault_window_index - 1\n", "    post_fault_idx = fault_window_index + config.POST_FAULT_STABLE_OFFSET\n", "\n", "    if pre_fault_idx < 0 or post_fault_idx >= len(feature_df):\n", "        print(\"❌ 错误: 窗口数量不足，无法计算影响向量。\")\n", "        return\n", "\n", "    print(f\"   - 故障前稳态窗口索引: {pre_fault_idx}\")\n", "    print(f\"   - 故障后稳态窗口索引: {post_fault_idx}\\n\")\n", "\n", "    # 提取特征并计算差值\n", "    pre_fault_features = feature_df.loc[pre_fault_idx]\n", "    post_fault_features = feature_df.loc[post_fault_idx]\n", "    impact_vector = post_fault_features.drop('label') - pre_fault_features.drop('label')\n", "    \n", "    # 格式化输出\n", "    impact_df = pd.DataFrame({\n", "        'Pre-Fault Value': pre_fault_features.drop('label'),\n", "        'Post-Fault Value': post_fault_features.drop('label'),\n", "        'Impact (Delta)': impact_vector\n", "    })\n", "    \n", "    pd.set_option('display.float_format', '{:.6f}'.format) # 设置浮点数显示格式\n", "    print(\"--- 故障影响特征向量 (Fault Impact Feature Vector) ---\")\n", "    print(impact_df)"]}, {"cell_type": "code", "execution_count": 8, "id": "16b8faca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 步骤1: 加载和准备数据 ---\n", "✅ 文件 'pv_fault_data.mat' 加载成功!\n", "   文件中包含的变量: ['__header__', '__version__', '__globals__', 'Grid_current', 'I1', 'I2', 'I3', 'I4', 'Ia', 'Ib', 'Ic', 'Ir', 'PQ', 'THD', 'Ts_Control', 'Ts_Power', 'Va', 'Vb', 'Vc', 'Vdc', 'a_phase_current', 'tout']\n", "   - 仿真参数: 总时长=3.0s, 采样点=60001, 采样率=20000.33Hz\n", "\n", "--- 步骤2: 数据分窗 ---\n", "   - 窗口参数: 大小=800点, 步长=400点\n", "   - 将生成 149 个分析窗口。\n", "\n", "--- 步骤3: 循环提取特征 ---\n", "✅ 特征提取完成，已生成DataFrame。\n", "\n", "--- 步骤4: 特征标注 ---\n", "✅ 数据标注完成。\n", "\n", "--- 步骤5: 计算故障影响向量 ---\n", "   - 故障前稳态窗口索引: 49\n", "   - 故障后稳态窗口索引: 55\n", "\n", "--- 故障影响特征向量 (Fault Impact Feature Vector) ---\n", "                      Pre-Fault Value  Post-Fault Value  Impact (Delta)\n", "ia_rms                       8.172928          6.866828       -1.306100\n", "ia_peak                     12.965036         10.230385       -2.734651\n", "ia_std                       8.108682          6.832810       -1.275872\n", "ia_crest_factor              1.586339          1.489827       -0.096512\n", "ia_h3_mag                    1.616133          1.309025       -0.307108\n", "ia_h5_mag                    0.687002          0.618033       -0.068970\n", "ia_h7_mag                    0.472133          0.429839       -0.042294\n", "ia_h11_mag                   0.275418          0.249270       -0.026148\n", "ia_h13_mag                   0.229817          0.208116       -0.021701\n", "ia_h15_mag                   0.197453          0.178756       -0.018696\n", "ia_h17_mag                   0.173196          0.156770       -0.016427\n", "ia_h19_mag                   0.154283          0.139662       -0.014621\n", "ia_thd                       0.953417          0.803384       -0.150033\n", "ia_fundamental_mag           1.970133          1.952988       -0.017145\n", "ia_wavelet_energy_d1         0.934993          1.657172        0.722179\n", "ia_wavelet_energy_d2         0.256926          0.961228        0.704302\n", "ia_wavelet_energy_d3         0.049469          0.066162        0.016693\n", "ia_wavelet_energy_d4         0.000183          0.000170       -0.000013\n", "dc_volt_mean               496.650754        500.014531        3.363777\n", "dc_volt_std                  4.303905          2.124723       -2.179182\n", "active_power_P          348604.224541     297491.682676   -51112.541865\n", "reactive_power_Q        -53229.013242     -50193.409742     3035.603500\n"]}], "source": ["if __name__ == '__main__':\n", "    main()"]}, {"cell_type": "code", "execution_count": null, "id": "e85f048f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "matlab", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}