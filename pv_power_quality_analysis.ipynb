{"cells": [{"cell_type": "markdown", "id": "title_cell", "metadata": {}, "source": ["# 光伏断路故障对电网电能质量影响特征提取与MPC补偿预备分析\n", "\n", "## 项目概述\n", "本项目旨在分析400KW并网光伏发电系统中PV Array4断路故障对电网电能质量的影响，\n", "提取关键特征用于后续的主动电网支撑和MPC补偿算法开发。\n", "\n", "## 系统配置\n", "- **总容量**: 400KW (4×100KW光伏阵列)\n", "- **故障类型**: PV Array4断路故障\n", "- **故障时间**: 1秒后断开\n", "- **分析目标**: 电网电流谐波畸变率及电能质量影响\n", "- **最终目标**: 实现光伏故障的主动电网支撑和无感补偿\n", "\n", "## 技术路线\n", "1. **数据预处理与分窗分析** - 确保数据质量和时间同步\n", "2. **多维特征提取** - 时域、频域、小波域全方位分析\n", "3. **电能质量指标量化** - 符合国标GB/T 14549-1993\n", "4. **故障影响向量计算** - 量化故障对电网的具体影响\n", "5. **MPC补偿特征基础** - 为预测控制算法提供输入特征\n", "6. **可视化分析** - 直观展示故障影响和补偿需求"]}, {"cell_type": "code", "execution_count": 1, "id": "imports_cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 所有库导入成功！\n", "NumPy版本: 2.2.6\n", "Pandas版本: 2.3.1\n", "SciPy版本: 1.15.3\n"]}], "source": ["# 导入必要的库\n", "import numpy as np\n", "import pandas as pd\n", "import scipy.io\n", "import scipy.stats\n", "import pywt\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import signal\n", "from scipy.fft import fft, fftfreq\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import mean_squared_error\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体和图表样式\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ 所有库导入成功！\")\n", "print(f\"NumPy版本: {np.__version__}\")\n", "print(f\"Pandas版本: {pd.__version__}\")\n", "print(f\"SciPy版本: {scipy.__version__}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "config_cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 电能质量分析配置加载完成！\n"]}], "source": ["# ===================================================================\n", "# 电能质量分析专用配置类\n", "# ===================================================================\n", "class PowerQualityConfig:\n", "    \"\"\"电能质量分析和MPC补偿算法专用配置类\"\"\"\n", "    \n", "    # -- 基础仿真参数 --\n", "    MAT_FILE_PATH = 'pv_fault_data.mat'  # .mat文件路径\n", "    SIM_TIME = 3.0                       # 总仿真时间 (秒)\n", "    FAULT_TIME = 1.0                     # 故障发生时间 (秒)\n", "    GRID_FREQ = 50.0                     # 电网基波频率 (Hz)\n", "    RATED_POWER = 400000                 # 系统额定功率 (W)\n", "    RATED_VOLTAGE = 380                  # 额定线电压 (V)\n", "    RATED_CURRENT = 608                  # 额定电流 (A)\n", "    \n", "    # -- 窗口化分析参数 --\n", "    CYCLES_PER_WINDOW = 2                # 每个窗口包含的工频周期数\n", "    WINDOW_OVERLAP = 0.5                 # 窗口重叠率 (50%)\n", "    \n", "    # -- 电能质量标准参数 (GB/T 14549-1993) --\n", "    THD_LIMIT = 0.05                     # 总谐波畸变率限值 (5%)\n", "    VOLTAGE_DEVIATION_LIMIT = 0.07       # 电压偏差限值 (±7%)\n", "    FREQUENCY_DEVIATION_LIMIT = 0.2      # 频率偏差限值 (±0.2Hz)\n", "    FLICKER_LIMIT = 1.0                  # 闪变限值\n", "    \n", "    # -- 谐波分析参数 --\n", "    HARMONICS_TO_ANALYZE = [2, 3, 5, 7, 11, 13, 15, 17, 19, 21, 23, 25]  # 扩展谐波分析\n", "    INTERHARMONICS = [1.5, 2.5, 3.5, 4.5, 6.5, 8.5]  # 间谐波分析\n", "    SUBHARMONICS = [0.5, 1.5]            # 次谐波分析\n", "    \n", "    # -- 小波分析参数 --\n", "    WAVELET_TYPE = 'db6'                 # 小波基函数 (db6对电力信号效果更好)\n", "    WAVELET_LEVEL = 6                    # 分解层数\n", "    \n", "    # -- 故障分析参数 --\n", "    PRE_FAULT_ANALYSIS_WINDOWS = 15      # 故障前分析窗口数\n", "    POST_FAULT_STABLE_OFFSET = 10        # 故障后稳态偏移窗口数\n", "    TRANSIENT_ANALYSIS_WINDOWS = 20      # 暂态过程分析窗口数\n", "    \n", "    # -- MPC补偿算法相关参数 --\n", "    PREDICTION_HORIZON = 20              # 预测时域\n", "    CONTROL_HORIZON = 5                  # 控制时域\n", "    COMPENSATION_DELAY = 0.02            # 补偿延迟时间 (秒)\n", "    SAMPLING_TIME = 0.001                # 控制采样时间 (秒)\n", "    \n", "    # -- 可视化参数 --\n", "    FIGURE_SIZE = (15, 10)\n", "    DPI = 300\n", "    SAVE_PLOTS = True\n", "    PLOT_FORMAT = 'png'\n", "    \n", "    # -- 数据导出参数 --\n", "    EXPORT_FEATURES = True\n", "    EXPORT_FORMAT = 'csv'\n", "    FEATURE_FILE_NAME = 'pv_fault_features.csv'\n", "    \n", "print(\"✅ 电能质量分析配置加载完成！\")"]}, {"cell_type": "code", "execution_count": 3, "id": "data_loader_cell", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 数据加载与预处理模块\n", "# ===================================================================\n", "\n", "def load_and_validate_data(config):\n", "    \"\"\"\n", "    从.mat文件加载数据并进行验证和预处理\n", "    \n", "    Returns:\n", "        tuple: (data_dict, sampling_rate, time_vector)\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"📊 步骤1: 数据加载与验证\")\n", "    print(\"=\"*60)\n", "    \n", "    try:\n", "        mat_data = scipy.io.loadmat(config.MAT_FILE_PATH)\n", "        print(f\"✅ 文件 '{config.MAT_FILE_PATH}' 加载成功!\")\n", "        print(f\"📋 文件中包含的变量: {list(mat_data.keys())}\")\n", "    except FileNotFoundError:\n", "        print(f\"❌ 错误: 未找到文件 '{config.MAT_FILE_PATH}'\")\n", "        return None, None, None\n", "    except Exception as e:\n", "        print(f\"❌ 文件加载错误: {e}\")\n", "        return None, None, None\n", "\n", "    # 定义需要提取的变量\n", "    required_vars = ['Ia', 'Ib', 'Ic', 'Va', 'Vb', 'Vc', 'Vdc']\n", "    optional_vars = ['Grid_current', 'PQ', 'THD', 'I1', 'I2', 'I3', 'I4']\n", "    \n", "    # 提取数据并验证\n", "    data_dict = {}\n", "    for var in required_vars:\n", "        if var in mat_data:\n", "            data_dict[var] = mat_data[var].flatten()\n", "            print(f\"✅ {var}: {len(data_dict[var])} 个采样点\")\n", "        else:\n", "            print(f\"❌ 缺少关键变量: {var}\")\n", "            return None, None, None\n", "    \n", "    # 提取可选变量\n", "    for var in optional_vars:\n", "        if var in mat_data:\n", "            data_dict[var] = mat_data[var].flatten()\n", "            print(f\"📈 {var}: {len(data_dict[var])} 个采样点 (可选)\")\n", "    \n", "    # 计算采样参数\n", "    num_samples = len(data_dict['Ia'])\n", "    sampling_rate = num_samples / config.SIM_TIME\n", "    time_vector = np.linspace(0, config.SIM_TIME, num_samples)\n", "    \n", "    print(f\"\\n📊 采样信息:\")\n", "    print(f\"   - 仿真时长: {config.SIM_TIME} 秒\")\n", "    print(f\"   - 采样点数: {num_samples}\")\n", "    print(f\"   - 采样频率: {sampling_rate:.2f} Hz\")\n", "    print(f\"   - 采样间隔: {1/sampling_rate*1000:.3f} ms\")\n", "    \n", "    # 数据质量检查\n", "    print(f\"\\n🔍 数据质量检查:\")\n", "    for var_name, var_data in data_dict.items():\n", "        if var_name in required_vars:\n", "            nan_count = np.isnan(var_data).sum()\n", "            inf_count = np.isinf(var_data).sum()\n", "            if nan_count > 0 or inf_count > 0:\n", "                print(f\"⚠️  {var_name}: {nan_count} NaN值, {inf_count} 无穷值\")\n", "            else:\n", "                print(f\"✅ {var_name}: 数据完整\")\n", "    \n", "    return data_dict, sampling_rate, time_vector\n", "\n", "def plot_raw_data_overview(data_dict, time_vector, config):\n", "    \"\"\"\n", "    绘制原始数据概览图\n", "    \"\"\"\n", "    fig, axes = plt.subplots(3, 2, figsize=config.FIGURE_SIZE)\n", "    fig.suptitle('光伏系统原始数据概览', fontsize=16, fontweight='bold')\n", "    \n", "    # 故障时间线\n", "    fault_time = config.FAULT_TIME\n", "    \n", "    # 三相电流\n", "    axes[0, 0].plot(time_vector, data_dict['Ia'], label='Ia', alpha=0.8)\n", "    axes[0, 0].plot(time_vector, data_dict['Ib'], label='Ib', alpha=0.8)\n", "    axes[0, 0].plot(time_vector, data_dict['Ic'], label='Ic', alpha=0.8)\n", "    axes[0, 0].axvline(fault_time, color='red', linestyle='--', label='故障时刻')\n", "    axes[0, 0].set_title('三相电流')\n", "    axes[0, 0].set_ylabel('电流 (A)')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 三相电压\n", "    axes[0, 1].plot(time_vector, data_dict['Va'], label='Va', alpha=0.8)\n", "    axes[0, 1].plot(time_vector, data_dict['Vb'], label='Vb', alpha=0.8)\n", "    axes[0, 1].plot(time_vector, data_dict['Vc'], label='Vc', alpha=0.8)\n", "    axes[0, 1].axvline(fault_time, color='red', linestyle='--', label='故障时刻')\n", "    axes[0, 1].set_title('三相电压')\n", "    axes[0, 1].set_ylabel('电压 (V)')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 直流母线电压\n", "    axes[1, 0].plot(time_vector, data_dict['Vdc'], color='purple', linewidth=2)\n", "    axes[1, 0].axvline(fault_time, color='red', linestyle='--', label='故障时刻')\n", "    axes[1, 0].set_title('直流母线电压')\n", "    axes[1, 0].set_ylabel('电压 (V)')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # A相电流放大图（故障前后）\n", "    start_idx = int((fault_time - 0.1) * len(time_vector) / config.SIM_TIME)\n", "    end_idx = int((fault_time + 0.1) * len(time_vector) / config.SIM_TIME)\n", "    axes[1, 1].plot(time_vector[start_idx:end_idx], data_dict['Ia'][start_idx:end_idx], \n", "                   color='blue', linewidth=2)\n", "    axes[1, 1].axvline(fault_time, color='red', linestyle='--', label='故障时刻')\n", "    axes[1, 1].set_title('A相电流 (故障前后0.1秒)')\n", "    axes[1, 1].set_ylabel('电流 (A)')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # 功率计算和显示\n", "    p_inst = (data_dict['Va'] * data_dict['Ia'] + \n", "              data_dict['Vb'] * data_dict['Ib'] + \n", "              data_dict['Vc'] * data_dict['Ic'])\n", "    axes[2, 0].plot(time_vector, p_inst/1000, color='green', linewidth=2)\n", "    axes[2, 0].axvline(fault_time, color='red', linestyle='--', label='故障时刻')\n", "    axes[2, 0].set_title('瞬时功率')\n", "    axes[2, 0].set_ylabel('功率 (kW)')\n", "    axes[2, 0].set_xlabel('时间 (s)')\n", "    axes[2, 0].legend()\n", "    axes[2, 0].grid(True, alpha=0.3)\n", "    \n", "    # 电流有效值趋势\n", "    window_size = int(0.02 * len(time_vector) / config.SIM_TIME)  # 20ms窗口\n", "    ia_rms = np.array([np.sqrt(np.mean(data_dict['Ia'][i:i+window_size]**2)) \n", "                      for i in range(0, len(data_dict['Ia'])-window_size, window_size//2)])\n", "    time_rms = time_vector[::window_size//2][:len(ia_rms)]\n", "    axes[2, 1].plot(time_rms, ia_rms, color='orange', linewidth=2)\n", "    axes[2, 1].axvline(fault_time, color='red', linestyle='--', label='故障时刻')\n", "    axes[2, 1].set_title('A相电流有效值趋势')\n", "    axes[2, 1].set_ylabel('电流有效值 (A)')\n", "    axes[2, 1].set_xlabel('时间 (s)')\n", "    axes[2, 1].legend()\n", "    axes[2, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    if config.SAVE_PLOTS:\n", "        plt.savefig(f'raw_data_overview.{config.PLOT_FORMAT}', dpi=config.DPI, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(\"✅ 原始数据概览图绘制完成\")"]}, {"cell_type": "code", "execution_count": 4, "id": "feature_extraction_cell", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 电能质量特征提取模块\n", "# ===================================================================\n", "\n", "class PowerQualityFeatureExtractor:\n", "    \"\"\"电能质量特征提取器\"\"\"\n", "    \n", "    def __init__(self, config):\n", "        self.config = config\n", "        \n", "    def extract_time_domain_features(self, signal_data):\n", "        \"\"\"提取时域特征\"\"\"\n", "        features = {}\n", "        \n", "        # 基础统计特征\n", "        features['rms'] = np.sqrt(np.mean(signal_data**2))\n", "        features['peak'] = np.max(np.abs(signal_data))\n", "        features['mean'] = np.mean(signal_data)\n", "        features['std'] = np.std(signal_data)\n", "        features['variance'] = np.var(signal_data)\n", "        features['skewness'] = scipy.stats.skew(signal_data)\n", "        features['kurtosis'] = scipy.stats.kurtosis(signal_data)\n", "        \n", "        # 波形因子\n", "        if features['rms'] != 0:\n", "            features['crest_factor'] = features['peak'] / features['rms']\n", "            features['form_factor'] = features['rms'] / np.mean(np.abs(signal_data))\n", "        else:\n", "            features['crest_factor'] = 0\n", "            features['form_factor'] = 0\n", "            \n", "        # 能量特征\n", "        features['energy'] = np.sum(signal_data**2)\n", "        features['power'] = features['energy'] / len(signal_data)\n", "        \n", "        return features\n", "    \n", "    def extract_frequency_domain_features(self, signal_data, fs):\n", "        \"\"\"提取频域特征\"\"\"\n", "        features = {}\n", "        N = len(signal_data)\n", "        \n", "        # FFT分析\n", "        yf = fft(signal_data)\n", "        yf_mag = 2.0/N * np.abs(yf[:N//2])\n", "        freqs = fftfreq(N, 1/fs)[:N//2]\n", "        \n", "        # 基波分析\n", "        fundamental_idx = np.argmin(np.abs(freqs - self.config.GRID_FREQ))\n", "        features['fundamental_magnitude'] = yf_mag[fundamental_idx]\n", "        features['fundamental_phase'] = np.angle(yf[fundamental_idx])\n", "        \n", "        # 谐波分析\n", "        harmonics_power = 0\n", "        for h in self.config.HARMONICS_TO_ANALYZE:\n", "            harmonic_freq = h * self.config.GRID_FREQ\n", "            harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))\n", "            if harmonic_idx < len(yf_mag):\n", "                harmonic_mag = yf_mag[harmonic_idx]\n", "                features[f'harmonic_{h}_magnitude'] = harmonic_mag\n", "                features[f'harmonic_{h}_phase'] = np.angle(yf[harmonic_idx])\n", "                harmonics_power += harmonic_mag**2\n", "        \n", "        # THD计算\n", "        if features['fundamental_magnitude'] != 0:\n", "            features['thd'] = np.sqrt(harmonics_power) / features['fundamental_magnitude']\n", "        else:\n", "            features['thd'] = 0\n", "            \n", "        # 频谱重心\n", "        features['spectral_centroid'] = np.sum(freqs * yf_mag) / np.sum(yf_mag)\n", "        \n", "        # 频谱扩散度\n", "        features['spectral_spread'] = np.sqrt(np.sum(((freqs - features['spectral_centroid'])**2) * yf_mag) / np.sum(yf_mag))\n", "        \n", "        return features\n", "    \n", "    def extract_wavelet_features(self, signal_data):\n", "        \"\"\"提取小波域特征\"\"\"\n", "        features = {}\n", "        \n", "        # 小波分解\n", "        coeffs = pywt.wavedec(signal_data, self.config.WAVELET_TYPE, level=self.config.WAVELET_LEVEL)\n", "        \n", "        # 各层能量\n", "        for i in range(self.config.WAVELET_LEVEL):\n", "            features[f'wavelet_energy_d{i+1}'] = np.sum(coeffs[i+1]**2)\n", "            features[f'wavelet_energy_ratio_d{i+1}'] = features[f'wavelet_energy_d{i+1}'] / np.sum([np.sum(c**2) for c in coeffs])\n", "        \n", "        # 近似系数能量\n", "        features['wavelet_energy_a'] = np.sum(coeffs[0]**2)\n", "        \n", "        # 小波熵\n", "        total_energy = sum([np.sum(c**2) for c in coeffs])\n", "        if total_energy > 0:\n", "            energies = [np.sum(c**2)/total_energy for c in coeffs]\n", "            features['wavelet_entropy'] = -np.sum([e*np.log2(e) for e in energies if e > 0])\n", "        else:\n", "            features['wavelet_entropy'] = 0\n", "            \n", "        return features\n", "    \n", "    def extract_power_quality_indicators(self, voltage_data, current_data, fs):\n", "        \"\"\"提取电能质量指标\"\"\"\n", "        features = {}\n", "        \n", "        # 电压质量指标\n", "        v_rms = np.sqrt(np.mean(voltage_data**2))\n", "        features['voltage_deviation'] = (v_rms - self.config.RATED_VOLTAGE) / self.config.RATED_VOLTAGE\n", "        features['voltage_unbalance'] = np.std([np.sqrt(np.mean(v**2)) for v in [voltage_data]]) / v_rms\n", "        \n", "        # 电流质量指标\n", "        i_rms = np.sqrt(np.mean(current_data**2))\n", "        features['current_deviation'] = (i_rms - self.config.RATED_CURRENT) / self.config.RATED_CURRENT\n", "        \n", "        # 功率因数\n", "        p_inst = voltage_data * current_data\n", "        features['active_power'] = np.mean(p_inst)\n", "        features['apparent_power'] = v_rms * i_rms\n", "        if features['apparent_power'] != 0:\n", "            features['power_factor'] = features['active_power'] / features['apparent_power']\n", "        else:\n", "            features['power_factor'] = 0\n", "            \n", "        # 频率偏差（通过零交叉点检测）\n", "        zero_crossings = np.where(np.diff(np.signbit(voltage_data)))[0]\n", "        if len(zero_crossings) > 2:\n", "            periods = np.diff(zero_crossings[::2]) / fs  # 每个周期的时间\n", "            avg_period = np.mean(periods)\n", "            measured_freq = 1 / avg_period if avg_period > 0 else 0\n", "            features['frequency_deviation'] = measured_freq - self.config.GRID_FREQ\n", "        else:\n", "            features['frequency_deviation'] = 0\n", "            \n", "        return features\n", "    \n", "    def extract_all_features_for_window(self, window_data, fs):\n", "        \"\"\"为单个窗口提取所有特征\"\"\"\n", "        all_features = {}\n", "        \n", "        # 对每相电流和电压提取特征\n", "        phases = ['a', 'b', 'c']\n", "        for i, phase in enumerate(phases):\n", "            current_key = ['Ia', 'Ib', 'Ic'][i]\n", "            voltage_key = ['Va', 'Vb', 'Vc'][i]\n", "            \n", "            if current_key in window_data and voltage_key in window_data:\n", "                # 电流特征\n", "                current_features = self.extract_time_domain_features(window_data[current_key])\n", "                for key, value in current_features.items():\n", "                    all_features[f'i{phase}_{key}'] = value\n", "                \n", "                freq_features = self.extract_frequency_domain_features(window_data[current_key], fs)\n", "                for key, value in freq_features.items():\n", "                    all_features[f'i{phase}_{key}'] = value\n", "                \n", "                wavelet_features = self.extract_wavelet_features(window_data[current_key])\n", "                for key, value in wavelet_features.items():\n", "                    all_features[f'i{phase}_{key}'] = value\n", "                \n", "                # 电压特征\n", "                voltage_features = self.extract_time_domain_features(window_data[voltage_key])\n", "                for key, value in voltage_features.items():\n", "                    all_features[f'v{phase}_{key}'] = value\n", "                \n", "                # 电能质量指标\n", "                pq_features = self.extract_power_quality_indicators(\n", "                    window_data[voltage_key], window_data[current_key], fs)\n", "                for key, value in pq_features.items():\n", "                    all_features[f'{phase}_{key}'] = value\n", "        \n", "        # 直流侧特征\n", "        if 'Vdc' in window_data:\n", "            dc_features = self.extract_time_domain_features(window_data['Vdc'])\n", "            for key, value in dc_features.items():\n", "                all_features[f'vdc_{key}'] = value\n", "        \n", "        # 三相总体特征\n", "        if all(['Ia', 'Ib', 'Ic'] in window_data.keys()):\n", "            # 三相不平衡度\n", "            i_rms = [np.sqrt(np.mean(window_data[f'I{phase}']**2)) for phase in ['a', 'b', 'c']]\n", "            all_features['current_unbalance'] = np.std(i_rms) / np.mean(i_rms) if np.mean(i_rms) > 0 else 0\n", "            \n", "            # 零序电流\n", "            i_zero = (window_data['Ia'] + window_data['Ib'] + window_data['Ic']) / 3\n", "            all_features['zero_sequence_current'] = np.sqrt(np.mean(i_zero**2))\n", "        \n", "        return all_features"]}, {"cell_type": "code", "execution_count": 5, "id": "windowing_cell", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 数据分窗与特征提取主流程\n", "# ===================================================================\n", "\n", "def perform_windowed_analysis(data_dict, sampling_rate, config):\n", "    \"\"\"\n", "    执行分窗分析和特征提取\n", "    \n", "    Returns:\n", "        tuple: (feature_df, window_info)\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🔍 步骤2: 分窗分析与特征提取\")\n", "    print(\"=\"*60)\n", "    \n", "    # 计算窗口参数\n", "    window_size = int(sampling_rate * config.CYCLES_PER_WINDOW / config.GRID_FREQ)\n", "    step_size = int(window_size * (1 - config.WINDOW_OVERLAP))\n", "    num_windows = (len(data_dict['Ia']) - window_size) // step_size + 1\n", "    \n", "    print(f\"📊 分窗参数:\")\n", "    print(f\"   - 每窗口周期数: {config.CYCLES_PER_WINDOW}\")\n", "    print(f\"   - 窗口大小: {window_size} 点 ({window_size/sampling_rate*1000:.1f} ms)\")\n", "    print(f\"   - 步长: {step_size} 点 ({step_size/sampling_rate*1000:.1f} ms)\")\n", "    print(f\"   - 重叠率: {config.WINDOW_OVERLAP*100:.1f}%\")\n", "    print(f\"   - 总窗口数: {num_windows}\")\n", "    \n", "    # 初始化特征提取器\n", "    feature_extractor = PowerQualityFeatureExtractor(config)\n", "    \n", "    # 存储所有窗口的特征\n", "    all_features = []\n", "    window_info = []\n", "    \n", "    print(f\"\\n🔄 开始特征提取...\")\n", "    for i in range(num_windows):\n", "        start_idx = i * step_size\n", "        end_idx = start_idx + window_size\n", "        window_center_time = (start_idx + end_idx) / 2 / sampling_rate\n", "        \n", "        # 创建当前窗口数据\n", "        current_window_data = {}\n", "        for key, data in data_dict.items():\n", "            current_window_data[key] = data[start_idx:end_idx]\n", "        \n", "        # 提取特征\n", "        features = feature_extractor.extract_all_features_for_window(\n", "            current_window_data, sampling_rate)\n", "        \n", "        # 添加窗口信息\n", "        features['window_index'] = i\n", "        features['window_center_time'] = window_center_time\n", "        features['window_start_time'] = start_idx / sampling_rate\n", "        features['window_end_time'] = end_idx / sampling_rate\n", "        \n", "        all_features.append(features)\n", "        window_info.append({\n", "            'index': i,\n", "            'start_idx': start_idx,\n", "            'end_idx': end_idx,\n", "            'center_time': window_center_time\n", "        })\n", "        \n", "        if (i + 1) % 20 == 0:\n", "            print(f\"   已处理 {i+1}/{num_windows} 个窗口\")\n", "    \n", "    # 转换为DataFrame\n", "    feature_df = pd.DataFrame(all_features)\n", "    \n", "    print(f\"✅ 特征提取完成!\")\n", "    print(f\"   - 提取特征数: {len(feature_df.columns)}\")\n", "    print(f\"   - 数据维度: {feature_df.shape}\")\n", "    \n", "    return feature_df, window_info\n", "\n", "def add_fault_labels(feature_df, config):\n", "    \"\"\"\n", "    为特征数据添加故障标签\n", "    \"\"\"\n", "    print(f\"\\n🏷️  添加故障标签...\")\n", "    \n", "    # 创建标签\n", "    feature_df['fault_label'] = 0  # 0: 正常, 1: 故障\n", "    feature_df['fault_phase'] = 'normal'  # 故障阶段标识\n", "    \n", "    # 根据时间标记故障\n", "    fault_mask = feature_df['window_center_time'] >= config.FAULT_TIME\n", "    feature_df.loc[fault_mask, 'fault_label'] = 1\n", "    \n", "    # 细分故障阶段\n", "    pre_fault_mask = feature_df['window_center_time'] < config.FAULT_TIME\n", "    transient_mask = ((feature_df['window_center_time'] >= config.FAULT_TIME) & \n", "                     (feature_df['window_center_time'] < config.FAULT_TIME + 0.2))  # 故障后200ms为暂态\n", "    post_fault_mask = feature_df['window_center_time'] >= config.FAULT_TIME + 0.2\n", "    \n", "    feature_df.loc[pre_fault_mask, 'fault_phase'] = 'pre_fault'\n", "    feature_df.loc[transient_mask, 'fault_phase'] = 'transient'\n", "    feature_df.loc[post_fault_mask, 'fault_phase'] = 'post_fault'\n", "    \n", "    # 统计信息\n", "    phase_counts = feature_df['fault_phase'].value_counts()\n", "    print(f\"   故障阶段分布:\")\n", "    for phase, count in phase_counts.items():\n", "        print(f\"     - {phase}: {count} 个窗口\")\n", "    \n", "    return feature_df"]}, {"cell_type": "code", "execution_count": 6, "id": "analysis_cell", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# 故障影响分析与可视化\n", "# ===================================================================\n", "\n", "def calculate_fault_impact_vector(feature_df, config):\n", "    \"\"\"\n", "    计算故障影响向量\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"📈 步骤3: 故障影响向量计算\")\n", "    print(\"=\"*60)\n", "    \n", "    # 选择故障前后的稳态窗口\n", "    pre_fault_windows = feature_df[feature_df['fault_phase'] == 'pre_fault'].tail(config.PRE_FAULT_ANALYSIS_WINDOWS)\n", "    post_fault_windows = feature_df[feature_df['fault_phase'] == 'post_fault'].head(config.POST_FAULT_STABLE_OFFSET)\n", "    \n", "    if len(pre_fault_windows) == 0 or len(post_fault_windows) == 0:\n", "        print(\"❌ 错误: 无法找到足够的故障前后稳态窗口\")\n", "        return None\n", "    \n", "    print(f\"📊 分析窗口:\")\n", "    print(f\"   - 故障前稳态窗口: {len(pre_fault_windows)} 个\")\n", "    print(f\"   - 故障后稳态窗口: {len(post_fault_windows)} 个\")\n", "    \n", "    # 排除非数值列\n", "    numeric_columns = feature_df.select_dtypes(include=[np.number]).columns\n", "    exclude_columns = ['window_index', 'window_center_time', 'window_start_time', 'window_end_time', 'fault_label']\n", "    feature_columns = [col for col in numeric_columns if col not in exclude_columns]\n", "    \n", "    # 计算故障前后的平均值\n", "    pre_fault_mean = pre_fault_windows[feature_columns].mean()\n", "    post_fault_mean = post_fault_windows[feature_columns].mean()\n", "    \n", "    # 计算影响向量（绝对变化和相对变化）\n", "    absolute_impact = post_fault_mean - pre_fault_mean\n", "    relative_impact = (post_fault_mean - pre_fault_mean) / (pre_fault_mean + 1e-10) * 100  # 避免除零\n", "    \n", "    # 创建影响分析DataFrame\n", "    impact_analysis = pd.DataFrame({\n", "        'Pre_Fault_Mean': pre_fault_mean,\n", "        'Post_Fault_Mean': post_fault_mean,\n", "        'Absolute_Impact': absolute_impact,\n", "        'Relative_Impact_Percent': relative_impact,\n", "        'Impact_Magnitude': np.abs(relative_impact)\n", "    })\n", "    \n", "    # 按影响程度排序\n", "    impact_analysis = impact_analysis.sort_values('Impact_Magnitude', ascending=False)\n", "    \n", "    print(f\"\\n🎯 故障影响最显著的前10个特征:\")\n", "    print(impact_analysis.head(10)[['Relative_Impact_Percent', 'Absolute_Impact']].round(4))\n", "    \n", "    return impact_analysis\n", "\n", "def plot_fault_impact_analysis(feature_df, impact_analysis, config):\n", "    \"\"\"\n", "    绘制故障影响分析图\n", "    \"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=config.FIGURE_SIZE)\n", "    fig.suptitle('光伏断路故障影响分析', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. 关键特征时间序列\n", "    key_features = ['ia_rms', 'ia_thd', 'a_active_power', 'vdc_rms']\n", "    colors = ['blue', 'red', 'green', 'purple']\n", "    \n", "    for i, (feature, color) in enumerate(zip(key_features, colors)):\n", "        if feature in feature_df.columns:\n", "            axes[0, 0].plot(feature_df['window_center_time'], feature_df[feature], \n", "                           label=feature, color=color, alpha=0.8)\n", "    \n", "    axes[0, 0].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')\n", "    axes[0, 0].set_title('关键电能质量特征时间序列')\n", "    axes[0, 0].set_xlabel('时间 (s)')\n", "    axes[0, 0].set_ylabel('特征值')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. 故障影响程度排序\n", "    top_impacts = impact_analysis.head(15)\n", "    y_pos = np.arange(len(top_impacts))\n", "    \n", "    bars = axes[0, 1].barh(y_pos, top_impacts['Relative_Impact_Percent'], \n", "                          color=['red' if x > 0 else 'blue' for x in top_impacts['Relative_Impact_Percent']])\n", "    axes[0, 1].set_yticks(y_pos)\n", "    axes[0, 1].set_yticklabels(top_impacts.index, fontsize=8)\n", "    axes[0, 1].set_title('故障影响程度排序 (相对变化%)')\n", "    axes[0, 1].set_xlabel('相对变化 (%)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. THD变化分析\n", "    if 'ia_thd' in feature_df.columns:\n", "        pre_fault_data = feature_df[feature_df['fault_phase'] == 'pre_fault']['ia_thd']\n", "        post_fault_data = feature_df[feature_df['fault_phase'] == 'post_fault']['ia_thd']\n", "        \n", "        axes[1, 0].hist(pre_fault_data, bins=20, alpha=0.7, label='故障前', color='blue')\n", "        axes[1, 0].hist(post_fault_data, bins=20, alpha=0.7, label='故障后', color='red')\n", "        axes[1, 0].axvline(config.THD_LIMIT, color='orange', linestyle='--', linewidth=2, label='国标限值(5%)')\n", "        axes[1, 0].set_title('THD分布对比')\n", "        axes[1, 0].set_xlabel('THD')\n", "        axes[1, 0].set_ylabel('频次')\n", "        axes[1, 0].legend()\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 4. 功率变化分析\n", "    if 'a_active_power' in feature_df.columns:\n", "        power_data = feature_df['a_active_power'] / 1000  # 转换为kW\n", "        axes[1, 1].plot(feature_df['window_center_time'], power_data, \n", "                       color='green', linewidth=2, label='有功功率')\n", "        axes[1, 1].axvline(config.FAULT_TIME, color='red', linestyle='--', linewidth=2, label='故障时刻')\n", "        \n", "        # 添加功率损失标注\n", "        pre_power = feature_df[feature_df['fault_phase'] == 'pre_fault']['a_active_power'].mean() / 1000\n", "        post_power = feature_df[feature_df['fault_phase'] == 'post_fault']['a_active_power'].mean() / 1000\n", "        power_loss = pre_power - post_power\n", "        \n", "        axes[1, 1].text(0.02, 0.98, f'功率损失: {power_loss:.1f} kW\\n损失率: {power_loss/pre_power*100:.1f}%', \n", "                        transform=axes[1, 1].transAxes, verticalalignment='top',\n", "                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "        \n", "        axes[1, 1].set_title('有功功率变化')\n", "        axes[1, 1].set_xlabel('时间 (s)')\n", "        axes[1, 1].set_ylabel('功率 (kW)')\n", "        axes[1, 1].legend()\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    if config.SAVE_PLOTS:\n", "        plt.savefig(f'fault_impact_analysis.{config.PLOT_FORMAT}', dpi=config.DPI, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(\"✅ 故障影响分析图绘制完成\")"]}, {"cell_type": "code", "execution_count": 7, "id": "mpc_preparation_cell", "metadata": {}, "outputs": [], "source": ["# ===================================================================\n", "# MPC补偿算法特征准备模块\n", "# ===================================================================\n", "\n", "def prepare_mpc_features(feature_df, impact_analysis, config):\n", "    \"\"\"\n", "    为MPC补偿算法准备特征数据\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🎛️  步骤4: MPC补偿算法特征准备\")\n", "    print(\"=\"*60)\n", "    \n", "    # 选择对故障最敏感的特征作为MPC输入\n", "    top_sensitive_features = impact_analysis.head(20).index.tolist()\n", "    \n", "    # 添加关键电能质量指标\n", "    essential_features = ['ia_rms', 'ia_thd', 'ia_fundamental_magnitude', \n", "                         'a_active_power', 'a_power_factor', 'vdc_rms']\n", "    \n", "    mpc_features = list(set(top_sensitive_features + essential_features))\n", "    mpc_features = [f for f in mpc_features if f in feature_df.columns]\n", "    \n", "    print(f\"📊 MPC特征选择:\")\n", "    print(f\"   - 选择特征数: {len(mpc_features)}\")\n", "    print(f\"   - 关键特征: {essential_features}\")\n", "    \n", "    # 创建MPC训练数据集\n", "    mpc_data = feature_df[mpc_features + ['window_center_time', 'fault_label', 'fault_phase']].copy()\n", "    \n", "    # 数据标准化\n", "    scaler = StandardScaler()\n", "    mpc_data_scaled = mpc_data.copy()\n", "    mpc_data_scaled[mpc_features] = scaler.fit_transform(mpc_data[mpc_features])\n", "    \n", "    # 计算补偿目标值（故障前的稳态值）\n", "    pre_fault_targets = feature_df[feature_df['fault_phase'] == 'pre_fault'][mpc_features].mean()\n", "    \n", "    # 计算需要补偿的量\n", "    compensation_requirements = {}\n", "    for feature in mpc_features:\n", "        if feature in impact_analysis.index:\n", "            compensation_requirements[feature] = {\n", "                'target_value': pre_fault_targets[feature],\n", "                'impact_magnitude': impact_analysis.loc[feature, 'Absolute_Impact'],\n", "                'relative_impact': impact_analysis.loc[feature, 'Relative_Impact_Percent'],\n", "                'compensation_priority': abs(impact_analysis.loc[feature, 'Relative_Impact_Percent'])\n", "            }\n", "    \n", "    # 按补偿优先级排序\n", "    compensation_df = pd.DataFrame(compensation_requirements).T\n", "    compensation_df = compensation_df.sort_values('compensation_priority', ascending=False)\n", "    \n", "    print(f\"\\n🎯 补偿优先级排序 (前10项):\")\n", "    print(compensation_df.head(10)[['relative_impact', 'impact_magnitude']].round(4))\n", "    \n", "    # 生成MPC控制序列建议\n", "    control_suggestions = generate_control_suggestions(compensation_df, config)\n", "    \n", "    return {\n", "        'mpc_features': mpc_features,\n", "        'mpc_data': mpc_data,\n", "        'mpc_data_scaled': mpc_data_scaled,\n", "        'scaler': scaler,\n", "        'compensation_requirements': compensation_df,\n", "        'control_suggestions': control_suggestions\n", "    }\n", "\n", "def generate_control_suggestions(compensation_df, config):\n", "    \"\"\"\n", "    生成MPC控制建议\n", "    \"\"\"\n", "    suggestions = {\n", "        'reactive_power_compensation': 0,\n", "        'harmonic_filtering': {},\n", "        'voltage_regulation': 0,\n", "        'power_factor_correction': 0\n", "    }\n", "    \n", "    # 基于特征影响程度生成控制建议\n", "    for feature, data in compensation_df.iterrows():\n", "        if 'thd' in feature:\n", "            # 谐波补偿建议\n", "            harmonic_num = feature.split('_')[1] if '_' in feature else 'total'\n", "            suggestions['harmonic_filtering'][harmonic_num] = {\n", "                'compensation_level': min(abs(data['relative_impact']) / 100, 1.0),\n", "                'priority': data['compensation_priority']\n", "            }\n", "        elif 'power_factor' in feature:\n", "            # 功率因数校正建议\n", "            suggestions['power_factor_correction'] = abs(data['impact_magnitude'])\n", "        elif 'voltage' in feature:\n", "            # 电压调节建议\n", "            suggestions['voltage_regulation'] = abs(data['impact_magnitude'])\n", "        elif 'reactive_power' in feature:\n", "            # 无功补偿建议\n", "            suggestions['reactive_power_compensation'] = abs(data['impact_magnitude'])\n", "    \n", "    return suggestions\n", "\n", "def plot_mpc_preparation_analysis(mpc_results, config):\n", "    \"\"\"\n", "    绘制MPC准备分析图\n", "    \"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=config.FIGURE_SIZE)\n", "    fig.suptitle('MPC补偿算法特征准备分析', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. 特征重要性\n", "    top_features = mpc_results['compensation_requirements'].head(10)\n", "    y_pos = np.arange(len(top_features))\n", "    \n", "    axes[0, 0].barh(y_pos, top_features['compensation_priority'], color='skyblue')\n", "    axes[0, 0].set_yticks(y_pos)\n", "    axes[0, 0].set_yticklabels(top_features.index, fontsize=8)\n", "    axes[0, 0].set_title('MPC特征重要性排序')\n", "    axes[0, 0].set_xlabel('补偿优先级')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. 补偿需求分析\n", "    compensation_types = ['谐波补偿', '无功补偿', '电压调节', '功率因数校正']\n", "    compensation_values = [\n", "        len(mpc_results['control_suggestions']['harmonic_filtering']),\n", "        mpc_results['control_suggestions']['reactive_power_compensation'],\n", "        mpc_results['control_suggestions']['voltage_regulation'],\n", "        mpc_results['control_suggestions']['power_factor_correction']\n", "    ]\n", "    \n", "    axes[0, 1].pie(compensation_values, labels=compensation_types, autopct='%1.1f%%', startangle=90)\n", "    axes[0, 1].set_title('补偿需求分布')\n", "    \n", "    # 3. 特征相关性热图\n", "    key_features = mpc_results['mpc_features'][:10]  # 取前10个特征\n", "    correlation_matrix = mpc_results['mpc_data'][key_features].corr()\n", "    \n", "    im = axes[1, 0].imshow(correlation_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)\n", "    axes[1, 0].set_xticks(range(len(key_features)))\n", "    axes[1, 0].set_yticks(range(len(key_features)))\n", "    axes[1, 0].set_xticklabels(key_features, rotation=45, ha='right', fontsize=8)\n", "    axes[1, 0].set_yticklabels(key_features, fontsize=8)\n", "    axes[1, 0].set_title('特征相关性矩阵')\n", "    plt.colorbar(im, ax=axes[1, 0])\n", "    \n", "    # 4. 控制时域分析\n", "    time_horizon = np.arange(config.PREDICTION_HORIZON)\n", "    \n", "    # 模拟预测轨迹\n", "    prediction_trajectory = np.exp(-time_horizon * 0.1) * 0.5  # 指数衰减\n", "    control_trajectory = np.ones(config.CONTROL_HORIZON) * 0.3\n", "    \n", "    axes[1, 1].plot(time_horizon, prediction_trajectory, 'b-', linewidth=2, label='预测轨迹')\n", "    axes[1, 1].plot(time_horizon[:config.CONTROL_HORIZON], control_trajectory, \n", "                   'r-', linewidth=3, label='控制时域')\n", "    axes[1, 1].axhline(0, color='black', linestyle='--', alpha=0.5)\n", "    axes[1, 1].set_title('MPC时域设计')\n", "    axes[1, 1].set_xlabel('时间步')\n", "    axes[1, 1].set_ylabel('控制量')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    if config.SAVE_PLOTS:\n", "        plt.savefig(f'mpc_preparation_analysis.{config.PLOT_FORMAT}', dpi=config.DPI, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(\"✅ MPC准备分析图绘制完成\")"]}, {"cell_type": "code", "execution_count": 8, "id": "main_execution_cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始光伏断路故障电能质量影响分析\n", "================================================================================\n", "\n", "============================================================\n", "📊 步骤1: 数据加载与验证\n", "============================================================\n", "✅ 文件 'pv_fault_data.mat' 加载成功!\n", "📋 文件中包含的变量: ['__header__', '__version__', '__globals__', 'Grid_current', 'I1', 'I2', 'I3', 'I4', 'Ia', 'Ib', 'Ic', 'Ir', 'PQ', 'THD', 'Ts_Control', 'Ts_Power', 'Va', 'Vb', 'Vc', 'Vdc', 'a_phase_current', 'tout']\n", "✅ Ia: 60001 个采样点\n", "✅ Ib: 60001 个采样点\n", "✅ Ic: 60001 个采样点\n", "✅ Va: 60001 个采样点\n", "✅ Vb: 60001 个采样点\n", "✅ Vc: 60001 个采样点\n", "✅ Vdc: 60001 个采样点\n", "📈 Grid_current: 1 个采样点 (可选)\n", "📈 PQ: 60002 个采样点 (可选)\n", "📈 THD: 180240 个采样点 (可选)\n", "📈 I1: 60001 个采样点 (可选)\n", "📈 I2: 60001 个采样点 (可选)\n", "📈 I3: 60001 个采样点 (可选)\n", "📈 I4: 60001 个采样点 (可选)\n", "\n", "📊 采样信息:\n", "   - 仿真时长: 3.0 秒\n", "   - 采样点数: 60001\n", "   - 采样频率: 20000.33 Hz\n", "   - 采样间隔: 0.050 ms\n", "\n", "🔍 数据质量检查:\n", "✅ Ia: 数据完整\n", "✅ Ib: 数据完整\n", "✅ Ic: 数据完整\n", "✅ Va: 数据完整\n", "✅ Vb: 数据完整\n", "✅ Vc: 数据完整\n", "✅ Vdc: 数据完整\n"]}, {"data": {"image/png": "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*********************************************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", "text/plain": ["<Figure size 1500x1000 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ 原始数据概览图绘制完成\n", "\n", "============================================================\n", "🔍 步骤2: 分窗分析与特征提取\n", "============================================================\n", "📊 分窗参数:\n", "   - 每窗口周期数: 2\n", "   - 窗口大小: 800 点 (40.0 ms)\n", "   - 步长: 400 点 (20.0 ms)\n", "   - 重叠率: 50.0%\n", "   - 总窗口数: 149\n", "\n", "🔄 开始特征提取...\n", "❌ 分析过程中发生错误: unhashable type: 'list'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21536\\2988185078.py\", line 25, in main_analysis\n", "    feature_df, window_info = perform_windowed_analysis(data_dict, sampling_rate, config)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21536\\1142742060.py\", line 47, in perform_windowed_analysis\n", "    features = feature_extractor.extract_all_features_for_window(\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21536\\2440991827.py\", line 179, in extract_all_features_for_window\n", "    if all(['Ia', 'Ib', 'Ic'] in window_data.keys()):\n", "TypeError: unhashable type: 'list'\n"]}], "source": ["# ===================================================================\n", "# 主执行流程\n", "# ===================================================================\n", "\n", "def main_analysis():\n", "    \"\"\"\n", "    执行完整的光伏故障电能质量影响分析\n", "    \"\"\"\n", "    print(\"🚀 开始光伏断路故障电能质量影响分析\")\n", "    print(\"=\"*80)\n", "    \n", "    # 初始化配置\n", "    config = PowerQualityConfig()\n", "    \n", "    try:\n", "        # 步骤1: 数据加载与验证\n", "        data_dict, sampling_rate, time_vector = load_and_validate_data(config)\n", "        if data_dict is None:\n", "            return None\n", "        \n", "        # 绘制原始数据概览\n", "        plot_raw_data_overview(data_dict, time_vector, config)\n", "        \n", "        # 步骤2: 分窗分析与特征提取\n", "        feature_df, window_info = perform_windowed_analysis(data_dict, sampling_rate, config)\n", "        \n", "        # 添加故障标签\n", "        feature_df = add_fault_labels(feature_df, config)\n", "        \n", "        # 步骤3: 故障影响分析\n", "        impact_analysis = calculate_fault_impact_vector(feature_df, config)\n", "        if impact_analysis is not None:\n", "            plot_fault_impact_analysis(feature_df, impact_analysis, config)\n", "        \n", "        # 步骤4: MPC补偿特征准备\n", "        mpc_results = prepare_mpc_features(feature_df, impact_analysis, config)\n", "        plot_mpc_preparation_analysis(mpc_results, config)\n", "        \n", "        # 步骤5: 结果导出\n", "        if config.EXPORT_FEATURES:\n", "            export_results(feature_df, impact_analysis, mpc_results, config)\n", "        \n", "        # 生成分析报告\n", "        generate_analysis_report(feature_df, impact_analysis, mpc_results, config)\n", "        \n", "        print(\"\\n\" + \"=\"*80)\n", "        print(\"🎉 分析完成！所有结果已保存。\")\n", "        print(\"=\"*80)\n", "        \n", "        return {\n", "            'feature_df': feature_df,\n", "            'impact_analysis': impact_analysis,\n", "            'mpc_results': mpc_results,\n", "            'config': config\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 分析过程中发生错误: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        return None\n", "\n", "def export_results(feature_df, impact_analysis, mpc_results, config):\n", "    \"\"\"\n", "    导出分析结果\n", "    \"\"\"\n", "    print(f\"\\n💾 导出分析结果...\")\n", "    \n", "    # 导出特征数据\n", "    feature_df.to_csv('pv_fault_features.csv', index=False)\n", "    print(f\"   ✅ 特征数据已导出: pv_fault_features.csv\")\n", "    \n", "    # 导出影响分析\n", "    impact_analysis.to_csv('fault_impact_analysis.csv')\n", "    print(f\"   ✅ 影响分析已导出: fault_impact_analysis.csv\")\n", "    \n", "    # 导出MPC特征\n", "    mpc_results['mpc_data'].to_csv('mpc_features.csv', index=False)\n", "    mpc_results['compensation_requirements'].to_csv('compensation_requirements.csv')\n", "    print(f\"   ✅ MPC数据已导出: mpc_features.csv, compensation_requirements.csv\")\n", "\n", "def generate_analysis_report(feature_df, impact_analysis, mpc_results, config):\n", "    \"\"\"\n", "    生成分析报告\n", "    \"\"\"\n", "    print(f\"\\n📋 生成分析报告...\")\n", "    \n", "    report = f\"\"\"\n", "# 光伏断路故障电能质量影响分析报告\n", "\n", "## 系统配置\n", "- 系统容量: {config.RATED_POWER/1000} kW\n", "- 故障时间: {config.FAULT_TIME} 秒\n", "- 分析时长: {config.SIM_TIME} 秒\n", "- 分析窗口数: {len(feature_df)}\n", "\n", "## 关键发现\n", "### 1. 电能质量影响\n", "- 最大THD变化: {impact_analysis.loc[impact_analysis.index.str.contains('thd', case=False), 'Relative_Impact_Percent'].abs().max():.2f}%\n", "- 功率损失: {abs(impact_analysis.loc[impact_analysis.index.str.contains('power', case=False), 'Absolute_Impact'].sum()/1000):.1f} kW\n", "- 电压偏差: {impact_analysis.loc[impact_analysis.index.str.contains('voltage', case=False), 'Relative_Impact_Percent'].abs().max():.2f}%\n", "\n", "### 2. MPC补偿建议\n", "- 优先补偿特征数: {len(mpc_results['compensation_requirements'])}\n", "- 谐波补偿需求: {len(mpc_results['control_suggestions']['harmonic_filtering'])} 个频次\n", "- 无功补偿需求: {mpc_results['control_suggestions']['reactive_power_compensation']:.2f}\n", "\n", "### 3. 控制策略建议\n", "1. 实施主动谐波补偿，重点关注3次、5次谐波\n", "2. 调整无功功率输出以维持电压稳定\n", "3. 优化功率因数以减少电网冲击\n", "4. 建议MPC预测时域: {config.PREDICTION_HORIZON} 步\n", "5. 建议控制时域: {config.CONTROL_HORIZON} 步\n", "\n", "## 结论\n", "PV Array4断路故障对电网电能质量产生显著影响，通过本分析提取的特征\n", "可为MPC补偿算法提供有效的输入，实现故障的主动补偿和电网支撑。\n", "\"\"\"\n", "    \n", "    with open('analysis_report.md', 'w', encoding='utf-8') as f:\n", "        f.write(report)\n", "    \n", "    print(f\"   ✅ 分析报告已生成: analysis_report.md\")\n", "\n", "# 执行分析\n", "if __name__ == \"__main__\":\n", "    results = main_analysis()"]}, {"cell_type": "markdown", "id": "usage_instructions", "metadata": {}, "source": ["## 使用说明\n", "\n", "### 快速开始\n", "1. **确保数据文件存在**: 将您的 `pv_fault_data.mat` 文件放在当前目录下\n", "2. **运行所有单元格**: 点击 \"Run All\" 或逐个执行单元格\n", "3. **查看结果**: 分析完成后会生成多个图表和CSV文件\n", "\n", "### 输出文件说明\n", "- `pv_fault_features.csv`: 完整的特征数据集\n", "- `fault_impact_analysis.csv`: 故障影响分析结果\n", "- `mpc_features.csv`: MPC算法专用特征数据\n", "- `compensation_requirements.csv`: 补偿需求分析\n", "- `analysis_report.md`: 完整的分析报告\n", "- 各种PNG格式的分析图表\n", "\n", "### 自定义配置\n", "如需修改分析参数，请编辑 `PowerQualityConfig` 类中的相应参数：\n", "- 调整窗口大小和重叠率\n", "- 修改谐波分析频次\n", "- 设置MPC时域参数\n", "- 更改电能质量标准限值\n", "\n", "### 故障排除\n", "- 如果遇到导入错误，请安装缺失的Python包\n", "- 如果图表显示异常，请检查中文字体设置\n", "- 如果内存不足，可以减少窗口数量或特征维度"]}, {"cell_type": "code", "execution_count": 9, "id": "quick_test_cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 环境验证...\n", "✅ 数据文件 pv_fault_data.mat 存在\n", "✅ SciPy.stats 可用\n", "\n", "🚀 环境验证完成，可以开始分析！\n", "💡 提示：运行 main_analysis() 开始完整分析\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 快速测试和验证\n", "import os\n", "\n", "def quick_validation():\n", "    \"\"\"快速验证环境和数据文件\"\"\"\n", "    print(\"🔍 环境验证...\")\n", "    \n", "    # 检查数据文件\n", "    if os.path.exists('pv_fault_data.mat'):\n", "        print(\"✅ 数据文件 pv_fault_data.mat 存在\")\n", "    else:\n", "        print(\"❌ 数据文件 pv_fault_data.mat 不存在，请确保文件在当前目录下\")\n", "        return False\n", "    \n", "    # 检查必要的库\n", "    try:\n", "        import scipy.stats\n", "        print(\"✅ SciPy.stats 可用\")\n", "    except ImportError:\n", "        print(\"❌ SciPy.stats 不可用，某些统计特征可能无法计算\")\n", "    \n", "    print(\"\\n🚀 环境验证完成，可以开始分析！\")\n", "    print(\"💡 提示：运行 main_analysis() 开始完整分析\")\n", "    return True\n", "\n", "# 运行验证\n", "quick_validation()"]}], "metadata": {"kernelspec": {"display_name": "matlab", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}