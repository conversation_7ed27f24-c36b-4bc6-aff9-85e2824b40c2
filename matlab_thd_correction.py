# MATLAB标准THD计算修正
# 参考: https://ww2.mathworks.cn/help/releases/R2024b/sps/powersys/ref/thd.html

import numpy as np
import scipy.io
import pandas as pd
import matplotlib.pyplot as plt

def extract_matlab_standard_thd(signal_data, fs, grid_freq=50):
    """
    按照MATLAB标准计算THD
    参考MATLAB官方文档的THD计算方法
    """
    N = len(signal_data)
    
    # 确保信号长度为偶数
    if N % 2 != 0:
        signal_data = signal_data[:-1]
        N = len(signal_data)
    
    # 按照MATLAB标准，不使用窗函数进行THD计算
    # 因为THD计算需要保持信号的真实幅值关系
    
    # FFT分析 - 使用原始信号
    yf = np.fft.fft(signal_data)
    yf_mag = np.abs(yf[:N//2]) * 2.0 / N  # 单边谱幅值
    yf_mag[0] = yf_mag[0] / 2  # 直流分量不需要乘2
    freqs = np.fft.fftfreq(N, 1/fs)[:N//2]
    
    # 频率分辨率
    freq_resolution = fs / N
    
    # 基波检测 - 按照MATLAB标准
    # 在50Hz附近寻找最大峰值
    search_range = max(3, int(5 / freq_resolution))  # ±5Hz搜索范围
    
    fundamental_center_idx = np.argmin(np.abs(freqs - grid_freq))
    start_idx = max(1, fundamental_center_idx - search_range)  # 避免直流分量
    end_idx = min(len(yf_mag), fundamental_center_idx + search_range)
    
    # 在搜索范围内找到最大峰值作为基波
    if end_idx > start_idx:
        local_peak_idx = np.argmax(yf_mag[start_idx:end_idx])
        fundamental_idx = start_idx + local_peak_idx
    else:
        fundamental_idx = fundamental_center_idx
    
    # 基波特征
    fundamental_mag = yf_mag[fundamental_idx]
    fundamental_freq = freqs[fundamental_idx]
    
    print(f"基波检测结果:")
    print(f"  - 基波频率: {fundamental_freq:.2f} Hz")
    print(f"  - 基波幅值: {fundamental_mag:.4f}")
    print(f"  - 基波索引: {fundamental_idx}")
    
    # 谐波检测 - 按照MATLAB THD标准
    # MATLAB THD计算通常考虑2-50次谐波
    harmonics_power_sum = 0
    harmonic_details = {}
    
    # 扩展谐波范围，按照MATLAB标准
    max_harmonic = min(50, int((fs/2) / fundamental_freq))  # 最大到50次或奈奎斯特频率
    
    print(f"\n谐波检测结果 (最大{max_harmonic}次):")
    
    for h in range(2, max_harmonic + 1):  # 从2次谐波开始
        harmonic_freq = h * fundamental_freq
        
        # 确保谐波频率在有效范围内
        if harmonic_freq >= fs/2:
            break
            
        # 找到最接近的频率点
        harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))
        
        # 谐波峰值检测 - 在±0.5Hz范围内搜索
        h_search_range = max(1, int(0.5 / freq_resolution))
        h_start_idx = max(0, harmonic_idx - h_search_range)
        h_end_idx = min(len(yf_mag), harmonic_idx + h_search_range + 1)
        
        if h_end_idx > h_start_idx:
            # 在搜索范围内找到最大值
            local_h_peak_idx = np.argmax(yf_mag[h_start_idx:h_end_idx])
            actual_harmonic_idx = h_start_idx + local_h_peak_idx
            harmonic_mag = yf_mag[actual_harmonic_idx]
            
            # 存储主要谐波信息
            if h <= 19:  # 只显示前19次谐波
                harmonic_details[h] = {
                    'magnitude': harmonic_mag,
                    'frequency': freqs[actual_harmonic_idx],
                    'ratio_percent': (harmonic_mag / fundamental_mag) * 100
                }
                
                if h <= 7:  # 显示前7次谐波的详细信息
                    print(f"  - {h}次谐波: {harmonic_mag:.4f} ({harmonic_details[h]['ratio_percent']:.2f}%)")
            
            # 累加所有谐波功率（用于THD计算）
            harmonics_power_sum += harmonic_mag**2
    
    # THD计算 - 严格按照MATLAB标准
    # THD = sqrt(sum(H2^2 + H3^2 + ... + Hn^2)) / H1 * 100%
    if fundamental_mag > 1e-12:  # 避免除零错误
        # MATLAB THD公式
        thd = np.sqrt(harmonics_power_sum) / fundamental_mag
        thd_percent = thd * 100  # 百分比形式
        
        print(f"\nTHD计算结果:")
        print(f"  - 基波功率: {fundamental_mag**2:.6f}")
        print(f"  - 谐波总功率: {harmonics_power_sum:.6f}")
        print(f"  - THD: {thd:.4f} ({thd_percent:.2f}%)")
        
        # 计算奇偶谐波分量
        odd_harmonics_power = 0
        even_harmonics_power = 0
        
        for h in range(2, max_harmonic + 1):
            harmonic_freq = h * fundamental_freq
            if harmonic_freq >= fs/2:
                break
            
            harmonic_idx = np.argmin(np.abs(freqs - harmonic_freq))
            h_search_range = max(1, int(0.5 / freq_resolution))
            h_start_idx = max(0, harmonic_idx - h_search_range)
            h_end_idx = min(len(yf_mag), harmonic_idx + h_search_range + 1)
            
            if h_end_idx > h_start_idx:
                local_h_peak_idx = np.argmax(yf_mag[h_start_idx:h_end_idx])
                actual_harmonic_idx = h_start_idx + local_h_peak_idx
                harmonic_mag = yf_mag[actual_harmonic_idx]
                
                if h % 2 == 0:  # 偶次谐波
                    even_harmonics_power += harmonic_mag**2
                else:  # 奇次谐波
                    odd_harmonics_power += harmonic_mag**2
        
        odd_thd = np.sqrt(odd_harmonics_power) / fundamental_mag * 100
        even_thd = np.sqrt(even_harmonics_power) / fundamental_mag * 100
        
        print(f"  - 奇次谐波THD: {odd_thd:.2f}%")
        print(f"  - 偶次谐波THD: {even_thd:.2f}%")
        
        return {
            'thd': thd,
            'thd_percent': thd_percent,
            'fundamental_mag': fundamental_mag,
            'fundamental_freq': fundamental_freq,
            'harmonics_power': harmonics_power_sum,
            'harmonic_details': harmonic_details,
            'odd_thd_percent': odd_thd,
            'even_thd_percent': even_thd,
            'frequency_spectrum': (freqs, yf_mag)
        }
    else:
        print("基波幅值过小，无法计算THD")
        return None

def plot_thd_analysis(thd_result, title="THD分析"):
    """绘制THD分析图"""
    if thd_result is None:
        return
    
    freqs, yf_mag = thd_result['frequency_spectrum']
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(title, fontsize=16, fontweight='bold')
    
    # 子图1: 频谱图
    axes[0, 0].plot(freqs[:500], yf_mag[:500], 'b-', linewidth=1)
    axes[0, 0].axvline(thd_result['fundamental_freq'], color='red', linestyle='--', 
                      linewidth=2, label=f"基波 {thd_result['fundamental_freq']:.1f}Hz")
    
    # 标记主要谐波
    for h, details in thd_result['harmonic_details'].items():
        if h <= 7:  # 只标记前7次谐波
            axes[0, 0].axvline(details['frequency'], color='orange', linestyle=':', 
                              alpha=0.7, label=f"{h}次谐波")
    
    axes[0, 0].set_title('频谱分析')
    axes[0, 0].set_xlabel('频率 (Hz)')
    axes[0, 0].set_ylabel('幅值')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 子图2: 谐波柱状图
    harmonics = list(thd_result['harmonic_details'].keys())
    harmonic_ratios = [thd_result['harmonic_details'][h]['ratio_percent'] for h in harmonics]
    
    bars = axes[0, 1].bar(harmonics, harmonic_ratios, color='skyblue', alpha=0.7)
    axes[0, 1].set_title('谐波含量分布')
    axes[0, 1].set_xlabel('谐波次数')
    axes[0, 1].set_ylabel('相对基波百分比 (%)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 标注数值
    for bar, ratio in zip(bars, harmonic_ratios):
        if ratio > 0.1:  # 只标注大于0.1%的谐波
            axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{ratio:.2f}%', ha='center', va='bottom', fontsize=8)
    
    # 子图3: THD信息
    thd_info = f"""
THD计算结果:
• 总THD: {thd_result['thd_percent']:.2f}%
• 奇次谐波THD: {thd_result['odd_thd_percent']:.2f}%
• 偶次谐波THD: {thd_result['even_thd_percent']:.2f}%

基波信息:
• 频率: {thd_result['fundamental_freq']:.2f} Hz
• 幅值: {thd_result['fundamental_mag']:.4f}

主要谐波:
"""
    
    for h in sorted(thd_result['harmonic_details'].keys())[:5]:
        details = thd_result['harmonic_details'][h]
        thd_info += f"• {h}次: {details['ratio_percent']:.2f}%\n"
    
    axes[1, 0].text(0.05, 0.95, thd_info, transform=axes[1, 0].transAxes,
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    axes[1, 0].set_xlim(0, 1)
    axes[1, 0].set_ylim(0, 1)
    axes[1, 0].set_title('THD分析结果')
    axes[1, 0].axis('off')
    
    # 子图4: 功率分布饼图
    power_data = [
        thd_result['fundamental_mag']**2,
        thd_result['harmonics_power']
    ]
    power_labels = ['基波功率', '谐波功率']
    colors = ['lightgreen', 'lightcoral']
    
    axes[1, 1].pie(power_data, labels=power_labels, colors=colors, autopct='%1.1f%%', startangle=90)
    axes[1, 1].set_title('功率分布')
    
    plt.tight_layout()
    plt.show()

# 测试函数
def test_matlab_thd():
    """测试MATLAB标准THD计算"""
    print("🔍 测试MATLAB标准THD计算...")
    
    # 生成测试信号：基波 + 3次谐波 + 5次谐波
    fs = 10000  # 采样频率
    t = np.linspace(0, 0.1, int(fs * 0.1))  # 0.1秒
    
    # 基波50Hz，幅值1
    fundamental = 1.0 * np.sin(2 * np.pi * 50 * t)
    # 3次谐波，幅值0.1
    harmonic_3 = 0.1 * np.sin(2 * np.pi * 150 * t)
    # 5次谐波，幅值0.05
    harmonic_5 = 0.05 * np.sin(2 * np.pi * 250 * t)
    
    # 合成信号
    test_signal = fundamental + harmonic_3 + harmonic_5
    
    # 计算THD
    result = extract_matlab_standard_thd(test_signal, fs)
    
    if result:
        print(f"\n✅ 测试完成！")
        print(f"理论THD: {np.sqrt(0.1**2 + 0.05**2) / 1.0 * 100:.2f}%")
        print(f"计算THD: {result['thd_percent']:.2f}%")
        
        # 绘制分析图
        plot_thd_analysis(result, "MATLAB标准THD计算测试")
    
    return result

if __name__ == "__main__":
    test_matlab_thd()
